// Script para probar el endpoint /auth/me
// Ejecutar en la consola del navegador después de iniciar sesión

async function testAuthEndpoint() {
    console.log('=== PROBANDO ENDPOINT /auth/me ===');
    
    // Obtener token de Keycloak
    const keycloakService = ng.getComponent(document.querySelector('app-root')).injector.get('KeycloakService');
    const token = keycloakService.getKeycloakInstance().token;
    
    if (!token) {
        console.error('❌ No hay token disponible. ¿Estás logueado?');
        return;
    }
    
    console.log('✅ Token obtenido:', token.substring(0, 50) + '...');
    
    try {
        // Llamar al endpoint
        const response = await fetch('http://localhost:8080/api/auth/me', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        console.log('📡 Response status:', response.status);
        console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));
        
        if (response.ok) {
            const userData = await response.json();
            console.log('✅ Usuario obtenido:', userData);
            
            // Verificar campos específicos
            console.log('📋 Campos del usuario:');
            console.log('  - ID:', userData.id);
            console.log('  - Username:', userData.username);
            console.log('  - Nombre:', userData.nombre);
            console.log('  - Apellido1:', userData.apellido1);
            console.log('  - Apellido2:', userData.apellido2);
            console.log('  - Nombre completo:', userData.nombre_completo);
            console.log('  - Email:', userData.email);
            console.log('  - Rol:', userData.rol);
            console.log('  - Estado:', userData.estado);
            
        } else {
            const errorText = await response.text();
            console.error('❌ Error en la respuesta:', response.status, errorText);
        }
        
    } catch (error) {
        console.error('❌ Error en la llamada:', error);
    }
}

// Ejecutar la prueba
testAuthEndpoint();
