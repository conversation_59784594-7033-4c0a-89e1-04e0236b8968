#!/bin/bash

# Script para probar la importación CSV
# Requiere un token JWT válido de Keycloak

# URL del backend
BASE_URL="http://localhost:8080"

# Token JWT (debes obtenerlo de Keycloak)
TOKEN="${1:-TU_TOKEN_AQUI}"

if [ "$TOKEN" = "TU_TOKEN_AQUI" ]; then
    echo "❌ Error: Debes proporcionar un token JWT válido"
    echo "Uso: $0 <TOKEN_JWT>"
    echo ""
    echo "Para obtener un token:"
    echo "1. Ve a http://localhost:8080/swagger-ui/index.html"
    echo "2. Haz clic en 'Authorize'"
    echo "3. Obtén el token desde la consola del navegador con:"
    echo "   ng.getComponent(document.querySelector('app-login')).keycloakService.getKeycloakInstance().token"
    exit 1
fi

echo "🚀 Probando importación CSV..."
echo "📍 URL: $BASE_URL/candidaturas/import"
echo "🔑 Token: ${TOKEN:0:50}..."
echo "📄 Archivo: test-candidatura.csv"
echo ""

# Importar CSV con formación política ID = 1 (PSOE)
curl -X POST "$BASE_URL/candidaturas/import" \
  -H "Authorization: Bearer $TOKEN" \
  -F "archivo=@test-candidatura.csv" \
  -F "formacionPoliticaId=1" \
  -w "\n📊 Status: %{http_code}\n⏱️  Tiempo: %{time_total}s\n" \
  -v

echo ""
echo "✅ Importación completada!"
echo ""
echo "🔍 Revisa los logs del backend para ver el detalle del procesamiento"
echo "📋 Para verificar los datos creados:"
echo "   GET $BASE_URL/candidaturas"
echo "   GET $BASE_URL/candidatos"
