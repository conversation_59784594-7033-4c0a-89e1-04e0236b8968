package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.CandidatoEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.TipoCandidatoEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.EstadoCandidatoEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service.CandidatoService;

@RestController
@RequestMapping("/api/candidatos")
@CrossOrigin(origins = { "http://localhost:4200", "https://localhost:4200" })
public class CandidatoController {

    @Autowired
    private CandidatoService candidatoService;

    @GetMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<?> getAll(
            @RequestParam(value = "page", required = false) Integer page,
            @RequestParam(value = "size", required = false) Integer size,
            @RequestParam(value = "sort", required = false) String sort) {

        // Si no se especifican parámetros de paginación, devolver todos
        if (page == null && size == null) {
            List<CandidatoEntity> candidatos = candidatoService.findAll();
            return ResponseEntity.ok(candidatos);
        }

        // Si se especifican parámetros, usar paginación
        Pageable pageable = PageRequest.of(
            page != null ? page : 0,
            size != null ? size : 20
        );

        Page<CandidatoEntity> candidatosPaginados = candidatoService.findAll(pageable);
        return ResponseEntity.ok(candidatosPaginados);
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<CandidatoEntity> getById(@PathVariable Long id) {
        return candidatoService.findById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<CandidatoEntity> create(@RequestBody CandidatoEntity candidato) {
        CandidatoEntity saved = candidatoService.save(candidato);
        return ResponseEntity.status(201).body(saved);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<CandidatoEntity> update(@PathVariable Long id, @RequestBody CandidatoEntity candidato) {
        candidato.setId(id);
        CandidatoEntity updated = candidatoService.save(candidato);
        return ResponseEntity.ok(updated);
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        candidatoService.deleteById(id);
        return ResponseEntity.noContent().build();
    }

    // Endpoints legacy eliminados según Script 2 (activo y circunscripcion no existen)

    /**
     * Obtiene candidatos por candidatura ordenados por orden
     */
    @GetMapping("/candidatura/{candidaturaId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<List<CandidatoEntity>> getByCandidatura(@PathVariable Long candidaturaId) {
        return ResponseEntity.ok(candidatoService.findByCandidatura(candidaturaId));
    }

    /**
     * Busca candidatos con filtros y paginación
     */
    @GetMapping("/buscar")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<Page<CandidatoEntity>> buscarConFiltros(
            @RequestParam(required = false) Long candidaturaId,
            @RequestParam(required = false) Long tipoId,
            @RequestParam(required = false) Long estadoId,
            @RequestParam(required = false) String nombre,
            @RequestParam(required = false) String apellido1,
            Pageable pageable) {
        Page<CandidatoEntity> candidatos = candidatoService.findWithFilters(
            candidaturaId, tipoId, estadoId, nombre, apellido1, pageable);
        return ResponseEntity.ok(candidatos);
    }

    /**
     * Busca candidatos por texto en nombre o apellidos
     */
    @GetMapping("/buscar-texto")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<List<CandidatoEntity>> buscarPorTexto(@RequestParam String texto) {
        return ResponseEntity.ok(candidatoService.findByTextoContaining(texto));
    }

    /**
     * Cuenta candidatos por candidatura y tipo
     */
    @GetMapping("/contar")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<Long> contarPorCandidaturaYTipo(
            @RequestParam Long candidaturaId,
            @RequestParam Long tipoId) {
        Long count = candidatoService.countByCandidaturaAndTipo(candidaturaId, tipoId);
        return ResponseEntity.ok(count);
    }

    /**
     * Valida un candidato
     */
    @PostMapping("/{id}/validar")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<CandidatoEntity> validarCandidato(
            @PathVariable Long id,
            @RequestParam String comentario,
            @AuthenticationPrincipal Jwt jwt) {
        try {
            String usuario = jwt.getClaimAsString("preferred_username");
            CandidatoEntity candidato = candidatoService.validarCandidato(id, usuario, comentario);
            return ResponseEntity.ok(candidato);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

}
