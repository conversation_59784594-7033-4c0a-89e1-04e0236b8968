package es.parlamentodeandalucia.datoscandidatos.dto;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import org.hibernate.validator.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * FormacionPoliticaInput
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2025-08-19T10:42:58.859075800+02:00[Europe/Madrid]")
public class FormacionPoliticaInput {

  private String nombre;

  private String siglas;

  private String codigoInterno;

  private Boolean activa;

  public FormacionPoliticaInput() {
    super();
  }

  /**
   * Constructor with only required parameters
   */
  public FormacionPoliticaInput(String nombre) {
    this.nombre = nombre;
  }

  public FormacionPoliticaInput nombre(String nombre) {
    this.nombre = nombre;
    return this;
  }

  /**
   * Get nombre
   * @return nombre
  */
  @NotNull 
  @Schema(name = "nombre", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("nombre")
  public String getNombre() {
    return nombre;
  }

  public void setNombre(String nombre) {
    this.nombre = nombre;
  }

  public FormacionPoliticaInput siglas(String siglas) {
    this.siglas = siglas;
    return this;
  }

  /**
   * Get siglas
   * @return siglas
  */
  
  @Schema(name = "siglas", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("siglas")
  public String getSiglas() {
    return siglas;
  }

  public void setSiglas(String siglas) {
    this.siglas = siglas;
  }

  public FormacionPoliticaInput codigoInterno(String codigoInterno) {
    this.codigoInterno = codigoInterno;
    return this;
  }

  /**
   * Get codigoInterno
   * @return codigoInterno
  */
  
  @Schema(name = "codigoInterno", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("codigoInterno")
  public String getCodigoInterno() {
    return codigoInterno;
  }

  public void setCodigoInterno(String codigoInterno) {
    this.codigoInterno = codigoInterno;
  }

  public FormacionPoliticaInput activa(Boolean activa) {
    this.activa = activa;
    return this;
  }

  /**
   * Get activa
   * @return activa
  */
  
  @Schema(name = "activa", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("activa")
  public Boolean getActiva() {
    return activa;
  }

  public void setActiva(Boolean activa) {
    this.activa = activa;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    FormacionPoliticaInput formacionPoliticaInput = (FormacionPoliticaInput) o;
    return Objects.equals(this.nombre, formacionPoliticaInput.nombre) &&
        Objects.equals(this.siglas, formacionPoliticaInput.siglas) &&
        Objects.equals(this.codigoInterno, formacionPoliticaInput.codigoInterno) &&
        Objects.equals(this.activa, formacionPoliticaInput.activa);
  }

  @Override
  public int hashCode() {
    return Objects.hash(nombre, siglas, codigoInterno, activa);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class FormacionPoliticaInput {\n");
    sb.append("    nombre: ").append(toIndentedString(nombre)).append("\n");
    sb.append("    siglas: ").append(toIndentedString(siglas)).append("\n");
    sb.append("    codigoInterno: ").append(toIndentedString(codigoInterno)).append("\n");
    sb.append("    activa: ").append(toIndentedString(activa)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

