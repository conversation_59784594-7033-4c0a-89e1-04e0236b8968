package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.controller;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.EstadoCandidaturaEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service.EstadoCandidaturaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/estados-candidatura")
@CrossOrigin(origins = { "http://localhost:4200", "https://localhost:4200" })
public class EstadoCandidaturaController {

    @Autowired
    private EstadoCandidaturaService estadoCandidaturaService;

    /**
     * Obtiene todos los estados de candidatura
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<List<EstadoCandidaturaEntity>> getAll() {
        return ResponseEntity.ok(estadoCandidaturaService.findAll());
    }

    /**
     * Obtiene un estado de candidatura por ID
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<EstadoCandidaturaEntity> getById(@PathVariable Long id) {
        return estadoCandidaturaService.findById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Obtiene un estado de candidatura por valor
     */
    @GetMapping("/valor/{valor}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<EstadoCandidaturaEntity> getByValor(@PathVariable String valor) {
        return estadoCandidaturaService.findByValor(valor)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Busca estados de candidatura por texto
     */
    @GetMapping("/buscar")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<List<EstadoCandidaturaEntity>> buscarPorTexto(@RequestParam String texto) {
        return ResponseEntity.ok(estadoCandidaturaService.findByTextoContaining(texto));
    }

    /**
     * Crea un nuevo estado de candidatura
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<EstadoCandidaturaEntity> create(@RequestBody EstadoCandidaturaEntity estadoCandidatura) {
        try {
            EstadoCandidaturaEntity saved = estadoCandidaturaService.save(estadoCandidatura);
            return ResponseEntity.ok(saved);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Actualiza un estado de candidatura
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<EstadoCandidaturaEntity> update(@PathVariable Long id, 
                                                         @RequestBody EstadoCandidaturaEntity estadoCandidatura) {
        return estadoCandidaturaService.findById(id)
                .map(existing -> {
                    existing.setValor(estadoCandidatura.getValor());
                    return ResponseEntity.ok(estadoCandidaturaService.save(existing));
                })
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Elimina un estado de candidatura
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        if (estadoCandidaturaService.findById(id).isPresent()) {
            estadoCandidaturaService.deleteById(id);
            return ResponseEntity.ok().build();
        }
        return ResponseEntity.notFound().build();
    }
}
