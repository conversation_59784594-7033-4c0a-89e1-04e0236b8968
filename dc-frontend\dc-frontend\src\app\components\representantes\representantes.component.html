<div class="layout-container">
  <app-header class="header-fixed"></app-header>

  <div class="page-wrapper scrollable-content">
    <!-- Breadcrumbs -->
    <div class="breadcrumbs">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="16px" height="16px"
        style="vertical-align: middle; margin-right: 3px;">
        <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z" />
      </svg>
      <span class="separator">&gt;</span>
      <span>Administración</span>
      <span class="separator">&gt;</span>
      <strong>Representantes</strong>
    </div>

    <!-- Título -->
    <div class="title-section">
      <a routerLink="/home" class="back-button">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="24px" height="24px">
          <path d="M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z" transform="rotate(180 12 12)" />
        </svg>
      </a>
      <h1 class="page-main-title">Representantes</h1>
    </div>

    <!-- Contenedor tabla -->
    <div class="usuarios-page-container">
      <!-- Filtros alineados a la derecha -->
      <div class="filter-actions-bar justify-end">
        <button class="btn-text" (click)="limpiarFiltros()">
          <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24" width="16px" height="16px">
            <path
              d="M6 13c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm14-10v2h-3.21l-3.42 8H18v2h-7.15c-.78 1.76-2.58 3-4.85 3-2.21 0-4-1.79-4-4s1.79-4 4-4c.78 0 1.5.22 2.15.62L12.58 4H20V2h2v2h-2z" />
          </svg>
          Limpiar filtros
        </button>
      </div>

      <!-- Tabla -->
      <div class="table-container">
        <table>
          <thead>
            <tr>
              <th>Nombre</th>
              <th>Formación</th>
              <th>Candidatos asignados</th>
              <th>Última modificación</th>
              <th>Modificado por</th>
              <th style="width: 150px;"></th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let representante of representantes">
              <td>{{ representante.nombre }}</td>
              <td>{{ representante.formacion }}</td>
              <td>{{ representante.candidatosAsignados }}</td>
              <td>{{ representante.ultimaModificacion }}</td>
              <td>{{ representante.modificadoPor }}</td>
              <td>
                <button class="btn-text" (click)="abrirModal()">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" width="16" height="16"
                    viewBox="0 0 24 24">
                    <path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z" />
                  </svg>
                  Asignar candidatos
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Paginación -->
      <div class="pagination">
        <button class="pagination-arrow">&laquo;</button>
        <button class="pagination-arrow">&lsaquo;</button>
        <button class="pagination-number active">1</button>
        <button class="pagination-number">2</button>
        <button class="pagination-number">3</button>
        <span class="pagination-ellipsis">...</span>
        <button class="pagination-number">5</button>
        <button class="pagination-arrow">&rsaquo;</button>
        <button class="pagination-arrow">&raquo;</button>
      </div>
    </div>
  </div>

  <app-footer class="footer-fixed"></app-footer>
</div>

<!-- Overlay -->
<div class="overlay" [class.show]="showSidebar" (click)="cerrarSidebar()"></div>

<!-- Sidebar -->
<div class="sidebar" [class.show]="showSidebar">
  <!-- Header -->
  <div class="sidebar-header">
    <h2>Asignar candidatos</h2>
    <button class="btn-icon" (click)="cerrarSidebar()">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" stroke="currentColor" viewBox="0 0 24 24" width="20" height="20">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
      </svg>
    </button>
  </div>

  <!-- Contenido -->
  <div class="sidebar-content">
    <div class="form-group">
      <label>Formación política</label>
      <input type="text" class="form-control" value="G. P. Socialista" readonly />
    </div>

    <div class="form-group">
      <label>Circunscripción</label>
      <select class="form-control">
        <option value="todas">Todas</option>
        <option value="sevilla">Sevilla</option>
        <option value="cadiz">Cádiz</option>
        <!-- añade más si quieres -->
      </select>
    </div>

    <div class="form-group">
      <label style="display: flex; align-items: center; justify-content: space-between;">
        <span>Asignar candidatos</span>
        <input type="checkbox" [(ngModel)]="selectAll" (change)="toggleSelectAll()" />
      </label>
    </div>

    <div class="checkbox-list">
      <div class="checkbox-item" *ngFor="let candidato of candidatos">
        <input type="checkbox"
               [checked]="selectedCandidatos.includes(candidato)"
               (change)="toggleCheckbox(candidato)" />
        {{ candidato }}
      </div>
    </div>
  </div>

  <!-- Footer -->
  <div class="sidebar-footer">
    <button class="btn-secondary" (click)="cerrarSidebar()">Cancelar</button>
    <button class="btn-primary">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" stroke="currentColor" viewBox="0 0 24 24" width="18" height="18">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
      </svg>
      Guardar
    </button>
  </div>
</div>
