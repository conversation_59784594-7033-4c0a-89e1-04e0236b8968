spring.application.name=sgi-negocio-backend

# Configuración del servidor
server.port=8094
server.servlet.context-path=/

# Configuración de base de datos SGI
spring.datasource.url=**************************************************
spring.datasource.username=sgi_user
spring.datasource.password=sgi_password
spring.datasource.driver-class-name=org.postgresql.Driver

# Configuración JPA/Hibernate
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.use_sql_comments=true

# Configuración de logging
logging.level.es.parlamentodeandalucia.sgi=INFO
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
logging.level.org.springframework.security=INFO

# Configuración de seguridad (básica para desarrollo)
spring.security.user.name=admin
spring.security.user.password=admin123
spring.security.user.roles=ADMIN
