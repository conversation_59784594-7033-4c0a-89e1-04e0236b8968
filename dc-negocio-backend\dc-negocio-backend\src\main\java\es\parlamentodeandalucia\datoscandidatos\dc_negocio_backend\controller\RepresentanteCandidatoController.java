package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.controller;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.RepresentanteCandidatoEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service.RepresentanteCandidatoService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/representantes-candidatos")
@CrossOrigin(origins = {"http://localhost:4200", "https://localhost:4200"})
public class RepresentanteCandidatoController {

    private final RepresentanteCandidatoService service;

    public RepresentanteCandidatoController(RepresentanteCandidatoService service) {
        this.service = service;
    }

    @GetMapping
    public ResponseEntity<List<RepresentanteCandidatoEntity>> list() {
        return ResponseEntity.ok(service.findAll());
    }

    @GetMapping("/{id}")
    public ResponseEntity<RepresentanteCandidatoEntity> getOne(@PathVariable Long id) {
        return service.findById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @PostMapping
    public ResponseEntity<RepresentanteCandidatoEntity> create(@RequestBody RepresentanteCandidatoEntity rc) {
        RepresentanteCandidatoEntity saved = service.create(rc);
        return ResponseEntity.status(201).body(saved);
    }

    @PutMapping("/{id}")
    public ResponseEntity<RepresentanteCandidatoEntity> update(
            @PathVariable Long id,
            @RequestBody RepresentanteCandidatoEntity rc) {
        if (!service.findById(id).isPresent()) {
            return ResponseEntity.notFound().build();
        }
        return ResponseEntity.ok(service.update(id, rc));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        if (!service.findById(id).isPresent()) {
            return ResponseEntity.notFound().build();
        }
        service.delete(id);
        return ResponseEntity.noContent().build();
    }
}
