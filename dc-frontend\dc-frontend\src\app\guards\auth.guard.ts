import { inject } from '@angular/core';
import { CanActivateFn, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { KeycloakService } from 'keycloak-angular';

export const authInitGuard: CanActivateFn = async (route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => {
  const keycloak = inject(KeycloakService);
  const router = inject(Router);

  try {
    // 1. Comprueba si el usuario ya está autenticado.
    const authenticated = await keycloak.isLoggedIn();

    // 2. Si el usuario ya está autenticado, permite el acceso a la ruta.
    if (authenticated) {
      return true;
    }

    // 3. Si el usuario no está autenticado, inicia el proceso de login de Keycloak.
    // La clave es la propiedad `redirectUri` aquí.
    await keycloak.login({
      redirectUri: window.location.origin + state.url
    });

    return false;

  } catch (error) {
    // Si hay algún error durante la comprobación de la sesión Keycloak,
    // registra el error y redirige al usuario a la raíz para evitar que se quede atascado.
    console.error('Error en authInitGuard:', error);
    router.navigate(['/']);
    return false;
  }
}