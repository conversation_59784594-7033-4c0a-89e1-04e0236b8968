package es.parlamentodeandalucia.sgi.keycloak.spi.entity;

import jakarta.persistence.*;

/**
 * Entidad para tipos de identificadores del SGI
 * Mapea con la tabla sgi_t_tipo_identificador
 */
@Entity
@Table(name = "sgi_t_tipo_identificador")
public class SgiTipoIdentificadorEntity {

    @Id
    @Column(name = "sgi_id_tipo_identificador")
    private Long id;

    @Column(name = "sgi_tx_valor", unique = true)
    private String valor;

    // Constructores
    public SgiTipoIdentificadorEntity() {}

    public SgiTipoIdentificadorEntity(Long id, String valor) {
        this.id = id;
        this.valor = valor;
    }

    // Getters y Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getValor() { return valor; }
    public void setValor(String valor) { this.valor = valor; }
}
