services:

  keycloak:
    container_name: keycloak
    environment:
      - KEYCLOAK_ADMIN=admin
      - KEYCLOAK_ADMIN_PASSWORD=admin
    ports:
      - '8080:8080'
      - '9000:9000'
    build:
      context: .
      dockerfile: Dockerfile
    depends_on:
      - keycloak-db
      - external-db

  keycloak-db:
    container_name: keycloak-db
    image: postgres:16-alpine
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=postgres
    volumes:
      - ./scripts/keycloak-db/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
      - ./postgresql.conf:/etc/postgresql/postgresql.conf
    command: [ "postgres", "-c", "config_file=/etc/postgresql/postgresql.conf" ]
    ports:
      - "5455:5432"

  external-db:
    container_name: external-db
    image: postgres:16-alpine
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=postgres
    volumes:
      - ./scripts/external-db/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
      - ./postgresql.conf:/etc/postgresql/postgresql.conf
    command: [ "postgres", "-c", "config_file=/etc/postgresql/postgresql.conf" ]
    ports:
      - "5456:5432"
