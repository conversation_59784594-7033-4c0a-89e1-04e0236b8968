package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service;

import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.PermisoEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository.PermisoRepository;

/**
 * Implementación del servicio para la gestión de permisos.
 */
@Service
@Transactional
public class PermisoService {

    private static final Logger logger = LoggerFactory.getLogger(PermisoService.class);

    @Autowired
    private final PermisoRepository permisoRepository;

    public PermisoService(PermisoRepository permisoRepository) {
        this.permisoRepository = permisoRepository;
    }

    /**
     * Obtiene todos los permisos.
     */
    @Transactional(readOnly = true)
    public List<PermisoEntity> findAll() {
        logger.info("Obteniendo todos los permisos");
        return permisoRepository.findAll();
    }

    /**
     * Busca un permiso por su ID.
     */
    @Transactional(readOnly = true)
    public Optional<PermisoEntity> findById(Long id) {
        logger.info("Buscando permiso con ID {}", id);
        return permisoRepository.findById(id);
    }

    /**
     * Guarda un nuevo permiso.
     */
    @Transactional
    public PermisoEntity save(PermisoEntity permiso) {
        logger.info("Guardando nuevo permiso: {}", permiso.getCodigo());
        return permisoRepository.save(permiso);
    }

    /**
     * Actualiza un permiso existente.
     */
    @Transactional
    public PermisoEntity updatePermiso(Long id, PermisoEntity permisoDetails) {
        logger.info("Actualizando permiso con ID {}", id);
        PermisoEntity permiso = permisoRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Permiso no encontrado con ID: " + id));

        permiso.setCodigo(permisoDetails.getCodigo());
        permiso.setDescripcion(permisoDetails.getDescripcion());
        permiso.setActivo(permisoDetails.getActivo());
        permiso.setFuncionalidad(permisoDetails.getFuncionalidad());

        return permisoRepository.save(permiso);
    }

    /**
     * Elimina un permiso por su ID.
     */
    @Transactional
    public void deleteById(Long id) {
        logger.info("Eliminando permiso con ID {}", id);
        PermisoEntity permiso = permisoRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Permiso no encontrado"));
        permisoRepository.delete(permiso);
    }

    /**
     * Activa o desactiva un permiso según el valor indicado.
     */
    @Transactional
    public PermisoEntity toggleActivo(Long id, boolean activo) {
        logger.info("{} permiso con ID {}", activo ? "Activando" : "Desactivando", id);
        PermisoEntity permiso = permisoRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Permiso no encontrado con ID: " + id));

        permiso.setActivo(activo);
        return permisoRepository.save(permiso);
    }

    /**
     * Verifica si existen permisos asociados a una funcionalidad.
     */
    @Transactional(readOnly = true)
    public boolean existsByFuncionalidadId(Long funcionalidadId) {
        logger.info("Verificando si existen permisos asociados a la funcionalidad con ID {}", funcionalidadId);
        return permisoRepository.existsByFuncionalidadId(funcionalidadId);
    }
}
