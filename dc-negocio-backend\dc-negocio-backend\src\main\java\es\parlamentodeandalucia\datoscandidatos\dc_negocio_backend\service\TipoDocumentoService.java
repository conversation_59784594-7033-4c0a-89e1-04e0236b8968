package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.TipoDocumentoEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository.TipoDocumentoRepository;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TipoDocumentoService {

    private final TipoDocumentoRepository repository;

    public TipoDocumentoService(TipoDocumentoRepository repository) {
        this.repository = repository;
    }

    public List<TipoDocumentoEntity> findAll() {
        return repository.findAll();
    }

    public TipoDocumentoEntity save(TipoDocumentoEntity entity) {
        return repository.save(entity);
    }

    public void deleteById(Long id) {
        repository.deleteById(id);
    }

    public TipoDocumentoEntity findById(Long id) {
        return repository.findById(id).orElse(null);
    }
}
