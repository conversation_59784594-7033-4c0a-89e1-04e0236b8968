import { Component, EventEmitter, Input, Output } from '@angular/core';
import { DialogModule } from 'primeng/dialog';
import { ButtonModule } from 'primeng/button';

@Component({
  selector: 'app-modal-reset',
  standalone: true,
  imports: [DialogModule, ButtonModule],
  templateUrl: './modal-reset.component.html',
  styleUrls: ['./modal-reset.component.scss']
})
export class ModalResetComponent {
  @Input() visible: boolean = false; // Controla la visibilidad del modal
  @Output() visibleChange = new EventEmitter<boolean>(); // Evento para manejar cambios en la visibilidad
  @Output() confirmDelete = new EventEmitter<void>(); // Evento para confirmar la acción

  close() {
    this.visible = false;
    this.visibleChange.emit(false); // Notifica al padre que el modal se cerró
  }

  confirmAction() {
    this.confirmDelete.emit(); // Emite el evento de confirmación
    this.close(); // Cierra el modal después de confirmar
  }
}