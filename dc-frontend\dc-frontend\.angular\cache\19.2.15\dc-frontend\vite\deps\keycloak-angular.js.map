{"version": 3, "sources": ["../../../../../../node_modules/keycloak-js/lib/keycloak.js", "../../../../../../node_modules/keycloak-angular/fesm2022/keycloak-angular.mjs"], "sourcesContent": ["/*\n * Copyright 2016 Red Hat, Inc. and/or its affiliates\n * and other contributors as indicated by the <AUTHOR>\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction Keycloak(config) {\n  if (!(this instanceof Keycloak)) {\n    throw new Error(\"The 'Keycloak' constructor must be invoked with 'new'.\");\n  }\n  if (typeof config !== 'string' && !isObject(config)) {\n    throw new Error(\"The 'Keycloak' constructor must be provided with a configuration object, or a URL to a JSON configuration file.\");\n  }\n  if (isObject(config)) {\n    const requiredProperties = 'oidcProvider' in config ? ['clientId'] : ['url', 'realm', 'clientId'];\n    for (const property of requiredProperties) {\n      if (!config[property]) {\n        throw new Error(`The configuration object is missing the required '${property}' property.`);\n      }\n    }\n  }\n  var kc = this;\n  var adapter;\n  var refreshQueue = [];\n  var callbackStorage;\n  var loginIframe = {\n    enable: true,\n    callbackList: [],\n    interval: 5\n  };\n  kc.didInitialize = false;\n  var useNonce = true;\n  var logInfo = createLogger(console.info);\n  var logWarn = createLogger(console.warn);\n  if (!globalThis.isSecureContext) {\n    logWarn(\"[KEYCLOAK] Keycloak JS must be used in a 'secure context' to function properly as it relies on browser APIs that are otherwise not available.\\n\" + \"Continuing to run your application insecurely will lead to unexpected behavior and breakage.\\n\\n\" + \"For more information see: https://developer.mozilla.org/en-US/docs/Web/Security/Secure_Contexts\");\n  }\n  kc.init = function (initOptions = {}) {\n    if (kc.didInitialize) {\n      throw new Error(\"A 'Keycloak' instance can only be initialized once.\");\n    }\n    kc.didInitialize = true;\n    kc.authenticated = false;\n    callbackStorage = createCallbackStorage();\n    var adapters = ['default', 'cordova', 'cordova-native'];\n    if (adapters.indexOf(initOptions.adapter) > -1) {\n      adapter = loadAdapter(initOptions.adapter);\n    } else if (typeof initOptions.adapter === \"object\") {\n      adapter = initOptions.adapter;\n    } else {\n      if (window.Cordova || window.cordova) {\n        adapter = loadAdapter('cordova');\n      } else {\n        adapter = loadAdapter();\n      }\n    }\n    if (typeof initOptions.useNonce !== 'undefined') {\n      useNonce = initOptions.useNonce;\n    }\n    if (typeof initOptions.checkLoginIframe !== 'undefined') {\n      loginIframe.enable = initOptions.checkLoginIframe;\n    }\n    if (initOptions.checkLoginIframeInterval) {\n      loginIframe.interval = initOptions.checkLoginIframeInterval;\n    }\n    if (initOptions.onLoad === 'login-required') {\n      kc.loginRequired = true;\n    }\n    if (initOptions.responseMode) {\n      if (initOptions.responseMode === 'query' || initOptions.responseMode === 'fragment') {\n        kc.responseMode = initOptions.responseMode;\n      } else {\n        throw 'Invalid value for responseMode';\n      }\n    }\n    if (initOptions.flow) {\n      switch (initOptions.flow) {\n        case 'standard':\n          kc.responseType = 'code';\n          break;\n        case 'implicit':\n          kc.responseType = 'id_token token';\n          break;\n        case 'hybrid':\n          kc.responseType = 'code id_token token';\n          break;\n        default:\n          throw 'Invalid value for flow';\n      }\n      kc.flow = initOptions.flow;\n    }\n    if (initOptions.timeSkew != null) {\n      kc.timeSkew = initOptions.timeSkew;\n    }\n    if (initOptions.redirectUri) {\n      kc.redirectUri = initOptions.redirectUri;\n    }\n    if (initOptions.silentCheckSsoRedirectUri) {\n      kc.silentCheckSsoRedirectUri = initOptions.silentCheckSsoRedirectUri;\n    }\n    if (typeof initOptions.silentCheckSsoFallback === 'boolean') {\n      kc.silentCheckSsoFallback = initOptions.silentCheckSsoFallback;\n    } else {\n      kc.silentCheckSsoFallback = true;\n    }\n    if (typeof initOptions.pkceMethod !== \"undefined\") {\n      if (initOptions.pkceMethod !== \"S256\" && initOptions.pkceMethod !== false) {\n        throw new TypeError(`Invalid value for pkceMethod', expected 'S256' or false but got ${initOptions.pkceMethod}.`);\n      }\n      kc.pkceMethod = initOptions.pkceMethod;\n    } else {\n      kc.pkceMethod = \"S256\";\n    }\n    if (typeof initOptions.enableLogging === 'boolean') {\n      kc.enableLogging = initOptions.enableLogging;\n    } else {\n      kc.enableLogging = false;\n    }\n    if (initOptions.logoutMethod === 'POST') {\n      kc.logoutMethod = 'POST';\n    } else {\n      kc.logoutMethod = 'GET';\n    }\n    if (typeof initOptions.scope === 'string') {\n      kc.scope = initOptions.scope;\n    }\n    if (typeof initOptions.acrValues === 'string') {\n      kc.acrValues = initOptions.acrValues;\n    }\n    if (typeof initOptions.messageReceiveTimeout === 'number' && initOptions.messageReceiveTimeout > 0) {\n      kc.messageReceiveTimeout = initOptions.messageReceiveTimeout;\n    } else {\n      kc.messageReceiveTimeout = 10000;\n    }\n    if (!kc.responseMode) {\n      kc.responseMode = 'fragment';\n    }\n    if (!kc.responseType) {\n      kc.responseType = 'code';\n      kc.flow = 'standard';\n    }\n    var promise = createPromise();\n    var initPromise = createPromise();\n    initPromise.promise.then(function () {\n      kc.onReady && kc.onReady(kc.authenticated);\n      promise.setSuccess(kc.authenticated);\n    }).catch(function (error) {\n      promise.setError(error);\n    });\n    var configPromise = loadConfig();\n    function onLoad() {\n      var doLogin = function (prompt) {\n        if (!prompt) {\n          options.prompt = 'none';\n        }\n        if (initOptions.locale) {\n          options.locale = initOptions.locale;\n        }\n        kc.login(options).then(function () {\n          initPromise.setSuccess();\n        }).catch(function (error) {\n          initPromise.setError(error);\n        });\n      };\n      var checkSsoSilently = async function () {\n        var ifrm = document.createElement(\"iframe\");\n        var src = await kc.createLoginUrl({\n          prompt: 'none',\n          redirectUri: kc.silentCheckSsoRedirectUri\n        });\n        ifrm.setAttribute(\"src\", src);\n        ifrm.setAttribute(\"sandbox\", \"allow-storage-access-by-user-activation allow-scripts allow-same-origin\");\n        ifrm.setAttribute(\"title\", \"keycloak-silent-check-sso\");\n        ifrm.style.display = \"none\";\n        document.body.appendChild(ifrm);\n        var messageCallback = function (event) {\n          if (event.origin !== window.location.origin || ifrm.contentWindow !== event.source) {\n            return;\n          }\n          var oauth = parseCallback(event.data);\n          processCallback(oauth, initPromise);\n          document.body.removeChild(ifrm);\n          window.removeEventListener(\"message\", messageCallback);\n        };\n        window.addEventListener(\"message\", messageCallback);\n      };\n      var options = {};\n      switch (initOptions.onLoad) {\n        case 'check-sso':\n          if (loginIframe.enable) {\n            setupCheckLoginIframe().then(function () {\n              checkLoginIframe().then(function (unchanged) {\n                if (!unchanged) {\n                  kc.silentCheckSsoRedirectUri ? checkSsoSilently() : doLogin(false);\n                } else {\n                  initPromise.setSuccess();\n                }\n              }).catch(function (error) {\n                initPromise.setError(error);\n              });\n            });\n          } else {\n            kc.silentCheckSsoRedirectUri ? checkSsoSilently() : doLogin(false);\n          }\n          break;\n        case 'login-required':\n          doLogin(true);\n          break;\n        default:\n          throw 'Invalid value for onLoad';\n      }\n    }\n    function processInit() {\n      var callback = parseCallback(window.location.href);\n      if (callback) {\n        window.history.replaceState(window.history.state, null, callback.newUrl);\n      }\n      if (callback && callback.valid) {\n        return setupCheckLoginIframe().then(function () {\n          processCallback(callback, initPromise);\n        }).catch(function (error) {\n          initPromise.setError(error);\n        });\n      }\n      if (initOptions.token && initOptions.refreshToken) {\n        setToken(initOptions.token, initOptions.refreshToken, initOptions.idToken);\n        if (loginIframe.enable) {\n          setupCheckLoginIframe().then(function () {\n            checkLoginIframe().then(function (unchanged) {\n              if (unchanged) {\n                kc.onAuthSuccess && kc.onAuthSuccess();\n                initPromise.setSuccess();\n                scheduleCheckIframe();\n              } else {\n                initPromise.setSuccess();\n              }\n            }).catch(function (error) {\n              initPromise.setError(error);\n            });\n          });\n        } else {\n          kc.updateToken(-1).then(function () {\n            kc.onAuthSuccess && kc.onAuthSuccess();\n            initPromise.setSuccess();\n          }).catch(function (error) {\n            kc.onAuthError && kc.onAuthError();\n            if (initOptions.onLoad) {\n              onLoad();\n            } else {\n              initPromise.setError(error);\n            }\n          });\n        }\n      } else if (initOptions.onLoad) {\n        onLoad();\n      } else {\n        initPromise.setSuccess();\n      }\n    }\n    configPromise.then(function () {\n      check3pCookiesSupported().then(processInit).catch(function (error) {\n        promise.setError(error);\n      });\n    });\n    configPromise.catch(function (error) {\n      promise.setError(error);\n    });\n    return promise.promise;\n  };\n  kc.login = function (options) {\n    return adapter.login(options);\n  };\n  function generateRandomData(len) {\n    if (typeof crypto === \"undefined\" || typeof crypto.getRandomValues === \"undefined\") {\n      throw new Error(\"Web Crypto API is not available.\");\n    }\n    return crypto.getRandomValues(new Uint8Array(len));\n  }\n  function generateCodeVerifier(len) {\n    return generateRandomString(len, 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789');\n  }\n  function generateRandomString(len, alphabet) {\n    var randomData = generateRandomData(len);\n    var chars = new Array(len);\n    for (var i = 0; i < len; i++) {\n      chars[i] = alphabet.charCodeAt(randomData[i] % alphabet.length);\n    }\n    return String.fromCharCode.apply(null, chars);\n  }\n  async function generatePkceChallenge(pkceMethod, codeVerifier) {\n    if (pkceMethod !== \"S256\") {\n      throw new TypeError(`Invalid value for 'pkceMethod', expected 'S256' but got '${pkceMethod}'.`);\n    }\n\n    // hash codeVerifier, then encode as url-safe base64 without padding\n    const hashBytes = new Uint8Array(await sha256Digest(codeVerifier));\n    const encodedHash = bytesToBase64(hashBytes).replace(/\\+/g, '-').replace(/\\//g, '_').replace(/\\=/g, '');\n    return encodedHash;\n  }\n  function buildClaimsParameter(requestedAcr) {\n    var claims = {\n      id_token: {\n        acr: requestedAcr\n      }\n    };\n    return JSON.stringify(claims);\n  }\n  kc.createLoginUrl = async function (options) {\n    var state = createUUID();\n    var nonce = createUUID();\n    var redirectUri = adapter.redirectUri(options);\n    var callbackState = {\n      state: state,\n      nonce: nonce,\n      redirectUri: encodeURIComponent(redirectUri),\n      loginOptions: options\n    };\n    if (options && options.prompt) {\n      callbackState.prompt = options.prompt;\n    }\n    var baseUrl;\n    if (options && options.action == 'register') {\n      baseUrl = kc.endpoints.register();\n    } else {\n      baseUrl = kc.endpoints.authorize();\n    }\n    var scope = options && options.scope || kc.scope;\n    if (!scope) {\n      // if scope is not set, default to \"openid\"\n      scope = \"openid\";\n    } else if (scope.indexOf(\"openid\") === -1) {\n      // if openid scope is missing, prefix the given scopes with it\n      scope = \"openid \" + scope;\n    }\n    var url = baseUrl + '?client_id=' + encodeURIComponent(kc.clientId) + '&redirect_uri=' + encodeURIComponent(redirectUri) + '&state=' + encodeURIComponent(state) + '&response_mode=' + encodeURIComponent(kc.responseMode) + '&response_type=' + encodeURIComponent(kc.responseType) + '&scope=' + encodeURIComponent(scope);\n    if (useNonce) {\n      url = url + '&nonce=' + encodeURIComponent(nonce);\n    }\n    if (options && options.prompt) {\n      url += '&prompt=' + encodeURIComponent(options.prompt);\n    }\n    if (options && typeof options.maxAge === 'number') {\n      url += '&max_age=' + encodeURIComponent(options.maxAge);\n    }\n    if (options && options.loginHint) {\n      url += '&login_hint=' + encodeURIComponent(options.loginHint);\n    }\n    if (options && options.idpHint) {\n      url += '&kc_idp_hint=' + encodeURIComponent(options.idpHint);\n    }\n    if (options && options.action && options.action != 'register') {\n      url += '&kc_action=' + encodeURIComponent(options.action);\n    }\n    if (options && options.locale) {\n      url += '&ui_locales=' + encodeURIComponent(options.locale);\n    }\n    if (options && options.acr) {\n      var claimsParameter = buildClaimsParameter(options.acr);\n      url += '&claims=' + encodeURIComponent(claimsParameter);\n    }\n    if (options && options.acrValues || kc.acrValues) {\n      url += '&acr_values=' + encodeURIComponent(options.acrValues || kc.acrValues);\n    }\n    if (kc.pkceMethod) {\n      try {\n        const codeVerifier = generateCodeVerifier(96);\n        const pkceChallenge = await generatePkceChallenge(kc.pkceMethod, codeVerifier);\n        callbackState.pkceCodeVerifier = codeVerifier;\n        url += '&code_challenge=' + pkceChallenge;\n        url += '&code_challenge_method=' + kc.pkceMethod;\n      } catch (error) {\n        throw new Error(\"Failed to generate PKCE challenge.\", {\n          cause: error\n        });\n      }\n    }\n    callbackStorage.add(callbackState);\n    return url;\n  };\n  kc.logout = function (options) {\n    return adapter.logout(options);\n  };\n  kc.createLogoutUrl = function (options) {\n    const logoutMethod = options?.logoutMethod ?? kc.logoutMethod;\n    if (logoutMethod === 'POST') {\n      return kc.endpoints.logout();\n    }\n    var url = kc.endpoints.logout() + '?client_id=' + encodeURIComponent(kc.clientId) + '&post_logout_redirect_uri=' + encodeURIComponent(adapter.redirectUri(options, false));\n    if (kc.idToken) {\n      url += '&id_token_hint=' + encodeURIComponent(kc.idToken);\n    }\n    return url;\n  };\n  kc.register = function (options) {\n    return adapter.register(options);\n  };\n  kc.createRegisterUrl = async function (options) {\n    if (!options) {\n      options = {};\n    }\n    options.action = 'register';\n    return await kc.createLoginUrl(options);\n  };\n  kc.createAccountUrl = function (options) {\n    var realm = getRealmUrl();\n    var url = undefined;\n    if (typeof realm !== 'undefined') {\n      url = realm + '/account' + '?referrer=' + encodeURIComponent(kc.clientId) + '&referrer_uri=' + encodeURIComponent(adapter.redirectUri(options));\n    }\n    return url;\n  };\n  kc.accountManagement = function () {\n    return adapter.accountManagement();\n  };\n  kc.hasRealmRole = function (role) {\n    var access = kc.realmAccess;\n    return !!access && access.roles.indexOf(role) >= 0;\n  };\n  kc.hasResourceRole = function (role, resource) {\n    if (!kc.resourceAccess) {\n      return false;\n    }\n    var access = kc.resourceAccess[resource || kc.clientId];\n    return !!access && access.roles.indexOf(role) >= 0;\n  };\n  kc.loadUserProfile = function () {\n    var url = getRealmUrl() + '/account';\n    var req = new XMLHttpRequest();\n    req.open('GET', url, true);\n    req.setRequestHeader('Accept', 'application/json');\n    req.setRequestHeader('Authorization', 'bearer ' + kc.token);\n    var promise = createPromise();\n    req.onreadystatechange = function () {\n      if (req.readyState == 4) {\n        if (req.status == 200) {\n          kc.profile = JSON.parse(req.responseText);\n          promise.setSuccess(kc.profile);\n        } else {\n          promise.setError();\n        }\n      }\n    };\n    req.send();\n    return promise.promise;\n  };\n  kc.loadUserInfo = function () {\n    var url = kc.endpoints.userinfo();\n    var req = new XMLHttpRequest();\n    req.open('GET', url, true);\n    req.setRequestHeader('Accept', 'application/json');\n    req.setRequestHeader('Authorization', 'bearer ' + kc.token);\n    var promise = createPromise();\n    req.onreadystatechange = function () {\n      if (req.readyState == 4) {\n        if (req.status == 200) {\n          kc.userInfo = JSON.parse(req.responseText);\n          promise.setSuccess(kc.userInfo);\n        } else {\n          promise.setError();\n        }\n      }\n    };\n    req.send();\n    return promise.promise;\n  };\n  kc.isTokenExpired = function (minValidity) {\n    if (!kc.tokenParsed || !kc.refreshToken && kc.flow != 'implicit') {\n      throw 'Not authenticated';\n    }\n    if (kc.timeSkew == null) {\n      logInfo('[KEYCLOAK] Unable to determine if token is expired as timeskew is not set');\n      return true;\n    }\n    var expiresIn = kc.tokenParsed['exp'] - Math.ceil(new Date().getTime() / 1000) + kc.timeSkew;\n    if (minValidity) {\n      if (isNaN(minValidity)) {\n        throw 'Invalid minValidity';\n      }\n      expiresIn -= minValidity;\n    }\n    return expiresIn < 0;\n  };\n  kc.updateToken = function (minValidity) {\n    var promise = createPromise();\n    if (!kc.refreshToken) {\n      promise.setError();\n      return promise.promise;\n    }\n    minValidity = minValidity || 5;\n    var exec = function () {\n      var refreshToken = false;\n      if (minValidity == -1) {\n        refreshToken = true;\n        logInfo('[KEYCLOAK] Refreshing token: forced refresh');\n      } else if (!kc.tokenParsed || kc.isTokenExpired(minValidity)) {\n        refreshToken = true;\n        logInfo('[KEYCLOAK] Refreshing token: token expired');\n      }\n      if (!refreshToken) {\n        promise.setSuccess(false);\n      } else {\n        var params = 'grant_type=refresh_token&' + 'refresh_token=' + kc.refreshToken;\n        var url = kc.endpoints.token();\n        refreshQueue.push(promise);\n        if (refreshQueue.length == 1) {\n          var req = new XMLHttpRequest();\n          req.open('POST', url, true);\n          req.setRequestHeader('Content-type', 'application/x-www-form-urlencoded');\n          req.withCredentials = true;\n          params += '&client_id=' + encodeURIComponent(kc.clientId);\n          var timeLocal = new Date().getTime();\n          req.onreadystatechange = function () {\n            if (req.readyState == 4) {\n              if (req.status == 200) {\n                logInfo('[KEYCLOAK] Token refreshed');\n                timeLocal = (timeLocal + new Date().getTime()) / 2;\n                var tokenResponse = JSON.parse(req.responseText);\n                setToken(tokenResponse['access_token'], tokenResponse['refresh_token'], tokenResponse['id_token'], timeLocal);\n                kc.onAuthRefreshSuccess && kc.onAuthRefreshSuccess();\n                for (var p = refreshQueue.pop(); p != null; p = refreshQueue.pop()) {\n                  p.setSuccess(true);\n                }\n              } else {\n                logWarn('[KEYCLOAK] Failed to refresh token');\n                if (req.status == 400) {\n                  kc.clearToken();\n                }\n                kc.onAuthRefreshError && kc.onAuthRefreshError();\n                for (var p = refreshQueue.pop(); p != null; p = refreshQueue.pop()) {\n                  p.setError(\"Failed to refresh token: An unexpected HTTP error occurred while attempting to refresh the token.\");\n                }\n              }\n            }\n          };\n          req.send(params);\n        }\n      }\n    };\n    if (loginIframe.enable) {\n      var iframePromise = checkLoginIframe();\n      iframePromise.then(function () {\n        exec();\n      }).catch(function (error) {\n        promise.setError(error);\n      });\n    } else {\n      exec();\n    }\n    return promise.promise;\n  };\n  kc.clearToken = function () {\n    if (kc.token) {\n      setToken(null, null, null);\n      kc.onAuthLogout && kc.onAuthLogout();\n      if (kc.loginRequired) {\n        kc.login();\n      }\n    }\n  };\n  function getRealmUrl() {\n    if (typeof kc.authServerUrl !== 'undefined') {\n      if (kc.authServerUrl.charAt(kc.authServerUrl.length - 1) == '/') {\n        return kc.authServerUrl + 'realms/' + encodeURIComponent(kc.realm);\n      } else {\n        return kc.authServerUrl + '/realms/' + encodeURIComponent(kc.realm);\n      }\n    } else {\n      return undefined;\n    }\n  }\n  function getOrigin() {\n    if (!window.location.origin) {\n      return window.location.protocol + \"//\" + window.location.hostname + (window.location.port ? ':' + window.location.port : '');\n    } else {\n      return window.location.origin;\n    }\n  }\n  function processCallback(oauth, promise) {\n    var code = oauth.code;\n    var error = oauth.error;\n    var prompt = oauth.prompt;\n    var timeLocal = new Date().getTime();\n    if (oauth['kc_action_status']) {\n      kc.onActionUpdate && kc.onActionUpdate(oauth['kc_action_status'], oauth['kc_action']);\n    }\n    if (error) {\n      if (prompt != 'none') {\n        if (oauth.error_description && oauth.error_description === \"authentication_expired\") {\n          kc.login(oauth.loginOptions);\n        } else {\n          var errorData = {\n            error: error,\n            error_description: oauth.error_description\n          };\n          kc.onAuthError && kc.onAuthError(errorData);\n          promise && promise.setError(errorData);\n        }\n      } else {\n        promise && promise.setSuccess();\n      }\n      return;\n    } else if (kc.flow != 'standard' && (oauth.access_token || oauth.id_token)) {\n      authSuccess(oauth.access_token, null, oauth.id_token, true);\n    }\n    if (kc.flow != 'implicit' && code) {\n      var params = 'code=' + code + '&grant_type=authorization_code';\n      var url = kc.endpoints.token();\n      var req = new XMLHttpRequest();\n      req.open('POST', url, true);\n      req.setRequestHeader('Content-type', 'application/x-www-form-urlencoded');\n      params += '&client_id=' + encodeURIComponent(kc.clientId);\n      params += '&redirect_uri=' + oauth.redirectUri;\n      if (oauth.pkceCodeVerifier) {\n        params += '&code_verifier=' + oauth.pkceCodeVerifier;\n      }\n      req.withCredentials = true;\n      req.onreadystatechange = function () {\n        if (req.readyState == 4) {\n          if (req.status == 200) {\n            var tokenResponse = JSON.parse(req.responseText);\n            authSuccess(tokenResponse['access_token'], tokenResponse['refresh_token'], tokenResponse['id_token'], kc.flow === 'standard');\n            scheduleCheckIframe();\n          } else {\n            kc.onAuthError && kc.onAuthError();\n            promise && promise.setError();\n          }\n        }\n      };\n      req.send(params);\n    }\n    function authSuccess(accessToken, refreshToken, idToken, fulfillPromise) {\n      timeLocal = (timeLocal + new Date().getTime()) / 2;\n      setToken(accessToken, refreshToken, idToken, timeLocal);\n      if (useNonce && kc.idTokenParsed && kc.idTokenParsed.nonce != oauth.storedNonce) {\n        logInfo('[KEYCLOAK] Invalid nonce, clearing token');\n        kc.clearToken();\n        promise && promise.setError();\n      } else {\n        if (fulfillPromise) {\n          kc.onAuthSuccess && kc.onAuthSuccess();\n          promise && promise.setSuccess();\n        }\n      }\n    }\n  }\n  function loadConfig() {\n    var promise = createPromise();\n    var configUrl;\n    if (typeof config === 'string') {\n      configUrl = config;\n    }\n    function setupOidcEndoints(oidcConfiguration) {\n      if (!oidcConfiguration) {\n        kc.endpoints = {\n          authorize: function () {\n            return getRealmUrl() + '/protocol/openid-connect/auth';\n          },\n          token: function () {\n            return getRealmUrl() + '/protocol/openid-connect/token';\n          },\n          logout: function () {\n            return getRealmUrl() + '/protocol/openid-connect/logout';\n          },\n          checkSessionIframe: function () {\n            return getRealmUrl() + '/protocol/openid-connect/login-status-iframe.html';\n          },\n          thirdPartyCookiesIframe: function () {\n            return getRealmUrl() + '/protocol/openid-connect/3p-cookies/step1.html';\n          },\n          register: function () {\n            return getRealmUrl() + '/protocol/openid-connect/registrations';\n          },\n          userinfo: function () {\n            return getRealmUrl() + '/protocol/openid-connect/userinfo';\n          }\n        };\n      } else {\n        kc.endpoints = {\n          authorize: function () {\n            return oidcConfiguration.authorization_endpoint;\n          },\n          token: function () {\n            return oidcConfiguration.token_endpoint;\n          },\n          logout: function () {\n            if (!oidcConfiguration.end_session_endpoint) {\n              throw \"Not supported by the OIDC server\";\n            }\n            return oidcConfiguration.end_session_endpoint;\n          },\n          checkSessionIframe: function () {\n            if (!oidcConfiguration.check_session_iframe) {\n              throw \"Not supported by the OIDC server\";\n            }\n            return oidcConfiguration.check_session_iframe;\n          },\n          register: function () {\n            throw 'Redirection to \"Register user\" page not supported in standard OIDC mode';\n          },\n          userinfo: function () {\n            if (!oidcConfiguration.userinfo_endpoint) {\n              throw \"Not supported by the OIDC server\";\n            }\n            return oidcConfiguration.userinfo_endpoint;\n          }\n        };\n      }\n    }\n    if (configUrl) {\n      var req = new XMLHttpRequest();\n      req.open('GET', configUrl, true);\n      req.setRequestHeader('Accept', 'application/json');\n      req.onreadystatechange = function () {\n        if (req.readyState == 4) {\n          if (req.status == 200 || fileLoaded(req)) {\n            var config = JSON.parse(req.responseText);\n            kc.authServerUrl = config['auth-server-url'];\n            kc.realm = config['realm'];\n            kc.clientId = config['resource'];\n            setupOidcEndoints(null);\n            promise.setSuccess();\n          } else {\n            promise.setError();\n          }\n        }\n      };\n      req.send();\n    } else {\n      kc.clientId = config.clientId;\n      var oidcProvider = config['oidcProvider'];\n      if (!oidcProvider) {\n        kc.authServerUrl = config.url;\n        kc.realm = config.realm;\n        setupOidcEndoints(null);\n        promise.setSuccess();\n      } else {\n        if (typeof oidcProvider === 'string') {\n          var oidcProviderConfigUrl;\n          if (oidcProvider.charAt(oidcProvider.length - 1) == '/') {\n            oidcProviderConfigUrl = oidcProvider + '.well-known/openid-configuration';\n          } else {\n            oidcProviderConfigUrl = oidcProvider + '/.well-known/openid-configuration';\n          }\n          var req = new XMLHttpRequest();\n          req.open('GET', oidcProviderConfigUrl, true);\n          req.setRequestHeader('Accept', 'application/json');\n          req.onreadystatechange = function () {\n            if (req.readyState == 4) {\n              if (req.status == 200 || fileLoaded(req)) {\n                var oidcProviderConfig = JSON.parse(req.responseText);\n                setupOidcEndoints(oidcProviderConfig);\n                promise.setSuccess();\n              } else {\n                promise.setError();\n              }\n            }\n          };\n          req.send();\n        } else {\n          setupOidcEndoints(oidcProvider);\n          promise.setSuccess();\n        }\n      }\n    }\n    return promise.promise;\n  }\n  function fileLoaded(xhr) {\n    return xhr.status == 0 && xhr.responseText && xhr.responseURL.startsWith('file:');\n  }\n  function setToken(token, refreshToken, idToken, timeLocal) {\n    if (kc.tokenTimeoutHandle) {\n      clearTimeout(kc.tokenTimeoutHandle);\n      kc.tokenTimeoutHandle = null;\n    }\n    if (refreshToken) {\n      kc.refreshToken = refreshToken;\n      kc.refreshTokenParsed = decodeToken(refreshToken);\n    } else {\n      delete kc.refreshToken;\n      delete kc.refreshTokenParsed;\n    }\n    if (idToken) {\n      kc.idToken = idToken;\n      kc.idTokenParsed = decodeToken(idToken);\n    } else {\n      delete kc.idToken;\n      delete kc.idTokenParsed;\n    }\n    if (token) {\n      kc.token = token;\n      kc.tokenParsed = decodeToken(token);\n      kc.sessionId = kc.tokenParsed.sid;\n      kc.authenticated = true;\n      kc.subject = kc.tokenParsed.sub;\n      kc.realmAccess = kc.tokenParsed.realm_access;\n      kc.resourceAccess = kc.tokenParsed.resource_access;\n      if (timeLocal) {\n        kc.timeSkew = Math.floor(timeLocal / 1000) - kc.tokenParsed.iat;\n      }\n      if (kc.timeSkew != null) {\n        logInfo('[KEYCLOAK] Estimated time difference between browser and server is ' + kc.timeSkew + ' seconds');\n        if (kc.onTokenExpired) {\n          var expiresIn = (kc.tokenParsed['exp'] - new Date().getTime() / 1000 + kc.timeSkew) * 1000;\n          logInfo('[KEYCLOAK] Token expires in ' + Math.round(expiresIn / 1000) + ' s');\n          if (expiresIn <= 0) {\n            kc.onTokenExpired();\n          } else {\n            kc.tokenTimeoutHandle = setTimeout(kc.onTokenExpired, expiresIn);\n          }\n        }\n      }\n    } else {\n      delete kc.token;\n      delete kc.tokenParsed;\n      delete kc.subject;\n      delete kc.realmAccess;\n      delete kc.resourceAccess;\n      kc.authenticated = false;\n    }\n  }\n  function createUUID() {\n    if (typeof crypto === \"undefined\" || typeof crypto.randomUUID === \"undefined\") {\n      throw new Error(\"Web Crypto API is not available.\");\n    }\n    return crypto.randomUUID();\n  }\n  function parseCallback(url) {\n    var oauth = parseCallbackUrl(url);\n    if (!oauth) {\n      return;\n    }\n    var oauthState = callbackStorage.get(oauth.state);\n    if (oauthState) {\n      oauth.valid = true;\n      oauth.redirectUri = oauthState.redirectUri;\n      oauth.storedNonce = oauthState.nonce;\n      oauth.prompt = oauthState.prompt;\n      oauth.pkceCodeVerifier = oauthState.pkceCodeVerifier;\n      oauth.loginOptions = oauthState.loginOptions;\n    }\n    return oauth;\n  }\n  function parseCallbackUrl(url) {\n    var supportedParams;\n    switch (kc.flow) {\n      case 'standard':\n        supportedParams = ['code', 'state', 'session_state', 'kc_action_status', 'kc_action', 'iss'];\n        break;\n      case 'implicit':\n        supportedParams = ['access_token', 'token_type', 'id_token', 'state', 'session_state', 'expires_in', 'kc_action_status', 'kc_action', 'iss'];\n        break;\n      case 'hybrid':\n        supportedParams = ['access_token', 'token_type', 'id_token', 'code', 'state', 'session_state', 'expires_in', 'kc_action_status', 'kc_action', 'iss'];\n        break;\n    }\n    supportedParams.push('error');\n    supportedParams.push('error_description');\n    supportedParams.push('error_uri');\n    var queryIndex = url.indexOf('?');\n    var fragmentIndex = url.indexOf('#');\n    var newUrl;\n    var parsed;\n    if (kc.responseMode === 'query' && queryIndex !== -1) {\n      newUrl = url.substring(0, queryIndex);\n      parsed = parseCallbackParams(url.substring(queryIndex + 1, fragmentIndex !== -1 ? fragmentIndex : url.length), supportedParams);\n      if (parsed.paramsString !== '') {\n        newUrl += '?' + parsed.paramsString;\n      }\n      if (fragmentIndex !== -1) {\n        newUrl += url.substring(fragmentIndex);\n      }\n    } else if (kc.responseMode === 'fragment' && fragmentIndex !== -1) {\n      newUrl = url.substring(0, fragmentIndex);\n      parsed = parseCallbackParams(url.substring(fragmentIndex + 1), supportedParams);\n      if (parsed.paramsString !== '') {\n        newUrl += '#' + parsed.paramsString;\n      }\n    }\n    if (parsed && parsed.oauthParams) {\n      if (kc.flow === 'standard' || kc.flow === 'hybrid') {\n        if ((parsed.oauthParams.code || parsed.oauthParams.error) && parsed.oauthParams.state) {\n          parsed.oauthParams.newUrl = newUrl;\n          return parsed.oauthParams;\n        }\n      } else if (kc.flow === 'implicit') {\n        if ((parsed.oauthParams.access_token || parsed.oauthParams.error) && parsed.oauthParams.state) {\n          parsed.oauthParams.newUrl = newUrl;\n          return parsed.oauthParams;\n        }\n      }\n    }\n  }\n  function parseCallbackParams(paramsString, supportedParams) {\n    var p = paramsString.split('&');\n    var result = {\n      paramsString: '',\n      oauthParams: {}\n    };\n    for (var i = 0; i < p.length; i++) {\n      var split = p[i].indexOf(\"=\");\n      var key = p[i].slice(0, split);\n      if (supportedParams.indexOf(key) !== -1) {\n        result.oauthParams[key] = p[i].slice(split + 1);\n      } else {\n        if (result.paramsString !== '') {\n          result.paramsString += '&';\n        }\n        result.paramsString += p[i];\n      }\n    }\n    return result;\n  }\n  function createPromise() {\n    // Need to create a native Promise which also preserves the\n    // interface of the custom promise type previously used by the API\n    var p = {\n      setSuccess: function (result) {\n        p.resolve(result);\n      },\n      setError: function (result) {\n        p.reject(result);\n      }\n    };\n    p.promise = new Promise(function (resolve, reject) {\n      p.resolve = resolve;\n      p.reject = reject;\n    });\n    return p;\n  }\n\n  // Function to extend existing native Promise with timeout\n  function applyTimeoutToPromise(promise, timeout, errorMessage) {\n    var timeoutHandle = null;\n    var timeoutPromise = new Promise(function (resolve, reject) {\n      timeoutHandle = setTimeout(function () {\n        reject({\n          \"error\": errorMessage || \"Promise is not settled within timeout of \" + timeout + \"ms\"\n        });\n      }, timeout);\n    });\n    return Promise.race([promise, timeoutPromise]).finally(function () {\n      clearTimeout(timeoutHandle);\n    });\n  }\n  function setupCheckLoginIframe() {\n    var promise = createPromise();\n    if (!loginIframe.enable) {\n      promise.setSuccess();\n      return promise.promise;\n    }\n    if (loginIframe.iframe) {\n      promise.setSuccess();\n      return promise.promise;\n    }\n    var iframe = document.createElement('iframe');\n    loginIframe.iframe = iframe;\n    iframe.onload = function () {\n      var authUrl = kc.endpoints.authorize();\n      if (authUrl.charAt(0) === '/') {\n        loginIframe.iframeOrigin = getOrigin();\n      } else {\n        loginIframe.iframeOrigin = authUrl.substring(0, authUrl.indexOf('/', 8));\n      }\n      promise.setSuccess();\n    };\n    var src = kc.endpoints.checkSessionIframe();\n    iframe.setAttribute('src', src);\n    iframe.setAttribute('sandbox', 'allow-storage-access-by-user-activation allow-scripts allow-same-origin');\n    iframe.setAttribute('title', 'keycloak-session-iframe');\n    iframe.style.display = 'none';\n    document.body.appendChild(iframe);\n    var messageCallback = function (event) {\n      if (event.origin !== loginIframe.iframeOrigin || loginIframe.iframe.contentWindow !== event.source) {\n        return;\n      }\n      if (!(event.data == 'unchanged' || event.data == 'changed' || event.data == 'error')) {\n        return;\n      }\n      if (event.data != 'unchanged') {\n        kc.clearToken();\n      }\n      var callbacks = loginIframe.callbackList.splice(0, loginIframe.callbackList.length);\n      for (var i = callbacks.length - 1; i >= 0; --i) {\n        var promise = callbacks[i];\n        if (event.data == 'error') {\n          promise.setError();\n        } else {\n          promise.setSuccess(event.data == 'unchanged');\n        }\n      }\n    };\n    window.addEventListener('message', messageCallback, false);\n    return promise.promise;\n  }\n  function scheduleCheckIframe() {\n    if (loginIframe.enable) {\n      if (kc.token) {\n        setTimeout(function () {\n          checkLoginIframe().then(function (unchanged) {\n            if (unchanged) {\n              scheduleCheckIframe();\n            }\n          });\n        }, loginIframe.interval * 1000);\n      }\n    }\n  }\n  function checkLoginIframe() {\n    var promise = createPromise();\n    if (loginIframe.iframe && loginIframe.iframeOrigin) {\n      var msg = kc.clientId + ' ' + (kc.sessionId ? kc.sessionId : '');\n      loginIframe.callbackList.push(promise);\n      var origin = loginIframe.iframeOrigin;\n      if (loginIframe.callbackList.length == 1) {\n        loginIframe.iframe.contentWindow.postMessage(msg, origin);\n      }\n    } else {\n      promise.setSuccess();\n    }\n    return promise.promise;\n  }\n  function check3pCookiesSupported() {\n    var promise = createPromise();\n    if ((loginIframe.enable || kc.silentCheckSsoRedirectUri) && typeof kc.endpoints.thirdPartyCookiesIframe === 'function') {\n      var iframe = document.createElement('iframe');\n      iframe.setAttribute('src', kc.endpoints.thirdPartyCookiesIframe());\n      iframe.setAttribute('sandbox', 'allow-storage-access-by-user-activation allow-scripts allow-same-origin');\n      iframe.setAttribute('title', 'keycloak-3p-check-iframe');\n      iframe.style.display = 'none';\n      document.body.appendChild(iframe);\n      var messageCallback = function (event) {\n        if (iframe.contentWindow !== event.source) {\n          return;\n        }\n        if (event.data !== \"supported\" && event.data !== \"unsupported\") {\n          return;\n        } else if (event.data === \"unsupported\") {\n          logWarn(\"[KEYCLOAK] Your browser is blocking access to 3rd-party cookies, this means:\\n\\n\" + \" - It is not possible to retrieve tokens without redirecting to the Keycloak server (a.k.a. no support for silent authentication).\\n\" + \" - It is not possible to automatically detect changes to the session status (such as the user logging out in another tab).\\n\\n\" + \"For more information see: https://www.keycloak.org/securing-apps/javascript-adapter#_modern_browsers\");\n          loginIframe.enable = false;\n          if (kc.silentCheckSsoFallback) {\n            kc.silentCheckSsoRedirectUri = false;\n          }\n        }\n        document.body.removeChild(iframe);\n        window.removeEventListener(\"message\", messageCallback);\n        promise.setSuccess();\n      };\n      window.addEventListener('message', messageCallback, false);\n    } else {\n      promise.setSuccess();\n    }\n    return applyTimeoutToPromise(promise.promise, kc.messageReceiveTimeout, \"Timeout when waiting for 3rd party check iframe message.\");\n  }\n  function loadAdapter(type) {\n    if (!type || type == 'default') {\n      return {\n        login: async function (options) {\n          window.location.assign(await kc.createLoginUrl(options));\n          return createPromise().promise;\n        },\n        logout: async function (options) {\n          const logoutMethod = options?.logoutMethod ?? kc.logoutMethod;\n          if (logoutMethod === \"GET\") {\n            window.location.replace(kc.createLogoutUrl(options));\n            return;\n          }\n\n          // Create form to send POST request.\n          const form = document.createElement(\"form\");\n          form.setAttribute(\"method\", \"POST\");\n          form.setAttribute(\"action\", kc.createLogoutUrl(options));\n          form.style.display = \"none\";\n\n          // Add data to form as hidden input fields.\n          const data = {\n            id_token_hint: kc.idToken,\n            client_id: kc.clientId,\n            post_logout_redirect_uri: adapter.redirectUri(options, false)\n          };\n          for (const [name, value] of Object.entries(data)) {\n            const input = document.createElement(\"input\");\n            input.setAttribute(\"type\", \"hidden\");\n            input.setAttribute(\"name\", name);\n            input.setAttribute(\"value\", value);\n            form.appendChild(input);\n          }\n\n          // Append form to page and submit it to perform logout and redirect.\n          document.body.appendChild(form);\n          form.submit();\n        },\n        register: async function (options) {\n          window.location.assign(await kc.createRegisterUrl(options));\n          return createPromise().promise;\n        },\n        accountManagement: function () {\n          var accountUrl = kc.createAccountUrl();\n          if (typeof accountUrl !== 'undefined') {\n            window.location.href = accountUrl;\n          } else {\n            throw \"Not supported by the OIDC server\";\n          }\n          return createPromise().promise;\n        },\n        redirectUri: function (options, encodeHash) {\n          if (arguments.length == 1) {\n            encodeHash = true;\n          }\n          if (options && options.redirectUri) {\n            return options.redirectUri;\n          } else if (kc.redirectUri) {\n            return kc.redirectUri;\n          } else {\n            return location.href;\n          }\n        }\n      };\n    }\n    if (type == 'cordova') {\n      loginIframe.enable = false;\n      var cordovaOpenWindowWrapper = function (loginUrl, target, options) {\n        if (window.cordova && window.cordova.InAppBrowser) {\n          // Use inappbrowser for IOS and Android if available\n          return window.cordova.InAppBrowser.open(loginUrl, target, options);\n        } else {\n          return window.open(loginUrl, target, options);\n        }\n      };\n      var shallowCloneCordovaOptions = function (userOptions) {\n        if (userOptions && userOptions.cordovaOptions) {\n          return Object.keys(userOptions.cordovaOptions).reduce(function (options, optionName) {\n            options[optionName] = userOptions.cordovaOptions[optionName];\n            return options;\n          }, {});\n        } else {\n          return {};\n        }\n      };\n      var formatCordovaOptions = function (cordovaOptions) {\n        return Object.keys(cordovaOptions).reduce(function (options, optionName) {\n          options.push(optionName + \"=\" + cordovaOptions[optionName]);\n          return options;\n        }, []).join(\",\");\n      };\n      var createCordovaOptions = function (userOptions) {\n        var cordovaOptions = shallowCloneCordovaOptions(userOptions);\n        cordovaOptions.location = 'no';\n        if (userOptions && userOptions.prompt == 'none') {\n          cordovaOptions.hidden = 'yes';\n        }\n        return formatCordovaOptions(cordovaOptions);\n      };\n      var getCordovaRedirectUri = function () {\n        return kc.redirectUri || 'http://localhost';\n      };\n      return {\n        login: async function (options) {\n          var promise = createPromise();\n          var cordovaOptions = createCordovaOptions(options);\n          var loginUrl = await kc.createLoginUrl(options);\n          var ref = cordovaOpenWindowWrapper(loginUrl, '_blank', cordovaOptions);\n          var completed = false;\n          var closed = false;\n          var closeBrowser = function () {\n            closed = true;\n            ref.close();\n          };\n          ref.addEventListener('loadstart', function (event) {\n            if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n              var callback = parseCallback(event.url);\n              processCallback(callback, promise);\n              closeBrowser();\n              completed = true;\n            }\n          });\n          ref.addEventListener('loaderror', function (event) {\n            if (!completed) {\n              if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n                var callback = parseCallback(event.url);\n                processCallback(callback, promise);\n                closeBrowser();\n                completed = true;\n              } else {\n                promise.setError();\n                closeBrowser();\n              }\n            }\n          });\n          ref.addEventListener('exit', function (event) {\n            if (!closed) {\n              promise.setError({\n                reason: \"closed_by_user\"\n              });\n            }\n          });\n          return promise.promise;\n        },\n        logout: function (options) {\n          var promise = createPromise();\n          var logoutUrl = kc.createLogoutUrl(options);\n          var ref = cordovaOpenWindowWrapper(logoutUrl, '_blank', 'location=no,hidden=yes,clearcache=yes');\n          var error;\n          ref.addEventListener('loadstart', function (event) {\n            if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n              ref.close();\n            }\n          });\n          ref.addEventListener('loaderror', function (event) {\n            if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n              ref.close();\n            } else {\n              error = true;\n              ref.close();\n            }\n          });\n          ref.addEventListener('exit', function (event) {\n            if (error) {\n              promise.setError();\n            } else {\n              kc.clearToken();\n              promise.setSuccess();\n            }\n          });\n          return promise.promise;\n        },\n        register: async function (options) {\n          var promise = createPromise();\n          var registerUrl = await kc.createRegisterUrl();\n          var cordovaOptions = createCordovaOptions(options);\n          var ref = cordovaOpenWindowWrapper(registerUrl, '_blank', cordovaOptions);\n          ref.addEventListener('loadstart', function (event) {\n            if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n              ref.close();\n              var oauth = parseCallback(event.url);\n              processCallback(oauth, promise);\n            }\n          });\n          return promise.promise;\n        },\n        accountManagement: function () {\n          var accountUrl = kc.createAccountUrl();\n          if (typeof accountUrl !== 'undefined') {\n            var ref = cordovaOpenWindowWrapper(accountUrl, '_blank', 'location=no');\n            ref.addEventListener('loadstart', function (event) {\n              if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n                ref.close();\n              }\n            });\n          } else {\n            throw \"Not supported by the OIDC server\";\n          }\n        },\n        redirectUri: function (options) {\n          return getCordovaRedirectUri();\n        }\n      };\n    }\n    if (type == 'cordova-native') {\n      loginIframe.enable = false;\n      return {\n        login: async function (options) {\n          var promise = createPromise();\n          var loginUrl = await kc.createLoginUrl(options);\n          universalLinks.subscribe('keycloak', function (event) {\n            universalLinks.unsubscribe('keycloak');\n            window.cordova.plugins.browsertab.close();\n            var oauth = parseCallback(event.url);\n            processCallback(oauth, promise);\n          });\n          window.cordova.plugins.browsertab.openUrl(loginUrl);\n          return promise.promise;\n        },\n        logout: function (options) {\n          var promise = createPromise();\n          var logoutUrl = kc.createLogoutUrl(options);\n          universalLinks.subscribe('keycloak', function (event) {\n            universalLinks.unsubscribe('keycloak');\n            window.cordova.plugins.browsertab.close();\n            kc.clearToken();\n            promise.setSuccess();\n          });\n          window.cordova.plugins.browsertab.openUrl(logoutUrl);\n          return promise.promise;\n        },\n        register: async function (options) {\n          var promise = createPromise();\n          var registerUrl = await kc.createRegisterUrl(options);\n          universalLinks.subscribe('keycloak', function (event) {\n            universalLinks.unsubscribe('keycloak');\n            window.cordova.plugins.browsertab.close();\n            var oauth = parseCallback(event.url);\n            processCallback(oauth, promise);\n          });\n          window.cordova.plugins.browsertab.openUrl(registerUrl);\n          return promise.promise;\n        },\n        accountManagement: function () {\n          var accountUrl = kc.createAccountUrl();\n          if (typeof accountUrl !== 'undefined') {\n            window.cordova.plugins.browsertab.openUrl(accountUrl);\n          } else {\n            throw \"Not supported by the OIDC server\";\n          }\n        },\n        redirectUri: function (options) {\n          if (options && options.redirectUri) {\n            return options.redirectUri;\n          } else if (kc.redirectUri) {\n            return kc.redirectUri;\n          } else {\n            return \"http://localhost\";\n          }\n        }\n      };\n    }\n    throw 'invalid adapter type: ' + type;\n  }\n  const STORAGE_KEY_PREFIX = 'kc-callback-';\n  var LocalStorage = function () {\n    if (!(this instanceof LocalStorage)) {\n      return new LocalStorage();\n    }\n    localStorage.setItem('kc-test', 'test');\n    localStorage.removeItem('kc-test');\n    var cs = this;\n\n    /**\n     * Clears all values from local storage that are no longer valid.\n     */\n    function clearInvalidValues() {\n      const currentTime = Date.now();\n      for (const [key, value] of getStoredEntries()) {\n        // Attempt to parse the expiry time from the value.\n        const expiry = parseExpiry(value);\n\n        // Discard the value if it is malformed or expired.\n        if (expiry === null || expiry < currentTime) {\n          localStorage.removeItem(key);\n        }\n      }\n    }\n\n    /**\n     * Clears all known values from local storage.\n     */\n    function clearAllValues() {\n      for (const [key] of getStoredEntries()) {\n        localStorage.removeItem(key);\n      }\n    }\n\n    /**\n     * Gets all entries stored in local storage that are known to be managed by this class.\n     * @returns {Array<[string, unknown]>} An array of key-value pairs.\n     */\n    function getStoredEntries() {\n      return Object.entries(localStorage).filter(([key]) => key.startsWith(STORAGE_KEY_PREFIX));\n    }\n\n    /**\n     * Parses the expiry time from a value stored in local storage.\n     * @param {unknown} value\n     * @returns {number | null} The expiry time in milliseconds, or `null` if the value is malformed.\n     */\n    function parseExpiry(value) {\n      let parsedValue;\n\n      // Attempt to parse the value as JSON.\n      try {\n        parsedValue = JSON.parse(value);\n      } catch (error) {\n        return null;\n      }\n\n      // Attempt to extract the 'expires' property.\n      if (isObject(parsedValue) && 'expires' in parsedValue && typeof parsedValue.expires === 'number') {\n        return parsedValue.expires;\n      }\n      return null;\n    }\n    cs.get = function (state) {\n      if (!state) {\n        return;\n      }\n      var key = STORAGE_KEY_PREFIX + state;\n      var value = localStorage.getItem(key);\n      if (value) {\n        localStorage.removeItem(key);\n        value = JSON.parse(value);\n      }\n      clearInvalidValues();\n      return value;\n    };\n    cs.add = function (state) {\n      clearInvalidValues();\n      const key = STORAGE_KEY_PREFIX + state.state;\n      const value = JSON.stringify({\n        ...state,\n        // Set the expiry time to 1 hour from now.\n        expires: Date.now() + 60 * 60 * 1000\n      });\n      try {\n        localStorage.setItem(key, value);\n      } catch (error) {\n        // If the storage is full, clear all known values and try again.\n        clearAllValues();\n        localStorage.setItem(key, value);\n      }\n    };\n  };\n  var CookieStorage = function () {\n    if (!(this instanceof CookieStorage)) {\n      return new CookieStorage();\n    }\n    var cs = this;\n    cs.get = function (state) {\n      if (!state) {\n        return;\n      }\n      var value = getCookie(STORAGE_KEY_PREFIX + state);\n      setCookie(STORAGE_KEY_PREFIX + state, '', cookieExpiration(-100));\n      if (value) {\n        return JSON.parse(value);\n      }\n    };\n    cs.add = function (state) {\n      setCookie(STORAGE_KEY_PREFIX + state.state, JSON.stringify(state), cookieExpiration(60));\n    };\n    cs.removeItem = function (key) {\n      setCookie(key, '', cookieExpiration(-100));\n    };\n    var cookieExpiration = function (minutes) {\n      var exp = new Date();\n      exp.setTime(exp.getTime() + minutes * 60 * 1000);\n      return exp;\n    };\n    var getCookie = function (key) {\n      var name = key + '=';\n      var ca = document.cookie.split(';');\n      for (var i = 0; i < ca.length; i++) {\n        var c = ca[i];\n        while (c.charAt(0) == ' ') {\n          c = c.substring(1);\n        }\n        if (c.indexOf(name) == 0) {\n          return c.substring(name.length, c.length);\n        }\n      }\n      return '';\n    };\n    var setCookie = function (key, value, expirationDate) {\n      var cookie = key + '=' + value + '; ' + 'expires=' + expirationDate.toUTCString() + '; ';\n      document.cookie = cookie;\n    };\n  };\n  function createCallbackStorage() {\n    try {\n      return new LocalStorage();\n    } catch (err) {}\n    return new CookieStorage();\n  }\n  function createLogger(fn) {\n    return function () {\n      if (kc.enableLogging) {\n        fn.apply(console, Array.prototype.slice.call(arguments));\n      }\n    };\n  }\n}\nexport default Keycloak;\n\n/**\n * @param {ArrayBuffer} bytes\n * @see https://developer.mozilla.org/en-US/docs/Glossary/Base64#the_unicode_problem\n */\nfunction bytesToBase64(bytes) {\n  const binString = String.fromCodePoint(...bytes);\n  return btoa(binString);\n}\n\n/**\n * @param {string} message\n * @see https://developer.mozilla.org/en-US/docs/Web/API/SubtleCrypto/digest#basic_example\n */\nasync function sha256Digest(message) {\n  const encoder = new TextEncoder();\n  const data = encoder.encode(message);\n  if (typeof crypto === \"undefined\" || typeof crypto.subtle === \"undefined\") {\n    throw new Error(\"Web Crypto API is not available.\");\n  }\n  return await crypto.subtle.digest(\"SHA-256\", data);\n}\n\n/**\n * @param {string} token\n */\nfunction decodeToken(token) {\n  const [header, payload] = token.split(\".\");\n  if (typeof payload !== \"string\") {\n    throw new Error(\"Unable to decode token, payload not found.\");\n  }\n  let decoded;\n  try {\n    decoded = base64UrlDecode(payload);\n  } catch (error) {\n    throw new Error(\"Unable to decode token, payload is not a valid Base64URL value.\", {\n      cause: error\n    });\n  }\n  try {\n    return JSON.parse(decoded);\n  } catch (error) {\n    throw new Error(\"Unable to decode token, payload is not a valid JSON value.\", {\n      cause: error\n    });\n  }\n}\n\n/**\n * @param {string} input\n */\nfunction base64UrlDecode(input) {\n  let output = input.replaceAll(\"-\", \"+\").replaceAll(\"_\", \"/\");\n  switch (output.length % 4) {\n    case 0:\n      break;\n    case 2:\n      output += \"==\";\n      break;\n    case 3:\n      output += \"=\";\n      break;\n    default:\n      throw new Error(\"Input is not of the correct length.\");\n  }\n  try {\n    return b64DecodeUnicode(output);\n  } catch (error) {\n    return atob(output);\n  }\n}\n\n/**\n * @param {string} input\n */\nfunction b64DecodeUnicode(input) {\n  return decodeURIComponent(atob(input).replace(/(.)/g, (m, p) => {\n    let code = p.charCodeAt(0).toString(16).toUpperCase();\n    if (code.length < 2) {\n      code = \"0\" + code;\n    }\n    return \"%\" + code;\n  }));\n}\n\n/**\n * Check if the input is an object that can be operated on.\n * @param {unknown} input\n */\nfunction isObject(input) {\n  return typeof input === 'object' && input !== null;\n}", "import * as i0 from '@angular/core';\nimport { Injectable, NgModule, signal, InjectionToken, inject, effect, Directive, Input, computed, PLATFORM_ID, provideAppInitializer, EnvironmentInjector, runInInjectionContext, makeEnvironmentProviders } from '@angular/core';\nimport { HttpHeaders, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { Subject, from, combineLatest, of, fromEvent, mergeMap as mergeMap$1 } from 'rxjs';\nimport { map, mergeMap, debounceTime, takeUntil } from 'rxjs/operators';\nimport Keycloak from 'keycloak-js';\nimport { CommonModule, isPlatformBrowser } from '@angular/common';\n\n/**\n * @license\n * Copyright Mauri<PERSON>emelli Vigolo and contributors.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * Keycloak event types, as described at the keycloak-js documentation:\n * https://www.keycloak.org/docs/latest/securing_apps/index.html#callback-events\n *\n * @deprecated Keycloak Event based on the KeycloakService is deprecated and\n * will be removed in future versions.\n * Use the new `KEYCLOAK_EVENT_SIGNAL` injection token to listen for the keycloak\n * events.\n * More info: https://github.com/mauriciovigolo/keycloak-angular/docs/migration-guides/v19.md\n */\nvar KeycloakEventTypeLegacy;\n(function (KeycloakEventTypeLegacy) {\n  /**\n   * Called if there was an error during authentication.\n   */\n  KeycloakEventTypeLegacy[KeycloakEventTypeLegacy[\"OnAuthError\"] = 0] = \"OnAuthError\";\n  /**\n   * Called if the user is logged out\n   * (will only be called if the session status iframe is enabled, or in Cordova mode).\n   */\n  KeycloakEventTypeLegacy[KeycloakEventTypeLegacy[\"OnAuthLogout\"] = 1] = \"OnAuthLogout\";\n  /**\n   * Called if there was an error while trying to refresh the token.\n   */\n  KeycloakEventTypeLegacy[KeycloakEventTypeLegacy[\"OnAuthRefreshError\"] = 2] = \"OnAuthRefreshError\";\n  /**\n   * Called when the token is refreshed.\n   */\n  KeycloakEventTypeLegacy[KeycloakEventTypeLegacy[\"OnAuthRefreshSuccess\"] = 3] = \"OnAuthRefreshSuccess\";\n  /**\n   * Called when a user is successfully authenticated.\n   */\n  KeycloakEventTypeLegacy[KeycloakEventTypeLegacy[\"OnAuthSuccess\"] = 4] = \"OnAuthSuccess\";\n  /**\n   * Called when the adapter is initialized.\n   */\n  KeycloakEventTypeLegacy[KeycloakEventTypeLegacy[\"OnReady\"] = 5] = \"OnReady\";\n  /**\n   * Called when the access token is expired. If a refresh token is available the token\n   * can be refreshed with updateToken, or in cases where it is not (that is, with implicit flow)\n   * you can redirect to login screen to obtain a new access token.\n   */\n  KeycloakEventTypeLegacy[KeycloakEventTypeLegacy[\"OnTokenExpired\"] = 6] = \"OnTokenExpired\";\n  /**\n   * Called when a AIA has been requested by the application.\n   */\n  KeycloakEventTypeLegacy[KeycloakEventTypeLegacy[\"OnActionUpdate\"] = 7] = \"OnActionUpdate\";\n})(KeycloakEventTypeLegacy || (KeycloakEventTypeLegacy = {}));\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo and contributors.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * A simple guard implementation out of the box. This class should be inherited and\n * implemented by the application. The only method that should be implemented is #isAccessAllowed.\n * The reason for this is that the authorization flow is usually not unique, so in this way you will\n * have more freedom to customize your authorization flow.\n *\n * @deprecated Class based guards are deprecated in Keycloak Angular and will be removed in future versions.\n * Use the new `createAuthGuard` function to create a Guard for your application.\n * More info: https://github.com/mauriciovigolo/keycloak-angular/docs/migration-guides/v19.md\n */\nclass KeycloakAuthGuard {\n  constructor(router, keycloakAngular) {\n    this.router = router;\n    this.keycloakAngular = keycloakAngular;\n  }\n  /**\n   * CanActivate checks if the user is logged in and get the full list of roles (REALM + CLIENT)\n   * of the logged user. This values are set to authenticated and roles params.\n   *\n   * @param route\n   * @param state\n   */\n  async canActivate(route, state) {\n    try {\n      this.authenticated = await this.keycloakAngular.isLoggedIn();\n      this.roles = await this.keycloakAngular.getUserRoles(true);\n      return await this.isAccessAllowed(route, state);\n    } catch (error) {\n      throw new Error('An error happened during access validation. Details:' + error);\n    }\n  }\n}\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo and contributors.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * Service to expose existent methods from the Keycloak JS adapter, adding new\n * functionalities to improve the use of keycloak in Angular v > 4.3 applications.\n *\n * This class should be injected in the application bootstrap, so the same instance will be used\n * along the web application.\n *\n * @deprecated This service is deprecated and will be removed in future versions.\n * Use the new `provideKeycloak` function to load Keycloak in an Angular application.\n * More info: https://github.com/mauriciovigolo/keycloak-angular/docs/migration-guides/v19.md\n */\nclass KeycloakService {\n  constructor() {\n    /**\n     * Observer for the keycloak events\n     */\n    this._keycloakEvents$ = new Subject();\n  }\n  /**\n   * Binds the keycloak-js events to the keycloakEvents Subject\n   * which is a good way to monitor for changes, if needed.\n   *\n   * The keycloakEvents returns the keycloak-js event type and any\n   * argument if the source function provides any.\n   */\n  bindsKeycloakEvents() {\n    this._instance.onAuthError = errorData => {\n      this._keycloakEvents$.next({\n        args: errorData,\n        type: KeycloakEventTypeLegacy.OnAuthError\n      });\n    };\n    this._instance.onAuthLogout = () => {\n      this._keycloakEvents$.next({\n        type: KeycloakEventTypeLegacy.OnAuthLogout\n      });\n    };\n    this._instance.onAuthRefreshSuccess = () => {\n      this._keycloakEvents$.next({\n        type: KeycloakEventTypeLegacy.OnAuthRefreshSuccess\n      });\n    };\n    this._instance.onAuthRefreshError = () => {\n      this._keycloakEvents$.next({\n        type: KeycloakEventTypeLegacy.OnAuthRefreshError\n      });\n    };\n    this._instance.onAuthSuccess = () => {\n      this._keycloakEvents$.next({\n        type: KeycloakEventTypeLegacy.OnAuthSuccess\n      });\n    };\n    this._instance.onTokenExpired = () => {\n      this._keycloakEvents$.next({\n        type: KeycloakEventTypeLegacy.OnTokenExpired\n      });\n    };\n    this._instance.onActionUpdate = state => {\n      this._keycloakEvents$.next({\n        args: state,\n        type: KeycloakEventTypeLegacy.OnActionUpdate\n      });\n    };\n    this._instance.onReady = authenticated => {\n      this._keycloakEvents$.next({\n        args: authenticated,\n        type: KeycloakEventTypeLegacy.OnReady\n      });\n    };\n  }\n  /**\n   * Loads all bearerExcludedUrl content in a uniform type: ExcludedUrl,\n   * so it becomes easier to handle.\n   *\n   * @param bearerExcludedUrls array of strings or ExcludedUrl that includes\n   * the url and HttpMethod.\n   */\n  loadExcludedUrls(bearerExcludedUrls) {\n    const excludedUrls = [];\n    for (const item of bearerExcludedUrls) {\n      let excludedUrl;\n      if (typeof item === 'string') {\n        excludedUrl = {\n          urlPattern: new RegExp(item, 'i'),\n          httpMethods: []\n        };\n      } else {\n        excludedUrl = {\n          urlPattern: new RegExp(item.url, 'i'),\n          httpMethods: item.httpMethods\n        };\n      }\n      excludedUrls.push(excludedUrl);\n    }\n    return excludedUrls;\n  }\n  /**\n   * Handles the class values initialization.\n   *\n   * @param options\n   */\n  initServiceValues({\n    enableBearerInterceptor = true,\n    loadUserProfileAtStartUp = false,\n    bearerExcludedUrls = [],\n    authorizationHeaderName = 'Authorization',\n    bearerPrefix = 'Bearer',\n    initOptions,\n    updateMinValidity = 20,\n    shouldAddToken = () => true,\n    shouldUpdateToken = () => true\n  }) {\n    this._enableBearerInterceptor = enableBearerInterceptor;\n    this._loadUserProfileAtStartUp = loadUserProfileAtStartUp;\n    this._authorizationHeaderName = authorizationHeaderName;\n    this._bearerPrefix = bearerPrefix.trim().concat(' ');\n    this._excludedUrls = this.loadExcludedUrls(bearerExcludedUrls);\n    this._silentRefresh = initOptions ? initOptions.flow === 'implicit' : false;\n    this._updateMinValidity = updateMinValidity;\n    this.shouldAddToken = shouldAddToken;\n    this.shouldUpdateToken = shouldUpdateToken;\n  }\n  /**\n   * Keycloak initialization. It should be called to initialize the adapter.\n   * Options is an object with 2 main parameters: config and initOptions. The first one\n   * will be used to create the Keycloak instance. The second one are options to initialize the\n   * keycloak instance.\n   *\n   * @param options\n   * Config: may be a string representing the keycloak URI or an object with the\n   * following content:\n   * - url: Keycloak json URL\n   * - realm: realm name\n   * - clientId: client id\n   *\n   * initOptions:\n   * Options to initialize the Keycloak adapter, matches the options as provided by Keycloak itself.\n   *\n   * enableBearerInterceptor:\n   * Flag to indicate if the bearer will added to the authorization header.\n   *\n   * loadUserProfileInStartUp:\n   * Indicates that the user profile should be loaded at the keycloak initialization,\n   * just after the login.\n   *\n   * bearerExcludedUrls:\n   * String Array to exclude the urls that should not have the Authorization Header automatically\n   * added.\n   *\n   * authorizationHeaderName:\n   * This value will be used as the Authorization Http Header name.\n   *\n   * bearerPrefix:\n   * This value will be included in the Authorization Http Header param.\n   *\n   * tokenUpdateExcludedHeaders:\n   * Array of Http Header key/value maps that should not trigger the token to be updated.\n   *\n   * updateMinValidity:\n   * This value determines if the token will be refreshed based on its expiration time.\n   *\n   * @returns\n   * A Promise with a boolean indicating if the initialization was successful.\n   */\n  async init(options = {}) {\n    this.initServiceValues(options);\n    const {\n      config,\n      initOptions\n    } = options;\n    this._instance = new Keycloak(config);\n    this.bindsKeycloakEvents();\n    const authenticated = await this._instance.init(initOptions);\n    if (authenticated && this._loadUserProfileAtStartUp) {\n      await this.loadUserProfile();\n    }\n    return authenticated;\n  }\n  /**\n   * Redirects to login form on (options is an optional object with redirectUri and/or\n   * prompt fields).\n   *\n   * @param options\n   * Object, where:\n   *  - redirectUri: Specifies the uri to redirect to after login.\n   *  - prompt:By default the login screen is displayed if the user is not logged-in to Keycloak.\n   * To only authenticate to the application if the user is already logged-in and not display the\n   * login page if the user is not logged-in, set this option to none. To always require\n   * re-authentication and ignore SSO, set this option to login .\n   *  - maxAge: Used just if user is already authenticated. Specifies maximum time since the\n   * authentication of user happened. If user is already authenticated for longer time than\n   * maxAge, the SSO is ignored and he will need to re-authenticate again.\n   *  - loginHint: Used to pre-fill the username/email field on the login form.\n   *  - action: If value is 'register' then user is redirected to registration page, otherwise to\n   * login page.\n   *  - locale: Specifies the desired locale for the UI.\n   * @returns\n   * A void Promise if the login is successful and after the user profile loading.\n   */\n  async login(options = {}) {\n    await this._instance.login(options);\n    if (this._loadUserProfileAtStartUp) {\n      await this.loadUserProfile();\n    }\n  }\n  /**\n   * Redirects to logout.\n   *\n   * @param redirectUri\n   * Specifies the uri to redirect to after logout.\n   * @returns\n   * A void Promise if the logout was successful, cleaning also the userProfile.\n   */\n  async logout(redirectUri) {\n    const options = {\n      redirectUri\n    };\n    await this._instance.logout(options);\n    this._userProfile = undefined;\n  }\n  /**\n   * Redirects to registration form. Shortcut for login with option\n   * action = 'register'. Options are same as for the login method but 'action' is set to\n   * 'register'.\n   *\n   * @param options\n   * login options\n   * @returns\n   * A void Promise if the register flow was successful.\n   */\n  async register(options = {\n    action: 'register'\n  }) {\n    await this._instance.register(options);\n  }\n  /**\n   * Check if the user has access to the specified role. It will look for roles in\n   * realm and the given resource, but will not check if the user is logged in for better performance.\n   *\n   * @param role\n   * role name\n   * @param resource\n   * resource name. If not specified, `clientId` is used\n   * @returns\n   * A boolean meaning if the user has the specified Role.\n   */\n  isUserInRole(role, resource) {\n    let hasRole;\n    hasRole = this._instance.hasResourceRole(role, resource);\n    if (!hasRole) {\n      hasRole = this._instance.hasRealmRole(role);\n    }\n    return hasRole;\n  }\n  /**\n   * Return the roles of the logged user. The realmRoles parameter, with default value\n   * true, will return the resource roles and realm roles associated with the logged user. If set to false\n   * it will only return the resource roles. The resource parameter, if specified, will return only resource roles\n   * associated with the given resource.\n   *\n   * @param realmRoles\n   * Set to false to exclude realm roles (only client roles)\n   * @param resource\n   * resource name If not specified, returns roles from all resources\n   * @returns\n   * Array of Roles associated with the logged user.\n   */\n  getUserRoles(realmRoles = true, resource) {\n    let roles = [];\n    if (this._instance.resourceAccess) {\n      Object.keys(this._instance.resourceAccess).forEach(key => {\n        if (resource && resource !== key) {\n          return;\n        }\n        const resourceAccess = this._instance.resourceAccess[key];\n        const clientRoles = resourceAccess['roles'] || [];\n        roles = roles.concat(clientRoles);\n      });\n    }\n    if (realmRoles && this._instance.realmAccess) {\n      const realmRoles = this._instance.realmAccess['roles'] || [];\n      roles.push(...realmRoles);\n    }\n    return roles;\n  }\n  /**\n   * Check if user is logged in.\n   *\n   * @returns\n   * A boolean that indicates if the user is logged in.\n   */\n  isLoggedIn() {\n    if (!this._instance) {\n      return false;\n    }\n    return this._instance.authenticated;\n  }\n  /**\n   * Returns true if the token has less than minValidity seconds left before\n   * it expires.\n   *\n   * @param minValidity\n   * Seconds left. (minValidity) is optional. Default value is 0.\n   * @returns\n   * Boolean indicating if the token is expired.\n   */\n  isTokenExpired(minValidity = 0) {\n    return this._instance.isTokenExpired(minValidity);\n  }\n  /**\n   * If the token expires within _updateMinValidity seconds the token is refreshed. If the\n   * session status iframe is enabled, the session status is also checked.\n   * Returns a promise telling if the token was refreshed or not. If the session is not active\n   * anymore, the promise is rejected.\n   *\n   * @param minValidity\n   * Seconds left. (minValidity is optional, if not specified updateMinValidity - default 20 is used)\n   * @returns\n   * Promise with a boolean indicating if the token was succesfully updated.\n   */\n  async updateToken(minValidity = this._updateMinValidity) {\n    // TODO: this is a workaround until the silent refresh (issue #43)\n    // is not implemented, avoiding the redirect loop.\n    if (this._silentRefresh) {\n      if (this.isTokenExpired()) {\n        throw new Error('Failed to refresh the token, or the session is expired');\n      }\n      return true;\n    }\n    if (!this._instance) {\n      throw new Error('Keycloak Angular library is not initialized.');\n    }\n    try {\n      return await this._instance.updateToken(minValidity);\n    } catch (error) {\n      return false;\n    }\n  }\n  /**\n   * Loads the user profile.\n   * Returns promise to set functions to be invoked if the profile was loaded\n   * successfully, or if the profile could not be loaded.\n   *\n   * @param forceReload\n   * If true will force the loadUserProfile even if its already loaded.\n   * @returns\n   * A promise with the KeycloakProfile data loaded.\n   */\n  async loadUserProfile(forceReload = false) {\n    if (this._userProfile && !forceReload) {\n      return this._userProfile;\n    }\n    if (!this._instance.authenticated) {\n      throw new Error('The user profile was not loaded as the user is not logged in.');\n    }\n    return this._userProfile = await this._instance.loadUserProfile();\n  }\n  /**\n   * Returns the authenticated token.\n   */\n  async getToken() {\n    return this._instance.token;\n  }\n  /**\n   * Returns the logged username.\n   *\n   * @returns\n   * The logged username.\n   */\n  getUsername() {\n    if (!this._userProfile) {\n      throw new Error('User not logged in or user profile was not loaded.');\n    }\n    return this._userProfile.username;\n  }\n  /**\n   * Clear authentication state, including tokens. This can be useful if application\n   * has detected the session was expired, for example if updating token fails.\n   * Invoking this results in onAuthLogout callback listener being invoked.\n   */\n  clearToken() {\n    this._instance.clearToken();\n  }\n  /**\n   * Adds a valid token in header. The key & value format is:\n   * Authorization Bearer <token>.\n   * If the headers param is undefined it will create the Angular headers object.\n   *\n   * @param headers\n   * Updated header with Authorization and Keycloak token.\n   * @returns\n   * An observable with with the HTTP Authorization header and the current token.\n   */\n  addTokenToHeader(headers = new HttpHeaders()) {\n    return from(this.getToken()).pipe(map(token => token ? headers.set(this._authorizationHeaderName, this._bearerPrefix + token) : headers));\n  }\n  /**\n   * Returns the original Keycloak instance, if you need any customization that\n   * this Angular service does not support yet. Use with caution.\n   *\n   * @returns\n   * The KeycloakInstance from keycloak-js.\n   */\n  getKeycloakInstance() {\n    return this._instance;\n  }\n  /**\n   * @deprecated\n   * Returns the excluded URLs that should not be considered by\n   * the http interceptor which automatically adds the authorization header in the Http Request.\n   *\n   * @returns\n   * The excluded urls that must not be intercepted by the KeycloakBearerInterceptor.\n   */\n  get excludedUrls() {\n    return this._excludedUrls;\n  }\n  /**\n   * Flag to indicate if the bearer will be added to the authorization header.\n   *\n   * @returns\n   * Returns if the bearer interceptor was set to be disabled.\n   */\n  get enableBearerInterceptor() {\n    return this._enableBearerInterceptor;\n  }\n  /**\n   * Keycloak subject to monitor the events triggered by keycloak-js.\n   * The following events as available (as described at keycloak docs -\n   * https://www.keycloak.org/docs/latest/securing_apps/index.html#callback-events):\n   * - OnAuthError\n   * - OnAuthLogout\n   * - OnAuthRefreshError\n   * - OnAuthRefreshSuccess\n   * - OnAuthSuccess\n   * - OnReady\n   * - OnTokenExpire\n   * In each occurrence of any of these, this subject will return the event type,\n   * described at {@link KeycloakEventTypeLegacy} enum and the function args from the keycloak-js\n   * if provided any.\n   *\n   * @returns\n   * A subject with the {@link KeycloakEventLegacy} which describes the event type and attaches the\n   * function args.\n   */\n  get keycloakEvents$() {\n    return this._keycloakEvents$;\n  }\n  static {\n    this.ɵfac = function KeycloakService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || KeycloakService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: KeycloakService,\n      factory: KeycloakService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(KeycloakService, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo and contributors.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * This interceptor includes the bearer by default in all HttpClient requests.\n *\n * If you need to exclude some URLs from adding the bearer, please, take a look\n * at the {@link KeycloakOptions} bearerExcludedUrls property.\n *\n * @deprecated KeycloakBearerInterceptor is deprecated and will be removed in future versions.\n * Use the new functional interceptor such as `includeBearerTokenInterceptor`.\n * More info: https://github.com/mauriciovigolo/keycloak-angular/docs/migration-guides/v19.md\n */\nclass KeycloakBearerInterceptor {\n  constructor(keycloak) {\n    this.keycloak = keycloak;\n  }\n  /**\n   * Calls to update the keycloak token if the request should update the token.\n   *\n   * @param req http request from @angular http module.\n   * @returns\n   * A promise boolean for the token update or noop result.\n   */\n  async conditionallyUpdateToken(req) {\n    if (this.keycloak.shouldUpdateToken(req)) {\n      return await this.keycloak.updateToken();\n    }\n    return true;\n  }\n  /**\n   * @deprecated\n   * Checks if the url is excluded from having the Bearer Authorization\n   * header added.\n   *\n   * @param req http request from @angular http module.\n   * @param excludedUrlRegex contains the url pattern and the http methods,\n   * excluded from adding the bearer at the Http Request.\n   */\n  isUrlExcluded({\n    method,\n    url\n  }, {\n    urlPattern,\n    httpMethods\n  }) {\n    const httpTest = httpMethods.length === 0 || httpMethods.join().indexOf(method.toUpperCase()) > -1;\n    const urlTest = urlPattern.test(url);\n    return httpTest && urlTest;\n  }\n  /**\n   * Intercept implementation that checks if the request url matches the excludedUrls.\n   * If not, adds the Authorization header to the request if the user is logged in.\n   *\n   * @param req\n   * @param next\n   */\n  intercept(req, next) {\n    const {\n      enableBearerInterceptor,\n      excludedUrls\n    } = this.keycloak;\n    if (!enableBearerInterceptor) {\n      return next.handle(req);\n    }\n    const shallPass = !this.keycloak.shouldAddToken(req) || excludedUrls.findIndex(item => this.isUrlExcluded(req, item)) > -1;\n    if (shallPass) {\n      return next.handle(req);\n    }\n    return combineLatest([from(this.conditionallyUpdateToken(req)), of(this.keycloak.isLoggedIn())]).pipe(mergeMap(([_, isLoggedIn]) => isLoggedIn ? this.handleRequestWithTokenHeader(req, next) : next.handle(req)));\n  }\n  /**\n   * Adds the token of the current user to the Authorization header\n   *\n   * @param req\n   * @param next\n   */\n  handleRequestWithTokenHeader(req, next) {\n    return this.keycloak.addTokenToHeader(req.headers).pipe(mergeMap(headersWithBearer => {\n      const kcReq = req.clone({\n        headers: headersWithBearer\n      });\n      return next.handle(kcReq);\n    }));\n  }\n  static {\n    this.ɵfac = function KeycloakBearerInterceptor_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || KeycloakBearerInterceptor)(i0.ɵɵinject(KeycloakService));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: KeycloakBearerInterceptor,\n      factory: KeycloakBearerInterceptor.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(KeycloakBearerInterceptor, [{\n    type: Injectable\n  }], () => [{\n    type: KeycloakService\n  }], null);\n})();\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo and contributors.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * @deprecated NgModules are deprecated in Keycloak Angular and will be removed in future versions.\n * Use the new `provideKeycloak` function to load Keycloak in an Angular application.\n * More info: https://github.com/mauriciovigolo/keycloak-angular/docs/migration-guides/v19.md\n */\nclass CoreModule {\n  static {\n    this.ɵfac = function CoreModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CoreModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: CoreModule,\n      imports: [CommonModule]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [KeycloakService, {\n        provide: HTTP_INTERCEPTORS,\n        useClass: KeycloakBearerInterceptor,\n        multi: true\n      }],\n      imports: [CommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CoreModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      providers: [KeycloakService, {\n        provide: HTTP_INTERCEPTORS,\n        useClass: KeycloakBearerInterceptor,\n        multi: true\n      }]\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo and contributors.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * @deprecated NgModules are deprecated in Keycloak Angular and will be removed in future versions.\n * Use the new `provideKeycloak` function to load Keycloak in an Angular application.\n * More info: https://github.com/mauriciovigolo/keycloak-angular/docs/migration-guides/v19.md\n */\nclass KeycloakAngularModule {\n  static {\n    this.ɵfac = function KeycloakAngularModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || KeycloakAngularModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: KeycloakAngularModule,\n      imports: [CoreModule]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CoreModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(KeycloakAngularModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CoreModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo All Rights Reserved.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n// This legacy implementation will be removed in Keycloak Angular v20\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo All Rights Reserved.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * Keycloak event types, as described at the keycloak-js documentation:\n * https://www.keycloak.org/docs/latest/securing_apps/index.html#callback-events\n */\nvar KeycloakEventType;\n(function (KeycloakEventType) {\n  /**\n   * Keycloak Angular is not initialized yet. This is the initial state applied to the Keycloak Event Signal.\n   * Note: This event is only emitted in Keycloak Angular, it is not part of the keycloak-js.\n   */\n  KeycloakEventType[\"KeycloakAngularNotInitialized\"] = \"KeycloakAngularNotInitialized\";\n  /**\n   * Keycloak Angular is in the process of initializing the providers and Keycloak Instance.\n   * Note: This event is only emitted in Keycloak Angular, it is not part of the keycloak-js.\n   */\n  KeycloakEventType[\"KeycloakAngularInit\"] = \"KeycloakAngularInit\";\n  /**\n   * Triggered if there is an error during authentication.\n   */\n  KeycloakEventType[\"AuthError\"] = \"AuthError\";\n  /**\n   * Triggered when the user logs out. This event will only be triggered\n   * if the session status iframe is enabled or in Cordova mode.\n   */\n  KeycloakEventType[\"AuthLogout\"] = \"AuthLogout\";\n  /**\n   * Triggered if an error occurs while attempting to refresh the token.\n   */\n  KeycloakEventType[\"AuthRefreshError\"] = \"AuthRefreshError\";\n  /**\n   * Triggered when the token is successfully refreshed.\n   */\n  KeycloakEventType[\"AuthRefreshSuccess\"] = \"AuthRefreshSuccess\";\n  /**\n   * Triggered when a user is successfully authenticated.\n   */\n  KeycloakEventType[\"AuthSuccess\"] = \"AuthSuccess\";\n  /**\n   * Triggered when the Keycloak adapter has completed initialization.\n   */\n  KeycloakEventType[\"Ready\"] = \"Ready\";\n  /**\n   * Triggered when the access token expires. Depending on the flow, you may\n   * need to use `updateToken` to refresh the token or redirect the user\n   * to the login screen.\n   */\n  KeycloakEventType[\"TokenExpired\"] = \"TokenExpired\";\n  /**\n   * Triggered when an authentication action is requested by the application.\n   */\n  KeycloakEventType[\"ActionUpdate\"] = \"ActionUpdate\";\n})(KeycloakEventType || (KeycloakEventType = {}));\n/**\n * Helper function to typecast unknown arguments into a specific Keycloak event type.\n *\n * @template T - The expected argument type.\n * @param args - The arguments to be cast.\n * @returns The arguments typed as `T`.\n */\nconst typeEventArgs = args => args;\n/**\n * Creates a signal to manage Keycloak events, initializing the signal with\n * appropriate default values or values from a given Keycloak instance.\n *\n * @param keycloak - An instance of the Keycloak client.\n * @returns A `Signal` that tracks the current Keycloak event state.\n */\nconst createKeycloakSignal = keycloak => {\n  const keycloakSignal = signal({\n    type: KeycloakEventType.KeycloakAngularInit\n  });\n  if (!keycloak) {\n    keycloakSignal.set({\n      type: KeycloakEventType.KeycloakAngularNotInitialized\n    });\n    return keycloakSignal;\n  }\n  keycloak.onReady = authenticated => {\n    keycloakSignal.set({\n      type: KeycloakEventType.Ready,\n      args: authenticated\n    });\n  };\n  keycloak.onAuthError = errorData => {\n    keycloakSignal.set({\n      type: KeycloakEventType.AuthError,\n      args: errorData\n    });\n  };\n  keycloak.onAuthLogout = () => {\n    keycloakSignal.set({\n      type: KeycloakEventType.AuthLogout\n    });\n  };\n  keycloak.onActionUpdate = (status, action) => {\n    keycloakSignal.set({\n      type: KeycloakEventType.ActionUpdate,\n      args: {\n        status,\n        action\n      }\n    });\n  };\n  keycloak.onAuthRefreshError = () => {\n    keycloakSignal.set({\n      type: KeycloakEventType.AuthRefreshError\n    });\n  };\n  keycloak.onAuthRefreshSuccess = () => {\n    keycloakSignal.set({\n      type: KeycloakEventType.AuthRefreshSuccess\n    });\n  };\n  keycloak.onAuthSuccess = () => {\n    keycloakSignal.set({\n      type: KeycloakEventType.AuthSuccess\n    });\n  };\n  keycloak.onTokenExpired = () => {\n    keycloakSignal.set({\n      type: KeycloakEventType.TokenExpired\n    });\n  };\n  return keycloakSignal;\n};\n/**\n * Injection token for the Keycloak events signal, used for dependency injection.\n */\nconst KEYCLOAK_EVENT_SIGNAL = new InjectionToken('Keycloak Events Signal');\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo All Rights Reserved.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * Structural directive to conditionally display elements based on Keycloak user roles.\n *\n * This directive checks if the authenticated user has at least one of the specified roles.\n * Roles can be validated against a specific **resource (client ID)** or the **realm**.\n *\n * ### Features:\n * - Supports role checking in both **resources (client-level roles)** and the **realm**.\n * - Accepts an array of roles to match.\n * - Optional configuration to check realm-level roles.\n *\n * ### Inputs:\n * - `kaHasRoles` (Required): Array of roles to validate.\n * - `resource` (Optional): The client ID or resource name to validate resource-level roles.\n * - `checkRealm` (Optional): A boolean flag to enable realm role validation (default is `false`).\n *\n * ### Requirements:\n * - A Keycloak instance must be injected via Angular's dependency injection.\n * - The user must be authenticated in Keycloak.\n *\n * @example\n * #### Example 1: Check for Global Realm Roles\n * Show the content only if the user has the `admin` or `editor` role in the realm.\n * ```html\n * <div *kaHasRoles=\"['admin', 'editor']; checkRealm:true\">\n *   <p>This content is visible only to users with 'admin' or 'editor' realm roles.</p>\n * </div>\n * ```\n *\n * @example\n * #### Example 2: Check for Resource Roles\n * Show the content only if the user has the `read` or `write` role for a specific resource (`my-client`).\n * ```html\n * <div *kaHasRoles=\"['read', 'write']; resource:'my-client'\">\n *   <p>This content is visible only to users with 'read' or 'write' roles for 'my-client'.</p>\n * </div>\n * ```\n *\n * @example\n * #### Example 3: Check for Both Resource and Realm Roles\n * Show the content if the user has the roles in either the realm or a resource.\n * ```html\n * <div *kaHasRoles=\"['admin', 'write']; resource:'my-client' checkRealm:true\">\n *   <p>This content is visible to users with 'admin' in the realm or 'write' in 'my-client'.</p>\n * </div>\n * ```\n *\n * @example\n * #### Example 4: Fallback Content When Roles Do Not Match\n * Use an `<ng-template>` to display fallback content if the user lacks the required roles.\n * ```html\n * <div *kaHasRoles=\"['admin']; resource:'my-client'\">\n *   <p>Welcome, Admin!</p>\n * </div>\n * <ng-template #noAccess>\n *   <p>Access Denied</p>\n * </ng-template>\n * ```\n */\nclass HasRolesDirective {\n  constructor(templateRef, viewContainer, keycloak) {\n    this.templateRef = templateRef;\n    this.viewContainer = viewContainer;\n    this.keycloak = keycloak;\n    /**\n     * List of roles to validate against the resource or realm.\n     */\n    this.roles = [];\n    /**\n     * Flag to enable realm-level role validation.\n     */\n    this.checkRealm = false;\n    this.viewContainer.clear();\n    const keycloakSignal = inject(KEYCLOAK_EVENT_SIGNAL);\n    effect(() => {\n      const keycloakEvent = keycloakSignal();\n      if (keycloakEvent.type !== KeycloakEventType.Ready) {\n        return;\n      }\n      const authenticated = typeEventArgs(keycloakEvent.args);\n      if (authenticated) {\n        this.render();\n      }\n    });\n  }\n  render() {\n    const hasAccess = this.checkUserRoles();\n    if (hasAccess) {\n      this.viewContainer.createEmbeddedView(this.templateRef);\n    } else {\n      this.viewContainer.clear();\n    }\n  }\n  /**\n   * Checks if the user has at least one of the specified roles in the resource or realm.\n   * @returns True if the user has access, false otherwise.\n   */\n  checkUserRoles() {\n    const hasResourceRole = this.roles.some(role => this.keycloak.hasResourceRole(role, this.resource));\n    const hasRealmRole = this.checkRealm ? this.roles.some(role => this.keycloak.hasRealmRole(role)) : false;\n    return hasResourceRole || hasRealmRole;\n  }\n  static {\n    this.ɵfac = function HasRolesDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HasRolesDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(Keycloak));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: HasRolesDirective,\n      selectors: [[\"\", \"kaHasRoles\", \"\"]],\n      inputs: {\n        roles: [0, \"kaHasRoles\", \"roles\"],\n        resource: [0, \"kaHasRolesResource\", \"resource\"],\n        checkRealm: [0, \"kaHasRolesCheckRealm\", \"checkRealm\"]\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HasRolesDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[kaHasRoles]'\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: Keycloak\n  }], {\n    roles: [{\n      type: Input,\n      args: ['kaHasRoles']\n    }],\n    resource: [{\n      type: Input,\n      args: ['kaHasRolesResource']\n    }],\n    checkRealm: [{\n      type: Input,\n      args: ['kaHasRolesCheckRealm']\n    }]\n  });\n})();\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo All Rights Reserved.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo All Rights Reserved.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * Service to monitor user activity in an Angular application.\n * Tracks user interactions (e.g., mouse movement, touch, key presses, clicks, and scrolls)\n * and updates the last activity timestamp. Consumers can check for user inactivity\n * based on a configurable timeout.\n *\n * The service is supposed to be used in the client context and for safety, it checks during the startup\n * if it is a browser context.\n */\nclass UserActivityService {\n  constructor(ngZone) {\n    this.ngZone = ngZone;\n    /**\n     * Signal to store the timestamp of the last user activity.\n     * The timestamp is represented as the number of milliseconds since epoch.\n     */\n    this.lastActivity = signal(Date.now());\n    /**\n     * Subject to signal the destruction of the service.\n     * Used to clean up RxJS subscriptions.\n     */\n    this.destroy$ = new Subject();\n    /**\n     * Computed signal to expose the last user activity as a read-only signal.\n     */\n    this.lastActivitySignal = computed(() => this.lastActivity());\n  }\n  /**\n   * Starts monitoring user activity events (`mousemove`, `touchstart`, `keydown`, `click`, `scroll`)\n   * and updates the last activity timestamp using RxJS with debounce.\n   * The events are processed outside Angular zone for performance optimization.\n   */\n  startMonitoring() {\n    const isBrowser = isPlatformBrowser(inject(PLATFORM_ID));\n    if (!isBrowser) {\n      return;\n    }\n    this.ngZone.runOutsideAngular(() => {\n      const events = ['mousemove', 'touchstart', 'keydown', 'click', 'scroll'];\n      events.forEach(event => {\n        fromEvent(window, event).pipe(debounceTime(300), takeUntil(this.destroy$)).subscribe(() => this.updateLastActivity());\n      });\n    });\n  }\n  /**\n   * Updates the last activity timestamp to the current time.\n   * This method runs inside Angular's zone to ensure reactivity with Angular signals.\n   */\n  updateLastActivity() {\n    this.ngZone.run(() => {\n      this.lastActivity.set(Date.now());\n    });\n  }\n  /**\n   * Retrieves the timestamp of the last recorded user activity.\n   * @returns {number} The last activity timestamp in milliseconds since epoch.\n   */\n  get lastActivityTime() {\n    return this.lastActivity();\n  }\n  /**\n   * Determines whether the user interacted with the application, meaning it is activily using the application, based on\n   * the specified duration.\n   * @param timeout - The inactivity timeout in milliseconds.\n   * @returns {boolean} `true` if the user is inactive, otherwise `false`.\n   */\n  isActive(timeout) {\n    return Date.now() - this.lastActivityTime < timeout;\n  }\n  /**\n   * Cleans up RxJS subscriptions and resources when the service is destroyed.\n   * This method is automatically called by Angular when the service is removed.\n   */\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function UserActivityService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || UserActivityService)(i0.ɵɵinject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: UserActivityService,\n      factory: UserActivityService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(UserActivityService, [{\n    type: Injectable\n  }], () => [{\n    type: i0.NgZone\n  }], null);\n})();\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo All Rights Reserved.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * Service to automatically manage the Keycloak token refresh process\n * based on user activity and token expiration events. This service\n * integrates with Keycloak for session management and interacts with\n * user activity monitoring to determine the appropriate action when\n * the token expires.\n *\n * The service listens to `KeycloakSignal` for token-related events\n * (e.g., `TokenExpired`) and provides configurable options for\n * session timeout and inactivity handling.\n */\nclass AutoRefreshTokenService {\n  constructor(keycloak, userActivity) {\n    this.keycloak = keycloak;\n    this.userActivity = userActivity;\n    this.options = this.defaultOptions;\n    this.initialized = false;\n    const keycloakSignal = inject(KEYCLOAK_EVENT_SIGNAL);\n    effect(() => {\n      const keycloakEvent = keycloakSignal();\n      if (keycloakEvent.type === KeycloakEventType.TokenExpired) {\n        this.processTokenExpiredEvent();\n      }\n    });\n  }\n  get defaultOptions() {\n    return {\n      sessionTimeout: 300000,\n      onInactivityTimeout: 'logout'\n    };\n  }\n  executeOnInactivityTimeout() {\n    switch (this.options.onInactivityTimeout) {\n      case 'login':\n        this.keycloak.login().catch(error => console.error('Failed to execute the login call', error));\n        break;\n      case 'logout':\n        this.keycloak.logout().catch(error => console.error('Failed to execute the logout call', error));\n        break;\n      default:\n        break;\n    }\n  }\n  processTokenExpiredEvent() {\n    if (!this.initialized || !this.keycloak.authenticated) {\n      return;\n    }\n    if (this.userActivity.isActive(this.options.sessionTimeout)) {\n      this.keycloak.updateToken().catch(() => this.executeOnInactivityTimeout());\n    } else {\n      this.executeOnInactivityTimeout();\n    }\n  }\n  start(options) {\n    this.options = {\n      ...this.defaultOptions,\n      ...options\n    };\n    this.initialized = true;\n    this.userActivity.startMonitoring();\n  }\n  static {\n    this.ɵfac = function AutoRefreshTokenService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AutoRefreshTokenService)(i0.ɵɵinject(Keycloak), i0.ɵɵinject(UserActivityService));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AutoRefreshTokenService,\n      factory: AutoRefreshTokenService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AutoRefreshTokenService, [{\n    type: Injectable\n  }], () => [{\n    type: Keycloak\n  }, {\n    type: UserActivityService\n  }], null);\n})();\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo All Rights Reserved.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * Enables automatic token refresh and session inactivity handling for a\n * Keycloak-enabled Angular application.\n *\n * This function initializes a service that tracks user interactions, such as\n * mouse movements, touches, key presses, clicks, and scrolls. If user activity\n * is detected, it periodically calls `Keycloak.updateToken` to ensure the bearer\n * token remains valid and does not expire.\n *\n * If the session remains inactive beyond the defined `sessionTimeout`, the\n * specified action (`logout`, `login`, or `none`) will be executed. By default,\n * the service will call `keycloak.logout` upon inactivity timeout.\n *\n * Event tracking uses RxJS observables with a debounce of 300 milliseconds to\n * monitor user interactions. When the Keycloak `OnTokenExpired` event occurs,\n * the service checks the user's last activity timestamp. If the user has been\n * active within the session timeout period, it refreshes the token using `updateToken`.\n *\n *\n * @param options - Configuration options for the auto-refresh token feature.\n *   - `sessionTimeout` (optional): The duration in milliseconds after which\n *     the session is considered inactive. Defaults to `300000` (5 minutes).\n *   - `onInactivityTimeout` (optional): The action to take when session inactivity\n *     exceeds the specified timeout. Defaults to `'logout'`.\n *       - `'login'`: Execute `keycloak.login` function.\n *       - `'logout'`: Logs the user out by calling `keycloak.logout`.\n *       - `'none'`: No action is taken.\n *\n * @returns A `KeycloakFeature` instance that configures and enables the\n * auto-refresh token functionality.\n */\nfunction withAutoRefreshToken(options) {\n  return {\n    configure: () => {\n      const autoRefreshTokenService = inject(AutoRefreshTokenService);\n      autoRefreshTokenService.start(options);\n    }\n  };\n}\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo All Rights Reserved.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\nconst mapResourceRoles = (resourceAccess = {}) => {\n  return Object.entries(resourceAccess).reduce((roles, [key, value]) => {\n    roles[key] = value.roles;\n    return roles;\n  }, {});\n};\n/**\n * Creates a custom authorization guard for Angular routes, enabling fine-grained access control.\n *\n * This guard invokes the provided `isAccessAllowed` function to determine if access is permitted\n * based on the current route, router state, and user's authentication and roles data.\n *\n * @template T - The type of the guard function (`CanActivateFn` or `CanActivateChildFn`).\n * @param isAccessAllowed - A callback function that evaluates access conditions. The function receives:\n *   - `route`: The current `ActivatedRouteSnapshot` for the route being accessed.\n *   - `state`: The current `RouterStateSnapshot` representing the router's state.\n *   - `authData`: An `AuthGuardData` object containing the user's authentication status, roles, and Keycloak instance.\n * @returns A guard function of type `T` that can be used as a route `canActivate` or `canActivateChild` guard.\n *\n * @example\n * ```ts\n * import { createAuthGuard } from './auth-guard';\n * import { Routes } from '@angular/router';\n *\n * const isUserAllowed = async (route, state, authData) => {\n *   const { authenticated, grantedRoles } = authData;\n *   return authenticated && grantedRoles.realmRoles.includes('admin');\n * };\n *\n * const routes: Routes = [\n *   {\n *     path: 'admin',\n *     canActivate: [createAuthGuard(isUserAllowed)],\n *     component: AdminComponent,\n *   },\n * ];\n * ```\n */\nconst createAuthGuard = isAccessAllowed => {\n  return (next, state) => {\n    const keycloak = inject(Keycloak);\n    const authenticated = keycloak?.authenticated ?? false;\n    const grantedRoles = {\n      resourceRoles: mapResourceRoles(keycloak?.resourceAccess),\n      realmRoles: keycloak?.realmAccess?.roles ?? []\n    };\n    const authData = {\n      authenticated,\n      keycloak,\n      grantedRoles\n    };\n    return isAccessAllowed(next, state, authData);\n  };\n};\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo All Rights Reserved.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * Default value for the authorization header prefix, used to construct the Authorization token.\n */\nconst BEARER_PREFIX = 'Bearer';\n/**\n * Default name of the authorization header.\n */\nconst AUTHORIZATION_HEADER_NAME = 'Authorization';\n/**\n * Generic factory function to create an interceptor condition with default values.\n *\n * This utility allows you to define custom interceptor conditions while ensuring that\n * default values are applied to any missing fields. By using generics, you can enforce\n * strong typing when creating the fields for the interceptor condition, enhancing type safety.\n *\n * @template T - A type that extends `AuthBearerCondition`.\n * @param value - An object of type `T` (extending `AuthBearerCondition`) to be enhanced with default values.\n * @returns A new object of type `T` with default values assigned to any undefined properties.\n */\nconst createInterceptorCondition = value => ({\n  ...value,\n  bearerPrefix: value.bearerPrefix ?? BEARER_PREFIX,\n  authorizationHeaderName: value.authorizationHeaderName ?? AUTHORIZATION_HEADER_NAME,\n  shouldUpdateToken: value.shouldUpdateToken ?? (() => true)\n});\n/**\n * Conditionally updates the Keycloak token based on the provided request and conditions.\n *\n * @param req - The `HttpRequest` object being processed.\n * @param keycloak - The Keycloak instance managing authentication.\n * @param condition - An `AuthBearerCondition` object with the `shouldUpdateToken` function.\n * @returns A `Promise<boolean>` indicating whether the token was successfully updated.\n */\nconst conditionallyUpdateToken = async (req, keycloak, {\n  shouldUpdateToken = _ => true\n}) => {\n  if (shouldUpdateToken(req)) {\n    return await keycloak.updateToken().catch(() => false);\n  }\n  return true;\n};\n/**\n * Adds the Authorization header to an HTTP request and forwards it to the next handler.\n *\n * @param req - The original `HttpRequest` object.\n * @param next - The `HttpHandlerFn` function for forwarding the HTTP request.\n * @param keycloak - The Keycloak instance providing the authentication token.\n * @param condition - An `AuthBearerCondition` object specifying header configuration.\n * @returns An `Observable<HttpEvent<unknown>>` representing the HTTP response.\n */\nconst addAuthorizationHeader = (req, next, keycloak, condition) => {\n  const {\n    bearerPrefix = BEARER_PREFIX,\n    authorizationHeaderName = AUTHORIZATION_HEADER_NAME\n  } = condition;\n  const clonedRequest = req.clone({\n    setHeaders: {\n      [authorizationHeaderName]: `${bearerPrefix} ${keycloak.token}`\n    }\n  });\n  return next(clonedRequest);\n};\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo All Rights Reserved.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * Injection token for configuring the `customBearerTokenInterceptor`.\n *\n * This injection token holds an array of `CustomBearerTokenCondition` objects, which define\n * the conditions under which a Bearer token should be included in the `Authorization` header\n * of outgoing HTTP requests. Each condition provides a `shouldAddToken` function that dynamically\n * determines whether the token should be added based on the request, handler, and Keycloak state.\n */\nconst CUSTOM_BEARER_TOKEN_INTERCEPTOR_CONFIG = new InjectionToken('Include the bearer token as implemented by the provided function');\n/**\n * Custom HTTP Interceptor for dynamically adding a Bearer token to requests based on conditions.\n *\n * This interceptor uses a flexible approach where the decision to include a Bearer token in the\n * `Authorization` HTTP header is determined by a user-provided function (`shouldAddToken`).\n * This enables a dynamic and granular control over when tokens are added to HTTP requests.\n *\n * ### Key Features:\n * 1. **Dynamic Token Inclusion**: Uses a condition function (`shouldAddToken`) to decide dynamically\n *    whether to add the token based on the request, Keycloak state, and other factors.\n * 2. **Token Management**: Optionally refreshes the Keycloak token before adding it to the request.\n * 3. **Controlled Authorization**: Adds the Bearer token only when the condition function allows\n *    and the user is authenticated in Keycloak.\n *\n * ### Configuration:\n * The interceptor relies on `CUSTOM_BEARER_TOKEN_INTERCEPTOR_CONFIG`, an injection token that contains\n * an array of `CustomBearerTokenCondition` objects. Each condition specifies a `shouldAddToken` function\n * that determines whether to add the Bearer token for a given request.\n *\n * ### Workflow:\n * 1. Reads the conditions from the `CUSTOM_BEARER_TOKEN_INTERCEPTOR_CONFIG` injection token.\n * 2. Iterates through the conditions and evaluates the `shouldAddToken` function for the request.\n * 3. If a condition matches:\n *    - Optionally refreshes the Keycloak token if needed.\n *    - Adds the Bearer token to the request's `Authorization` header if the user is authenticated.\n * 4. If no conditions match, the request proceeds unchanged.\n *\n * ### Parameters:\n * @param req - The `HttpRequest` object representing the outgoing HTTP request.\n * @param next - The `HttpHandlerFn` for passing the request to the next handler in the chain.\n *\n * @returns An `Observable<HttpEvent<unknown>>` representing the HTTP response.\n *\n * ### Usage Example:\n * ```typescript\n * // Define a custom condition to include the token\n * const customCondition: CustomBearerTokenCondition = {\n *   shouldAddToken: async (req, next, keycloak) => {\n *     // Add token only for requests to the /api endpoint\n *     return req.url.startsWith('/api') && keycloak.authenticated;\n *   },\n * };\n *\n * // Configure the interceptor with the custom condition\n * export const appConfig: ApplicationConfig = {\n *   providers: [\n *     provideHttpClient(withInterceptors([customBearerTokenInterceptor])),\n *     {\n *       provide: CUSTOM_BEARER_TOKEN_INTERCEPTOR_CONFIG,\n *       useValue: [customCondition],\n *     },\n *   ],\n * };\n * ```\n */\nconst customBearerTokenInterceptor = (req, next) => {\n  const conditions = inject(CUSTOM_BEARER_TOKEN_INTERCEPTOR_CONFIG) ?? [];\n  const keycloak = inject(Keycloak);\n  const matchingCondition = conditions.find(async condition => await condition.shouldAddToken(req, next, keycloak));\n  if (!matchingCondition) {\n    return next(req);\n  }\n  return from(conditionallyUpdateToken(req, keycloak, matchingCondition)).pipe(mergeMap$1(() => keycloak.authenticated ? addAuthorizationHeader(req, next, keycloak, matchingCondition) : next(req)));\n};\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo All Rights Reserved.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * Injection token for configuring the `includeBearerTokenInterceptor`, allowing the specification\n * of conditions under which the Bearer token should be included in HTTP request headers.\n *\n * This configuration supports multiple conditions, enabling customization for different URLs.\n * It also provides options to tailor the Bearer prefix and the Authorization header name as needed.\n */\nconst INCLUDE_BEARER_TOKEN_INTERCEPTOR_CONFIG = new InjectionToken('Include the bearer token when explicitly defined int the URL pattern condition');\nconst findMatchingCondition = ({\n  method,\n  url\n}, {\n  urlPattern,\n  httpMethods = []\n}) => {\n  const httpMethodTest = httpMethods.length === 0 || httpMethods.join().indexOf(method.toUpperCase()) > -1;\n  const urlTest = urlPattern.test(url);\n  return httpMethodTest && urlTest;\n};\n/**\n * HTTP Interceptor to include a Bearer token in the Authorization header for specific HTTP requests.\n *\n * This interceptor ensures that a Bearer token is added to outgoing HTTP requests based on explicitly\n * defined conditions. By default, the interceptor does not include the Bearer token unless the request\n * matches the provided configuration (`IncludeBearerTokenCondition`). This approach enhances security\n * by preventing sensitive tokens from being unintentionally sent to unauthorized services.\n *\n * ### Features:\n * 1. **Explicit URL Matching**: The interceptor uses regular expressions to match URLs where the Bearer token should be included.\n * 2. **HTTP Method Filtering**: Optional filtering by HTTP methods (e.g., `GET`, `POST`, `PUT`) to refine the conditions for adding the token.\n * 3. **Token Management**: Ensures the Keycloak token is valid by optionally refreshing it before attaching it to the request.\n * 4. **Controlled Authorization**: Sends the token only for requests where the user is authenticated, and the conditions match.\n *\n * ### Workflow:\n * - Reads conditions from `INCLUDE_BEARER_TOKEN_INTERCEPTOR_CONFIG`, which specifies when the Bearer token should be included.\n * - If a request matches the conditions:\n *   1. The Keycloak token is refreshed if needed.\n *   2. The Bearer token is added to the Authorization header.\n *   3. The modified request is passed to the next handler.\n * - If no conditions match, the request proceeds unchanged.\n *\n * ### Security:\n * By explicitly defining URL patterns and optional HTTP methods, this interceptor prevents the leakage of tokens\n * to unintended endpoints, such as third-party APIs or external services. This is especially critical for applications\n * that interact with both internal and external services.\n *\n * @param req - The `HttpRequest` object representing the outgoing HTTP request.\n * @param next - The `HttpHandlerFn` for passing the request to the next handler in the chain.\n * @returns An `Observable<HttpEvent<unknown>>` representing the asynchronous HTTP response.\n *\n * ### Configuration:\n * The interceptor relies on `INCLUDE_BEARER_TOKEN_INTERCEPTOR_CONFIG`, an injection token that holds\n * an array of `IncludeBearerTokenCondition` objects. Each object defines the conditions for including\n * the Bearer token in the request.\n *\n * #### Example Configuration:\n * ```typescript\n * provideHttpClient(\n *   withInterceptors([includeBearerTokenInterceptor]),\n *   {\n *     provide: INCLUDE_BEARER_TOKEN_INTERCEPTOR_CONFIG,\n *     useValue: [\n *       {\n *         urlPattern: /^https:\\/\\/api\\.internal\\.myapp\\.com\\/.*\\/,\n *         httpMethods: ['GET', 'POST'], // Add the token only for GET and POST methods\n *       },\n *     ],\n *   }\n * );\n * ```\n *\n * ### Example Usage:\n * ```typescript\n * export const appConfig: ApplicationConfig = {\n *   providers: [\n *     provideHttpClient(withInterceptors([includeBearerTokenInterceptor])),\n *     provideZoneChangeDetection({ eventCoalescing: true }),\n *     provideRouter(routes),\n *   ],\n * };\n * ```\n *\n * ### Example Matching Condition:\n * ```typescript\n * {\n *   urlPattern: /^(https:\\/\\/internal\\.mycompany\\.com)(\\/.*)?$/i,\n *   httpMethods: ['GET', 'PUT'], // Optional: Match only specific HTTP methods\n * }\n * ```\n */\nconst includeBearerTokenInterceptor = (req, next) => {\n  const conditions = inject(INCLUDE_BEARER_TOKEN_INTERCEPTOR_CONFIG) ?? [];\n  const matchingCondition = conditions.find(condition => findMatchingCondition(req, condition));\n  if (!matchingCondition) {\n    return next(req);\n  }\n  const keycloak = inject(Keycloak);\n  return from(conditionallyUpdateToken(req, keycloak, matchingCondition)).pipe(mergeMap$1(() => keycloak.authenticated ? addAuthorizationHeader(req, next, keycloak, matchingCondition) : next(req)));\n};\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo All Rights Reserved.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n/**\n * Provides Keycloak initialization logic for the app initializer phase.\n * Ensures Keycloak is initialized and features are configured.\n *\n * @param keycloak - The Keycloak instance.\n * @param options - ProvideKeycloakOptions for configuration.\n * @returns EnvironmentProviders or an empty array if `initOptions` is not provided.\n */\nconst provideKeycloakInAppInitializer = (keycloak, options) => {\n  const {\n    initOptions,\n    features = []\n  } = options;\n  if (!initOptions) {\n    return [];\n  }\n  return provideAppInitializer(async () => {\n    const injector = inject(EnvironmentInjector);\n    runInInjectionContext(injector, () => features.forEach(feature => feature.configure()));\n    await keycloak.init(initOptions).catch(error => console.error('Keycloak initialization failed', error));\n  });\n};\n/**\n * Configures and provides Keycloak as a dependency in an Angular application.\n *\n * This function initializes a Keycloak instance with the provided configuration and\n * optional initialization options. It integrates Keycloak into Angular dependency\n * injection system, allowing easy consumption throughout the application. Additionally,\n * it supports custom providers and Keycloak Angular features.\n *\n * If `initOptions` is not provided, the Keycloak instance will not be automatically initialized.\n * In such cases, the application must call `keycloak.init()` explicitly.\n *\n * @param options - Configuration object for Keycloak:\n *   - `config`: The Keycloak configuration, including the server URL, realm, and client ID.\n *   - `initOptions` (Optional): Initialization options for the Keycloak instance.\n *   - `providers` (Optional): Additional Angular providers to include.\n *   - `features` (Optional): Keycloak Angular features to configure during initialization.\n *\n * @returns An `EnvironmentProviders` object integrating Keycloak setup and additional providers.\n *\n * @example\n * ```ts\n * import { provideKeycloak } from './keycloak.providers';\n * import { bootstrapApplication } from '@angular/platform-browser';\n * import { AppComponent } from './app/app.component';\n *\n * bootstrapApplication(AppComponent, {\n *   providers: [\n *     provideKeycloak({\n *       config: {\n *         url: 'https://auth-server.example.com',\n *         realm: 'my-realm',\n *         clientId: 'my-client',\n *       },\n *       initOptions: {\n *         onLoad: 'login-required',\n *       },\n *     }),\n *   ],\n * });\n * ```\n */\nfunction provideKeycloak(options) {\n  const keycloak = new Keycloak(options.config);\n  const providers = options.providers ?? [];\n  const keycloakSignal = createKeycloakSignal(keycloak);\n  return makeEnvironmentProviders([{\n    provide: KEYCLOAK_EVENT_SIGNAL,\n    useValue: keycloakSignal\n  }, {\n    provide: Keycloak,\n    useValue: keycloak\n  }, ...providers, provideKeycloakInAppInitializer(keycloak, options)]);\n}\n\n/**\n * @license\n * Copyright Mauricio Gemelli Vigolo All Rights Reserved.\n *\n * Use of this source code is governed by a MIT-style license that can be\n * found in the LICENSE file at https://github.com/mauriciovigolo/keycloak-angular/blob/main/LICENSE.md\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AutoRefreshTokenService, CUSTOM_BEARER_TOKEN_INTERCEPTOR_CONFIG, CoreModule, HasRolesDirective, INCLUDE_BEARER_TOKEN_INTERCEPTOR_CONFIG, KEYCLOAK_EVENT_SIGNAL, KeycloakAngularModule, KeycloakAuthGuard, KeycloakBearerInterceptor, KeycloakEventType, KeycloakEventTypeLegacy, KeycloakService, UserActivityService, addAuthorizationHeader, conditionallyUpdateToken, createAuthGuard, createInterceptorCondition, createKeycloakSignal, customBearerTokenInterceptor, includeBearerTokenInterceptor, provideKeycloak, typeEventArgs, withAutoRefreshToken };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA,SAAS,SAAS,QAAQ;AACxB,MAAI,EAAE,gBAAgB,WAAW;AAC/B,UAAM,IAAI,MAAM,wDAAwD;AAAA,EAC1E;AACA,MAAI,OAAO,WAAW,YAAY,CAAC,SAAS,MAAM,GAAG;AACnD,UAAM,IAAI,MAAM,iHAAiH;AAAA,EACnI;AACA,MAAI,SAAS,MAAM,GAAG;AACpB,UAAM,qBAAqB,kBAAkB,SAAS,CAAC,UAAU,IAAI,CAAC,OAAO,SAAS,UAAU;AAChG,eAAW,YAAY,oBAAoB;AACzC,UAAI,CAAC,OAAO,QAAQ,GAAG;AACrB,cAAM,IAAI,MAAM,qDAAqD,QAAQ,aAAa;AAAA,MAC5F;AAAA,IACF;AAAA,EACF;AACA,MAAI,KAAK;AACT,MAAI;AACJ,MAAI,eAAe,CAAC;AACpB,MAAI;AACJ,MAAI,cAAc;AAAA,IAChB,QAAQ;AAAA,IACR,cAAc,CAAC;AAAA,IACf,UAAU;AAAA,EACZ;AACA,KAAG,gBAAgB;AACnB,MAAI,WAAW;AACf,MAAI,UAAU,aAAa,QAAQ,IAAI;AACvC,MAAI,UAAU,aAAa,QAAQ,IAAI;AACvC,MAAI,CAAC,WAAW,iBAAiB;AAC/B,YAAQ,gVAA0V;AAAA,EACpW;AACA,KAAG,OAAO,SAAU,cAAc,CAAC,GAAG;AACpC,QAAI,GAAG,eAAe;AACpB,YAAM,IAAI,MAAM,qDAAqD;AAAA,IACvE;AACA,OAAG,gBAAgB;AACnB,OAAG,gBAAgB;AACnB,sBAAkB,sBAAsB;AACxC,QAAI,WAAW,CAAC,WAAW,WAAW,gBAAgB;AACtD,QAAI,SAAS,QAAQ,YAAY,OAAO,IAAI,IAAI;AAC9C,gBAAU,YAAY,YAAY,OAAO;AAAA,IAC3C,WAAW,OAAO,YAAY,YAAY,UAAU;AAClD,gBAAU,YAAY;AAAA,IACxB,OAAO;AACL,UAAI,OAAO,WAAW,OAAO,SAAS;AACpC,kBAAU,YAAY,SAAS;AAAA,MACjC,OAAO;AACL,kBAAU,YAAY;AAAA,MACxB;AAAA,IACF;AACA,QAAI,OAAO,YAAY,aAAa,aAAa;AAC/C,iBAAW,YAAY;AAAA,IACzB;AACA,QAAI,OAAO,YAAY,qBAAqB,aAAa;AACvD,kBAAY,SAAS,YAAY;AAAA,IACnC;AACA,QAAI,YAAY,0BAA0B;AACxC,kBAAY,WAAW,YAAY;AAAA,IACrC;AACA,QAAI,YAAY,WAAW,kBAAkB;AAC3C,SAAG,gBAAgB;AAAA,IACrB;AACA,QAAI,YAAY,cAAc;AAC5B,UAAI,YAAY,iBAAiB,WAAW,YAAY,iBAAiB,YAAY;AACnF,WAAG,eAAe,YAAY;AAAA,MAChC,OAAO;AACL,cAAM;AAAA,MACR;AAAA,IACF;AACA,QAAI,YAAY,MAAM;AACpB,cAAQ,YAAY,MAAM;AAAA,QACxB,KAAK;AACH,aAAG,eAAe;AAClB;AAAA,QACF,KAAK;AACH,aAAG,eAAe;AAClB;AAAA,QACF,KAAK;AACH,aAAG,eAAe;AAClB;AAAA,QACF;AACE,gBAAM;AAAA,MACV;AACA,SAAG,OAAO,YAAY;AAAA,IACxB;AACA,QAAI,YAAY,YAAY,MAAM;AAChC,SAAG,WAAW,YAAY;AAAA,IAC5B;AACA,QAAI,YAAY,aAAa;AAC3B,SAAG,cAAc,YAAY;AAAA,IAC/B;AACA,QAAI,YAAY,2BAA2B;AACzC,SAAG,4BAA4B,YAAY;AAAA,IAC7C;AACA,QAAI,OAAO,YAAY,2BAA2B,WAAW;AAC3D,SAAG,yBAAyB,YAAY;AAAA,IAC1C,OAAO;AACL,SAAG,yBAAyB;AAAA,IAC9B;AACA,QAAI,OAAO,YAAY,eAAe,aAAa;AACjD,UAAI,YAAY,eAAe,UAAU,YAAY,eAAe,OAAO;AACzE,cAAM,IAAI,UAAU,mEAAmE,YAAY,UAAU,GAAG;AAAA,MAClH;AACA,SAAG,aAAa,YAAY;AAAA,IAC9B,OAAO;AACL,SAAG,aAAa;AAAA,IAClB;AACA,QAAI,OAAO,YAAY,kBAAkB,WAAW;AAClD,SAAG,gBAAgB,YAAY;AAAA,IACjC,OAAO;AACL,SAAG,gBAAgB;AAAA,IACrB;AACA,QAAI,YAAY,iBAAiB,QAAQ;AACvC,SAAG,eAAe;AAAA,IACpB,OAAO;AACL,SAAG,eAAe;AAAA,IACpB;AACA,QAAI,OAAO,YAAY,UAAU,UAAU;AACzC,SAAG,QAAQ,YAAY;AAAA,IACzB;AACA,QAAI,OAAO,YAAY,cAAc,UAAU;AAC7C,SAAG,YAAY,YAAY;AAAA,IAC7B;AACA,QAAI,OAAO,YAAY,0BAA0B,YAAY,YAAY,wBAAwB,GAAG;AAClG,SAAG,wBAAwB,YAAY;AAAA,IACzC,OAAO;AACL,SAAG,wBAAwB;AAAA,IAC7B;AACA,QAAI,CAAC,GAAG,cAAc;AACpB,SAAG,eAAe;AAAA,IACpB;AACA,QAAI,CAAC,GAAG,cAAc;AACpB,SAAG,eAAe;AAClB,SAAG,OAAO;AAAA,IACZ;AACA,QAAI,UAAU,cAAc;AAC5B,QAAI,cAAc,cAAc;AAChC,gBAAY,QAAQ,KAAK,WAAY;AACnC,SAAG,WAAW,GAAG,QAAQ,GAAG,aAAa;AACzC,cAAQ,WAAW,GAAG,aAAa;AAAA,IACrC,CAAC,EAAE,MAAM,SAAU,OAAO;AACxB,cAAQ,SAAS,KAAK;AAAA,IACxB,CAAC;AACD,QAAI,gBAAgB,WAAW;AAC/B,aAAS,SAAS;AAChB,UAAI,UAAU,SAAU,QAAQ;AAC9B,YAAI,CAAC,QAAQ;AACX,kBAAQ,SAAS;AAAA,QACnB;AACA,YAAI,YAAY,QAAQ;AACtB,kBAAQ,SAAS,YAAY;AAAA,QAC/B;AACA,WAAG,MAAM,OAAO,EAAE,KAAK,WAAY;AACjC,sBAAY,WAAW;AAAA,QACzB,CAAC,EAAE,MAAM,SAAU,OAAO;AACxB,sBAAY,SAAS,KAAK;AAAA,QAC5B,CAAC;AAAA,MACH;AACA,UAAI,mBAAmB,WAAkB;AAAA;AACvC,cAAI,OAAO,SAAS,cAAc,QAAQ;AAC1C,cAAI,MAAM,MAAM,GAAG,eAAe;AAAA,YAChC,QAAQ;AAAA,YACR,aAAa,GAAG;AAAA,UAClB,CAAC;AACD,eAAK,aAAa,OAAO,GAAG;AAC5B,eAAK,aAAa,WAAW,yEAAyE;AACtG,eAAK,aAAa,SAAS,2BAA2B;AACtD,eAAK,MAAM,UAAU;AACrB,mBAAS,KAAK,YAAY,IAAI;AAC9B,cAAI,kBAAkB,SAAU,OAAO;AACrC,gBAAI,MAAM,WAAW,OAAO,SAAS,UAAU,KAAK,kBAAkB,MAAM,QAAQ;AAClF;AAAA,YACF;AACA,gBAAI,QAAQ,cAAc,MAAM,IAAI;AACpC,4BAAgB,OAAO,WAAW;AAClC,qBAAS,KAAK,YAAY,IAAI;AAC9B,mBAAO,oBAAoB,WAAW,eAAe;AAAA,UACvD;AACA,iBAAO,iBAAiB,WAAW,eAAe;AAAA,QACpD;AAAA;AACA,UAAI,UAAU,CAAC;AACf,cAAQ,YAAY,QAAQ;AAAA,QAC1B,KAAK;AACH,cAAI,YAAY,QAAQ;AACtB,kCAAsB,EAAE,KAAK,WAAY;AACvC,+BAAiB,EAAE,KAAK,SAAU,WAAW;AAC3C,oBAAI,CAAC,WAAW;AACd,qBAAG,4BAA4B,iBAAiB,IAAI,QAAQ,KAAK;AAAA,gBACnE,OAAO;AACL,8BAAY,WAAW;AAAA,gBACzB;AAAA,cACF,CAAC,EAAE,MAAM,SAAU,OAAO;AACxB,4BAAY,SAAS,KAAK;AAAA,cAC5B,CAAC;AAAA,YACH,CAAC;AAAA,UACH,OAAO;AACL,eAAG,4BAA4B,iBAAiB,IAAI,QAAQ,KAAK;AAAA,UACnE;AACA;AAAA,QACF,KAAK;AACH,kBAAQ,IAAI;AACZ;AAAA,QACF;AACE,gBAAM;AAAA,MACV;AAAA,IACF;AACA,aAAS,cAAc;AACrB,UAAI,WAAW,cAAc,OAAO,SAAS,IAAI;AACjD,UAAI,UAAU;AACZ,eAAO,QAAQ,aAAa,OAAO,QAAQ,OAAO,MAAM,SAAS,MAAM;AAAA,MACzE;AACA,UAAI,YAAY,SAAS,OAAO;AAC9B,eAAO,sBAAsB,EAAE,KAAK,WAAY;AAC9C,0BAAgB,UAAU,WAAW;AAAA,QACvC,CAAC,EAAE,MAAM,SAAU,OAAO;AACxB,sBAAY,SAAS,KAAK;AAAA,QAC5B,CAAC;AAAA,MACH;AACA,UAAI,YAAY,SAAS,YAAY,cAAc;AACjD,iBAAS,YAAY,OAAO,YAAY,cAAc,YAAY,OAAO;AACzE,YAAI,YAAY,QAAQ;AACtB,gCAAsB,EAAE,KAAK,WAAY;AACvC,6BAAiB,EAAE,KAAK,SAAU,WAAW;AAC3C,kBAAI,WAAW;AACb,mBAAG,iBAAiB,GAAG,cAAc;AACrC,4BAAY,WAAW;AACvB,oCAAoB;AAAA,cACtB,OAAO;AACL,4BAAY,WAAW;AAAA,cACzB;AAAA,YACF,CAAC,EAAE,MAAM,SAAU,OAAO;AACxB,0BAAY,SAAS,KAAK;AAAA,YAC5B,CAAC;AAAA,UACH,CAAC;AAAA,QACH,OAAO;AACL,aAAG,YAAY,EAAE,EAAE,KAAK,WAAY;AAClC,eAAG,iBAAiB,GAAG,cAAc;AACrC,wBAAY,WAAW;AAAA,UACzB,CAAC,EAAE,MAAM,SAAU,OAAO;AACxB,eAAG,eAAe,GAAG,YAAY;AACjC,gBAAI,YAAY,QAAQ;AACtB,qBAAO;AAAA,YACT,OAAO;AACL,0BAAY,SAAS,KAAK;AAAA,YAC5B;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,WAAW,YAAY,QAAQ;AAC7B,eAAO;AAAA,MACT,OAAO;AACL,oBAAY,WAAW;AAAA,MACzB;AAAA,IACF;AACA,kBAAc,KAAK,WAAY;AAC7B,8BAAwB,EAAE,KAAK,WAAW,EAAE,MAAM,SAAU,OAAO;AACjE,gBAAQ,SAAS,KAAK;AAAA,MACxB,CAAC;AAAA,IACH,CAAC;AACD,kBAAc,MAAM,SAAU,OAAO;AACnC,cAAQ,SAAS,KAAK;AAAA,IACxB,CAAC;AACD,WAAO,QAAQ;AAAA,EACjB;AACA,KAAG,QAAQ,SAAU,SAAS;AAC5B,WAAO,QAAQ,MAAM,OAAO;AAAA,EAC9B;AACA,WAAS,mBAAmB,KAAK;AAC/B,QAAI,OAAO,WAAW,eAAe,OAAO,OAAO,oBAAoB,aAAa;AAClF,YAAM,IAAI,MAAM,kCAAkC;AAAA,IACpD;AACA,WAAO,OAAO,gBAAgB,IAAI,WAAW,GAAG,CAAC;AAAA,EACnD;AACA,WAAS,qBAAqB,KAAK;AACjC,WAAO,qBAAqB,KAAK,gEAAgE;AAAA,EACnG;AACA,WAAS,qBAAqB,KAAK,UAAU;AAC3C,QAAI,aAAa,mBAAmB,GAAG;AACvC,QAAI,QAAQ,IAAI,MAAM,GAAG;AACzB,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,YAAM,CAAC,IAAI,SAAS,WAAW,WAAW,CAAC,IAAI,SAAS,MAAM;AAAA,IAChE;AACA,WAAO,OAAO,aAAa,MAAM,MAAM,KAAK;AAAA,EAC9C;AACA,WAAe,sBAAsB,YAAY,cAAc;AAAA;AAC7D,UAAI,eAAe,QAAQ;AACzB,cAAM,IAAI,UAAU,4DAA4D,UAAU,IAAI;AAAA,MAChG;AAGA,YAAM,YAAY,IAAI,WAAW,MAAM,aAAa,YAAY,CAAC;AACjE,YAAM,cAAc,cAAc,SAAS,EAAE,QAAQ,OAAO,GAAG,EAAE,QAAQ,OAAO,GAAG,EAAE,QAAQ,OAAO,EAAE;AACtG,aAAO;AAAA,IACT;AAAA;AACA,WAAS,qBAAqB,cAAc;AAC1C,QAAI,SAAS;AAAA,MACX,UAAU;AAAA,QACR,KAAK;AAAA,MACP;AAAA,IACF;AACA,WAAO,KAAK,UAAU,MAAM;AAAA,EAC9B;AACA,KAAG,iBAAiB,SAAgB,SAAS;AAAA;AAC3C,UAAI,QAAQ,WAAW;AACvB,UAAI,QAAQ,WAAW;AACvB,UAAI,cAAc,QAAQ,YAAY,OAAO;AAC7C,UAAI,gBAAgB;AAAA,QAClB;AAAA,QACA;AAAA,QACA,aAAa,mBAAmB,WAAW;AAAA,QAC3C,cAAc;AAAA,MAChB;AACA,UAAI,WAAW,QAAQ,QAAQ;AAC7B,sBAAc,SAAS,QAAQ;AAAA,MACjC;AACA,UAAI;AACJ,UAAI,WAAW,QAAQ,UAAU,YAAY;AAC3C,kBAAU,GAAG,UAAU,SAAS;AAAA,MAClC,OAAO;AACL,kBAAU,GAAG,UAAU,UAAU;AAAA,MACnC;AACA,UAAI,QAAQ,WAAW,QAAQ,SAAS,GAAG;AAC3C,UAAI,CAAC,OAAO;AAEV,gBAAQ;AAAA,MACV,WAAW,MAAM,QAAQ,QAAQ,MAAM,IAAI;AAEzC,gBAAQ,YAAY;AAAA,MACtB;AACA,UAAI,MAAM,UAAU,gBAAgB,mBAAmB,GAAG,QAAQ,IAAI,mBAAmB,mBAAmB,WAAW,IAAI,YAAY,mBAAmB,KAAK,IAAI,oBAAoB,mBAAmB,GAAG,YAAY,IAAI,oBAAoB,mBAAmB,GAAG,YAAY,IAAI,YAAY,mBAAmB,KAAK;AAC3T,UAAI,UAAU;AACZ,cAAM,MAAM,YAAY,mBAAmB,KAAK;AAAA,MAClD;AACA,UAAI,WAAW,QAAQ,QAAQ;AAC7B,eAAO,aAAa,mBAAmB,QAAQ,MAAM;AAAA,MACvD;AACA,UAAI,WAAW,OAAO,QAAQ,WAAW,UAAU;AACjD,eAAO,cAAc,mBAAmB,QAAQ,MAAM;AAAA,MACxD;AACA,UAAI,WAAW,QAAQ,WAAW;AAChC,eAAO,iBAAiB,mBAAmB,QAAQ,SAAS;AAAA,MAC9D;AACA,UAAI,WAAW,QAAQ,SAAS;AAC9B,eAAO,kBAAkB,mBAAmB,QAAQ,OAAO;AAAA,MAC7D;AACA,UAAI,WAAW,QAAQ,UAAU,QAAQ,UAAU,YAAY;AAC7D,eAAO,gBAAgB,mBAAmB,QAAQ,MAAM;AAAA,MAC1D;AACA,UAAI,WAAW,QAAQ,QAAQ;AAC7B,eAAO,iBAAiB,mBAAmB,QAAQ,MAAM;AAAA,MAC3D;AACA,UAAI,WAAW,QAAQ,KAAK;AAC1B,YAAI,kBAAkB,qBAAqB,QAAQ,GAAG;AACtD,eAAO,aAAa,mBAAmB,eAAe;AAAA,MACxD;AACA,UAAI,WAAW,QAAQ,aAAa,GAAG,WAAW;AAChD,eAAO,iBAAiB,mBAAmB,QAAQ,aAAa,GAAG,SAAS;AAAA,MAC9E;AACA,UAAI,GAAG,YAAY;AACjB,YAAI;AACF,gBAAM,eAAe,qBAAqB,EAAE;AAC5C,gBAAM,gBAAgB,MAAM,sBAAsB,GAAG,YAAY,YAAY;AAC7E,wBAAc,mBAAmB;AACjC,iBAAO,qBAAqB;AAC5B,iBAAO,4BAA4B,GAAG;AAAA,QACxC,SAAS,OAAO;AACd,gBAAM,IAAI,MAAM,sCAAsC;AAAA,YACpD,OAAO;AAAA,UACT,CAAC;AAAA,QACH;AAAA,MACF;AACA,sBAAgB,IAAI,aAAa;AACjC,aAAO;AAAA,IACT;AAAA;AACA,KAAG,SAAS,SAAU,SAAS;AAC7B,WAAO,QAAQ,OAAO,OAAO;AAAA,EAC/B;AACA,KAAG,kBAAkB,SAAU,SAAS;AACtC,UAAM,eAAe,SAAS,gBAAgB,GAAG;AACjD,QAAI,iBAAiB,QAAQ;AAC3B,aAAO,GAAG,UAAU,OAAO;AAAA,IAC7B;AACA,QAAI,MAAM,GAAG,UAAU,OAAO,IAAI,gBAAgB,mBAAmB,GAAG,QAAQ,IAAI,+BAA+B,mBAAmB,QAAQ,YAAY,SAAS,KAAK,CAAC;AACzK,QAAI,GAAG,SAAS;AACd,aAAO,oBAAoB,mBAAmB,GAAG,OAAO;AAAA,IAC1D;AACA,WAAO;AAAA,EACT;AACA,KAAG,WAAW,SAAU,SAAS;AAC/B,WAAO,QAAQ,SAAS,OAAO;AAAA,EACjC;AACA,KAAG,oBAAoB,SAAgB,SAAS;AAAA;AAC9C,UAAI,CAAC,SAAS;AACZ,kBAAU,CAAC;AAAA,MACb;AACA,cAAQ,SAAS;AACjB,aAAO,MAAM,GAAG,eAAe,OAAO;AAAA,IACxC;AAAA;AACA,KAAG,mBAAmB,SAAU,SAAS;AACvC,QAAI,QAAQ,YAAY;AACxB,QAAI,MAAM;AACV,QAAI,OAAO,UAAU,aAAa;AAChC,YAAM,QAAQ,uBAA4B,mBAAmB,GAAG,QAAQ,IAAI,mBAAmB,mBAAmB,QAAQ,YAAY,OAAO,CAAC;AAAA,IAChJ;AACA,WAAO;AAAA,EACT;AACA,KAAG,oBAAoB,WAAY;AACjC,WAAO,QAAQ,kBAAkB;AAAA,EACnC;AACA,KAAG,eAAe,SAAU,MAAM;AAChC,QAAI,SAAS,GAAG;AAChB,WAAO,CAAC,CAAC,UAAU,OAAO,MAAM,QAAQ,IAAI,KAAK;AAAA,EACnD;AACA,KAAG,kBAAkB,SAAU,MAAM,UAAU;AAC7C,QAAI,CAAC,GAAG,gBAAgB;AACtB,aAAO;AAAA,IACT;AACA,QAAI,SAAS,GAAG,eAAe,YAAY,GAAG,QAAQ;AACtD,WAAO,CAAC,CAAC,UAAU,OAAO,MAAM,QAAQ,IAAI,KAAK;AAAA,EACnD;AACA,KAAG,kBAAkB,WAAY;AAC/B,QAAI,MAAM,YAAY,IAAI;AAC1B,QAAI,MAAM,IAAI,eAAe;AAC7B,QAAI,KAAK,OAAO,KAAK,IAAI;AACzB,QAAI,iBAAiB,UAAU,kBAAkB;AACjD,QAAI,iBAAiB,iBAAiB,YAAY,GAAG,KAAK;AAC1D,QAAI,UAAU,cAAc;AAC5B,QAAI,qBAAqB,WAAY;AACnC,UAAI,IAAI,cAAc,GAAG;AACvB,YAAI,IAAI,UAAU,KAAK;AACrB,aAAG,UAAU,KAAK,MAAM,IAAI,YAAY;AACxC,kBAAQ,WAAW,GAAG,OAAO;AAAA,QAC/B,OAAO;AACL,kBAAQ,SAAS;AAAA,QACnB;AAAA,MACF;AAAA,IACF;AACA,QAAI,KAAK;AACT,WAAO,QAAQ;AAAA,EACjB;AACA,KAAG,eAAe,WAAY;AAC5B,QAAI,MAAM,GAAG,UAAU,SAAS;AAChC,QAAI,MAAM,IAAI,eAAe;AAC7B,QAAI,KAAK,OAAO,KAAK,IAAI;AACzB,QAAI,iBAAiB,UAAU,kBAAkB;AACjD,QAAI,iBAAiB,iBAAiB,YAAY,GAAG,KAAK;AAC1D,QAAI,UAAU,cAAc;AAC5B,QAAI,qBAAqB,WAAY;AACnC,UAAI,IAAI,cAAc,GAAG;AACvB,YAAI,IAAI,UAAU,KAAK;AACrB,aAAG,WAAW,KAAK,MAAM,IAAI,YAAY;AACzC,kBAAQ,WAAW,GAAG,QAAQ;AAAA,QAChC,OAAO;AACL,kBAAQ,SAAS;AAAA,QACnB;AAAA,MACF;AAAA,IACF;AACA,QAAI,KAAK;AACT,WAAO,QAAQ;AAAA,EACjB;AACA,KAAG,iBAAiB,SAAU,aAAa;AACzC,QAAI,CAAC,GAAG,eAAe,CAAC,GAAG,gBAAgB,GAAG,QAAQ,YAAY;AAChE,YAAM;AAAA,IACR;AACA,QAAI,GAAG,YAAY,MAAM;AACvB,cAAQ,2EAA2E;AACnF,aAAO;AAAA,IACT;AACA,QAAI,YAAY,GAAG,YAAY,KAAK,IAAI,KAAK,MAAK,oBAAI,KAAK,GAAE,QAAQ,IAAI,GAAI,IAAI,GAAG;AACpF,QAAI,aAAa;AACf,UAAI,MAAM,WAAW,GAAG;AACtB,cAAM;AAAA,MACR;AACA,mBAAa;AAAA,IACf;AACA,WAAO,YAAY;AAAA,EACrB;AACA,KAAG,cAAc,SAAU,aAAa;AACtC,QAAI,UAAU,cAAc;AAC5B,QAAI,CAAC,GAAG,cAAc;AACpB,cAAQ,SAAS;AACjB,aAAO,QAAQ;AAAA,IACjB;AACA,kBAAc,eAAe;AAC7B,QAAI,OAAO,WAAY;AACrB,UAAI,eAAe;AACnB,UAAI,eAAe,IAAI;AACrB,uBAAe;AACf,gBAAQ,6CAA6C;AAAA,MACvD,WAAW,CAAC,GAAG,eAAe,GAAG,eAAe,WAAW,GAAG;AAC5D,uBAAe;AACf,gBAAQ,4CAA4C;AAAA,MACtD;AACA,UAAI,CAAC,cAAc;AACjB,gBAAQ,WAAW,KAAK;AAAA,MAC1B,OAAO;AACL,YAAI,SAAS,4CAAiD,GAAG;AACjE,YAAI,MAAM,GAAG,UAAU,MAAM;AAC7B,qBAAa,KAAK,OAAO;AACzB,YAAI,aAAa,UAAU,GAAG;AAC5B,cAAI,MAAM,IAAI,eAAe;AAC7B,cAAI,KAAK,QAAQ,KAAK,IAAI;AAC1B,cAAI,iBAAiB,gBAAgB,mCAAmC;AACxE,cAAI,kBAAkB;AACtB,oBAAU,gBAAgB,mBAAmB,GAAG,QAAQ;AACxD,cAAI,aAAY,oBAAI,KAAK,GAAE,QAAQ;AACnC,cAAI,qBAAqB,WAAY;AACnC,gBAAI,IAAI,cAAc,GAAG;AACvB,kBAAI,IAAI,UAAU,KAAK;AACrB,wBAAQ,4BAA4B;AACpC,6BAAa,aAAY,oBAAI,KAAK,GAAE,QAAQ,KAAK;AACjD,oBAAI,gBAAgB,KAAK,MAAM,IAAI,YAAY;AAC/C,yBAAS,cAAc,cAAc,GAAG,cAAc,eAAe,GAAG,cAAc,UAAU,GAAG,SAAS;AAC5G,mBAAG,wBAAwB,GAAG,qBAAqB;AACnD,yBAAS,IAAI,aAAa,IAAI,GAAG,KAAK,MAAM,IAAI,aAAa,IAAI,GAAG;AAClE,oBAAE,WAAW,IAAI;AAAA,gBACnB;AAAA,cACF,OAAO;AACL,wBAAQ,oCAAoC;AAC5C,oBAAI,IAAI,UAAU,KAAK;AACrB,qBAAG,WAAW;AAAA,gBAChB;AACA,mBAAG,sBAAsB,GAAG,mBAAmB;AAC/C,yBAAS,IAAI,aAAa,IAAI,GAAG,KAAK,MAAM,IAAI,aAAa,IAAI,GAAG;AAClE,oBAAE,SAAS,mGAAmG;AAAA,gBAChH;AAAA,cACF;AAAA,YACF;AAAA,UACF;AACA,cAAI,KAAK,MAAM;AAAA,QACjB;AAAA,MACF;AAAA,IACF;AACA,QAAI,YAAY,QAAQ;AACtB,UAAI,gBAAgB,iBAAiB;AACrC,oBAAc,KAAK,WAAY;AAC7B,aAAK;AAAA,MACP,CAAC,EAAE,MAAM,SAAU,OAAO;AACxB,gBAAQ,SAAS,KAAK;AAAA,MACxB,CAAC;AAAA,IACH,OAAO;AACL,WAAK;AAAA,IACP;AACA,WAAO,QAAQ;AAAA,EACjB;AACA,KAAG,aAAa,WAAY;AAC1B,QAAI,GAAG,OAAO;AACZ,eAAS,MAAM,MAAM,IAAI;AACzB,SAAG,gBAAgB,GAAG,aAAa;AACnC,UAAI,GAAG,eAAe;AACpB,WAAG,MAAM;AAAA,MACX;AAAA,IACF;AAAA,EACF;AACA,WAAS,cAAc;AACrB,QAAI,OAAO,GAAG,kBAAkB,aAAa;AAC3C,UAAI,GAAG,cAAc,OAAO,GAAG,cAAc,SAAS,CAAC,KAAK,KAAK;AAC/D,eAAO,GAAG,gBAAgB,YAAY,mBAAmB,GAAG,KAAK;AAAA,MACnE,OAAO;AACL,eAAO,GAAG,gBAAgB,aAAa,mBAAmB,GAAG,KAAK;AAAA,MACpE;AAAA,IACF,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACA,WAAS,YAAY;AACnB,QAAI,CAAC,OAAO,SAAS,QAAQ;AAC3B,aAAO,OAAO,SAAS,WAAW,OAAO,OAAO,SAAS,YAAY,OAAO,SAAS,OAAO,MAAM,OAAO,SAAS,OAAO;AAAA,IAC3H,OAAO;AACL,aAAO,OAAO,SAAS;AAAA,IACzB;AAAA,EACF;AACA,WAAS,gBAAgB,OAAO,SAAS;AACvC,QAAI,OAAO,MAAM;AACjB,QAAI,QAAQ,MAAM;AAClB,QAAI,SAAS,MAAM;AACnB,QAAI,aAAY,oBAAI,KAAK,GAAE,QAAQ;AACnC,QAAI,MAAM,kBAAkB,GAAG;AAC7B,SAAG,kBAAkB,GAAG,eAAe,MAAM,kBAAkB,GAAG,MAAM,WAAW,CAAC;AAAA,IACtF;AACA,QAAI,OAAO;AACT,UAAI,UAAU,QAAQ;AACpB,YAAI,MAAM,qBAAqB,MAAM,sBAAsB,0BAA0B;AACnF,aAAG,MAAM,MAAM,YAAY;AAAA,QAC7B,OAAO;AACL,cAAI,YAAY;AAAA,YACd;AAAA,YACA,mBAAmB,MAAM;AAAA,UAC3B;AACA,aAAG,eAAe,GAAG,YAAY,SAAS;AAC1C,qBAAW,QAAQ,SAAS,SAAS;AAAA,QACvC;AAAA,MACF,OAAO;AACL,mBAAW,QAAQ,WAAW;AAAA,MAChC;AACA;AAAA,IACF,WAAW,GAAG,QAAQ,eAAe,MAAM,gBAAgB,MAAM,WAAW;AAC1E,kBAAY,MAAM,cAAc,MAAM,MAAM,UAAU,IAAI;AAAA,IAC5D;AACA,QAAI,GAAG,QAAQ,cAAc,MAAM;AACjC,UAAI,SAAS,UAAU,OAAO;AAC9B,UAAI,MAAM,GAAG,UAAU,MAAM;AAC7B,UAAI,MAAM,IAAI,eAAe;AAC7B,UAAI,KAAK,QAAQ,KAAK,IAAI;AAC1B,UAAI,iBAAiB,gBAAgB,mCAAmC;AACxE,gBAAU,gBAAgB,mBAAmB,GAAG,QAAQ;AACxD,gBAAU,mBAAmB,MAAM;AACnC,UAAI,MAAM,kBAAkB;AAC1B,kBAAU,oBAAoB,MAAM;AAAA,MACtC;AACA,UAAI,kBAAkB;AACtB,UAAI,qBAAqB,WAAY;AACnC,YAAI,IAAI,cAAc,GAAG;AACvB,cAAI,IAAI,UAAU,KAAK;AACrB,gBAAI,gBAAgB,KAAK,MAAM,IAAI,YAAY;AAC/C,wBAAY,cAAc,cAAc,GAAG,cAAc,eAAe,GAAG,cAAc,UAAU,GAAG,GAAG,SAAS,UAAU;AAC5H,gCAAoB;AAAA,UACtB,OAAO;AACL,eAAG,eAAe,GAAG,YAAY;AACjC,uBAAW,QAAQ,SAAS;AAAA,UAC9B;AAAA,QACF;AAAA,MACF;AACA,UAAI,KAAK,MAAM;AAAA,IACjB;AACA,aAAS,YAAY,aAAa,cAAc,SAAS,gBAAgB;AACvE,mBAAa,aAAY,oBAAI,KAAK,GAAE,QAAQ,KAAK;AACjD,eAAS,aAAa,cAAc,SAAS,SAAS;AACtD,UAAI,YAAY,GAAG,iBAAiB,GAAG,cAAc,SAAS,MAAM,aAAa;AAC/E,gBAAQ,0CAA0C;AAClD,WAAG,WAAW;AACd,mBAAW,QAAQ,SAAS;AAAA,MAC9B,OAAO;AACL,YAAI,gBAAgB;AAClB,aAAG,iBAAiB,GAAG,cAAc;AACrC,qBAAW,QAAQ,WAAW;AAAA,QAChC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,WAAS,aAAa;AACpB,QAAI,UAAU,cAAc;AAC5B,QAAI;AACJ,QAAI,OAAO,WAAW,UAAU;AAC9B,kBAAY;AAAA,IACd;AACA,aAAS,kBAAkB,mBAAmB;AAC5C,UAAI,CAAC,mBAAmB;AACtB,WAAG,YAAY;AAAA,UACb,WAAW,WAAY;AACrB,mBAAO,YAAY,IAAI;AAAA,UACzB;AAAA,UACA,OAAO,WAAY;AACjB,mBAAO,YAAY,IAAI;AAAA,UACzB;AAAA,UACA,QAAQ,WAAY;AAClB,mBAAO,YAAY,IAAI;AAAA,UACzB;AAAA,UACA,oBAAoB,WAAY;AAC9B,mBAAO,YAAY,IAAI;AAAA,UACzB;AAAA,UACA,yBAAyB,WAAY;AACnC,mBAAO,YAAY,IAAI;AAAA,UACzB;AAAA,UACA,UAAU,WAAY;AACpB,mBAAO,YAAY,IAAI;AAAA,UACzB;AAAA,UACA,UAAU,WAAY;AACpB,mBAAO,YAAY,IAAI;AAAA,UACzB;AAAA,QACF;AAAA,MACF,OAAO;AACL,WAAG,YAAY;AAAA,UACb,WAAW,WAAY;AACrB,mBAAO,kBAAkB;AAAA,UAC3B;AAAA,UACA,OAAO,WAAY;AACjB,mBAAO,kBAAkB;AAAA,UAC3B;AAAA,UACA,QAAQ,WAAY;AAClB,gBAAI,CAAC,kBAAkB,sBAAsB;AAC3C,oBAAM;AAAA,YACR;AACA,mBAAO,kBAAkB;AAAA,UAC3B;AAAA,UACA,oBAAoB,WAAY;AAC9B,gBAAI,CAAC,kBAAkB,sBAAsB;AAC3C,oBAAM;AAAA,YACR;AACA,mBAAO,kBAAkB;AAAA,UAC3B;AAAA,UACA,UAAU,WAAY;AACpB,kBAAM;AAAA,UACR;AAAA,UACA,UAAU,WAAY;AACpB,gBAAI,CAAC,kBAAkB,mBAAmB;AACxC,oBAAM;AAAA,YACR;AACA,mBAAO,kBAAkB;AAAA,UAC3B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAI,WAAW;AACb,UAAI,MAAM,IAAI,eAAe;AAC7B,UAAI,KAAK,OAAO,WAAW,IAAI;AAC/B,UAAI,iBAAiB,UAAU,kBAAkB;AACjD,UAAI,qBAAqB,WAAY;AACnC,YAAI,IAAI,cAAc,GAAG;AACvB,cAAI,IAAI,UAAU,OAAO,WAAW,GAAG,GAAG;AACxC,gBAAIA,UAAS,KAAK,MAAM,IAAI,YAAY;AACxC,eAAG,gBAAgBA,QAAO,iBAAiB;AAC3C,eAAG,QAAQA,QAAO,OAAO;AACzB,eAAG,WAAWA,QAAO,UAAU;AAC/B,8BAAkB,IAAI;AACtB,oBAAQ,WAAW;AAAA,UACrB,OAAO;AACL,oBAAQ,SAAS;AAAA,UACnB;AAAA,QACF;AAAA,MACF;AACA,UAAI,KAAK;AAAA,IACX,OAAO;AACL,SAAG,WAAW,OAAO;AACrB,UAAI,eAAe,OAAO,cAAc;AACxC,UAAI,CAAC,cAAc;AACjB,WAAG,gBAAgB,OAAO;AAC1B,WAAG,QAAQ,OAAO;AAClB,0BAAkB,IAAI;AACtB,gBAAQ,WAAW;AAAA,MACrB,OAAO;AACL,YAAI,OAAO,iBAAiB,UAAU;AACpC,cAAI;AACJ,cAAI,aAAa,OAAO,aAAa,SAAS,CAAC,KAAK,KAAK;AACvD,oCAAwB,eAAe;AAAA,UACzC,OAAO;AACL,oCAAwB,eAAe;AAAA,UACzC;AACA,cAAI,MAAM,IAAI,eAAe;AAC7B,cAAI,KAAK,OAAO,uBAAuB,IAAI;AAC3C,cAAI,iBAAiB,UAAU,kBAAkB;AACjD,cAAI,qBAAqB,WAAY;AACnC,gBAAI,IAAI,cAAc,GAAG;AACvB,kBAAI,IAAI,UAAU,OAAO,WAAW,GAAG,GAAG;AACxC,oBAAI,qBAAqB,KAAK,MAAM,IAAI,YAAY;AACpD,kCAAkB,kBAAkB;AACpC,wBAAQ,WAAW;AAAA,cACrB,OAAO;AACL,wBAAQ,SAAS;AAAA,cACnB;AAAA,YACF;AAAA,UACF;AACA,cAAI,KAAK;AAAA,QACX,OAAO;AACL,4BAAkB,YAAY;AAC9B,kBAAQ,WAAW;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AACA,WAAO,QAAQ;AAAA,EACjB;AACA,WAAS,WAAW,KAAK;AACvB,WAAO,IAAI,UAAU,KAAK,IAAI,gBAAgB,IAAI,YAAY,WAAW,OAAO;AAAA,EAClF;AACA,WAAS,SAAS,OAAO,cAAc,SAAS,WAAW;AACzD,QAAI,GAAG,oBAAoB;AACzB,mBAAa,GAAG,kBAAkB;AAClC,SAAG,qBAAqB;AAAA,IAC1B;AACA,QAAI,cAAc;AAChB,SAAG,eAAe;AAClB,SAAG,qBAAqB,YAAY,YAAY;AAAA,IAClD,OAAO;AACL,aAAO,GAAG;AACV,aAAO,GAAG;AAAA,IACZ;AACA,QAAI,SAAS;AACX,SAAG,UAAU;AACb,SAAG,gBAAgB,YAAY,OAAO;AAAA,IACxC,OAAO;AACL,aAAO,GAAG;AACV,aAAO,GAAG;AAAA,IACZ;AACA,QAAI,OAAO;AACT,SAAG,QAAQ;AACX,SAAG,cAAc,YAAY,KAAK;AAClC,SAAG,YAAY,GAAG,YAAY;AAC9B,SAAG,gBAAgB;AACnB,SAAG,UAAU,GAAG,YAAY;AAC5B,SAAG,cAAc,GAAG,YAAY;AAChC,SAAG,iBAAiB,GAAG,YAAY;AACnC,UAAI,WAAW;AACb,WAAG,WAAW,KAAK,MAAM,YAAY,GAAI,IAAI,GAAG,YAAY;AAAA,MAC9D;AACA,UAAI,GAAG,YAAY,MAAM;AACvB,gBAAQ,wEAAwE,GAAG,WAAW,UAAU;AACxG,YAAI,GAAG,gBAAgB;AACrB,cAAI,aAAa,GAAG,YAAY,KAAK,KAAI,oBAAI,KAAK,GAAE,QAAQ,IAAI,MAAO,GAAG,YAAY;AACtF,kBAAQ,iCAAiC,KAAK,MAAM,YAAY,GAAI,IAAI,IAAI;AAC5E,cAAI,aAAa,GAAG;AAClB,eAAG,eAAe;AAAA,UACpB,OAAO;AACL,eAAG,qBAAqB,WAAW,GAAG,gBAAgB,SAAS;AAAA,UACjE;AAAA,QACF;AAAA,MACF;AAAA,IACF,OAAO;AACL,aAAO,GAAG;AACV,aAAO,GAAG;AACV,aAAO,GAAG;AACV,aAAO,GAAG;AACV,aAAO,GAAG;AACV,SAAG,gBAAgB;AAAA,IACrB;AAAA,EACF;AACA,WAAS,aAAa;AACpB,QAAI,OAAO,WAAW,eAAe,OAAO,OAAO,eAAe,aAAa;AAC7E,YAAM,IAAI,MAAM,kCAAkC;AAAA,IACpD;AACA,WAAO,OAAO,WAAW;AAAA,EAC3B;AACA,WAAS,cAAc,KAAK;AAC1B,QAAI,QAAQ,iBAAiB,GAAG;AAChC,QAAI,CAAC,OAAO;AACV;AAAA,IACF;AACA,QAAI,aAAa,gBAAgB,IAAI,MAAM,KAAK;AAChD,QAAI,YAAY;AACd,YAAM,QAAQ;AACd,YAAM,cAAc,WAAW;AAC/B,YAAM,cAAc,WAAW;AAC/B,YAAM,SAAS,WAAW;AAC1B,YAAM,mBAAmB,WAAW;AACpC,YAAM,eAAe,WAAW;AAAA,IAClC;AACA,WAAO;AAAA,EACT;AACA,WAAS,iBAAiB,KAAK;AAC7B,QAAI;AACJ,YAAQ,GAAG,MAAM;AAAA,MACf,KAAK;AACH,0BAAkB,CAAC,QAAQ,SAAS,iBAAiB,oBAAoB,aAAa,KAAK;AAC3F;AAAA,MACF,KAAK;AACH,0BAAkB,CAAC,gBAAgB,cAAc,YAAY,SAAS,iBAAiB,cAAc,oBAAoB,aAAa,KAAK;AAC3I;AAAA,MACF,KAAK;AACH,0BAAkB,CAAC,gBAAgB,cAAc,YAAY,QAAQ,SAAS,iBAAiB,cAAc,oBAAoB,aAAa,KAAK;AACnJ;AAAA,IACJ;AACA,oBAAgB,KAAK,OAAO;AAC5B,oBAAgB,KAAK,mBAAmB;AACxC,oBAAgB,KAAK,WAAW;AAChC,QAAI,aAAa,IAAI,QAAQ,GAAG;AAChC,QAAI,gBAAgB,IAAI,QAAQ,GAAG;AACnC,QAAI;AACJ,QAAI;AACJ,QAAI,GAAG,iBAAiB,WAAW,eAAe,IAAI;AACpD,eAAS,IAAI,UAAU,GAAG,UAAU;AACpC,eAAS,oBAAoB,IAAI,UAAU,aAAa,GAAG,kBAAkB,KAAK,gBAAgB,IAAI,MAAM,GAAG,eAAe;AAC9H,UAAI,OAAO,iBAAiB,IAAI;AAC9B,kBAAU,MAAM,OAAO;AAAA,MACzB;AACA,UAAI,kBAAkB,IAAI;AACxB,kBAAU,IAAI,UAAU,aAAa;AAAA,MACvC;AAAA,IACF,WAAW,GAAG,iBAAiB,cAAc,kBAAkB,IAAI;AACjE,eAAS,IAAI,UAAU,GAAG,aAAa;AACvC,eAAS,oBAAoB,IAAI,UAAU,gBAAgB,CAAC,GAAG,eAAe;AAC9E,UAAI,OAAO,iBAAiB,IAAI;AAC9B,kBAAU,MAAM,OAAO;AAAA,MACzB;AAAA,IACF;AACA,QAAI,UAAU,OAAO,aAAa;AAChC,UAAI,GAAG,SAAS,cAAc,GAAG,SAAS,UAAU;AAClD,aAAK,OAAO,YAAY,QAAQ,OAAO,YAAY,UAAU,OAAO,YAAY,OAAO;AACrF,iBAAO,YAAY,SAAS;AAC5B,iBAAO,OAAO;AAAA,QAChB;AAAA,MACF,WAAW,GAAG,SAAS,YAAY;AACjC,aAAK,OAAO,YAAY,gBAAgB,OAAO,YAAY,UAAU,OAAO,YAAY,OAAO;AAC7F,iBAAO,YAAY,SAAS;AAC5B,iBAAO,OAAO;AAAA,QAChB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,WAAS,oBAAoB,cAAc,iBAAiB;AAC1D,QAAI,IAAI,aAAa,MAAM,GAAG;AAC9B,QAAI,SAAS;AAAA,MACX,cAAc;AAAA,MACd,aAAa,CAAC;AAAA,IAChB;AACA,aAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,UAAI,QAAQ,EAAE,CAAC,EAAE,QAAQ,GAAG;AAC5B,UAAI,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG,KAAK;AAC7B,UAAI,gBAAgB,QAAQ,GAAG,MAAM,IAAI;AACvC,eAAO,YAAY,GAAG,IAAI,EAAE,CAAC,EAAE,MAAM,QAAQ,CAAC;AAAA,MAChD,OAAO;AACL,YAAI,OAAO,iBAAiB,IAAI;AAC9B,iBAAO,gBAAgB;AAAA,QACzB;AACA,eAAO,gBAAgB,EAAE,CAAC;AAAA,MAC5B;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,WAAS,gBAAgB;AAGvB,QAAI,IAAI;AAAA,MACN,YAAY,SAAU,QAAQ;AAC5B,UAAE,QAAQ,MAAM;AAAA,MAClB;AAAA,MACA,UAAU,SAAU,QAAQ;AAC1B,UAAE,OAAO,MAAM;AAAA,MACjB;AAAA,IACF;AACA,MAAE,UAAU,IAAI,QAAQ,SAAU,SAAS,QAAQ;AACjD,QAAE,UAAU;AACZ,QAAE,SAAS;AAAA,IACb,CAAC;AACD,WAAO;AAAA,EACT;AAGA,WAAS,sBAAsB,SAAS,SAAS,cAAc;AAC7D,QAAI,gBAAgB;AACpB,QAAI,iBAAiB,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC1D,sBAAgB,WAAW,WAAY;AACrC,eAAO;AAAA,UACL,SAAS,gBAAgB,8CAA8C,UAAU;AAAA,QACnF,CAAC;AAAA,MACH,GAAG,OAAO;AAAA,IACZ,CAAC;AACD,WAAO,QAAQ,KAAK,CAAC,SAAS,cAAc,CAAC,EAAE,QAAQ,WAAY;AACjE,mBAAa,aAAa;AAAA,IAC5B,CAAC;AAAA,EACH;AACA,WAAS,wBAAwB;AAC/B,QAAI,UAAU,cAAc;AAC5B,QAAI,CAAC,YAAY,QAAQ;AACvB,cAAQ,WAAW;AACnB,aAAO,QAAQ;AAAA,IACjB;AACA,QAAI,YAAY,QAAQ;AACtB,cAAQ,WAAW;AACnB,aAAO,QAAQ;AAAA,IACjB;AACA,QAAI,SAAS,SAAS,cAAc,QAAQ;AAC5C,gBAAY,SAAS;AACrB,WAAO,SAAS,WAAY;AAC1B,UAAI,UAAU,GAAG,UAAU,UAAU;AACrC,UAAI,QAAQ,OAAO,CAAC,MAAM,KAAK;AAC7B,oBAAY,eAAe,UAAU;AAAA,MACvC,OAAO;AACL,oBAAY,eAAe,QAAQ,UAAU,GAAG,QAAQ,QAAQ,KAAK,CAAC,CAAC;AAAA,MACzE;AACA,cAAQ,WAAW;AAAA,IACrB;AACA,QAAI,MAAM,GAAG,UAAU,mBAAmB;AAC1C,WAAO,aAAa,OAAO,GAAG;AAC9B,WAAO,aAAa,WAAW,yEAAyE;AACxG,WAAO,aAAa,SAAS,yBAAyB;AACtD,WAAO,MAAM,UAAU;AACvB,aAAS,KAAK,YAAY,MAAM;AAChC,QAAI,kBAAkB,SAAU,OAAO;AACrC,UAAI,MAAM,WAAW,YAAY,gBAAgB,YAAY,OAAO,kBAAkB,MAAM,QAAQ;AAClG;AAAA,MACF;AACA,UAAI,EAAE,MAAM,QAAQ,eAAe,MAAM,QAAQ,aAAa,MAAM,QAAQ,UAAU;AACpF;AAAA,MACF;AACA,UAAI,MAAM,QAAQ,aAAa;AAC7B,WAAG,WAAW;AAAA,MAChB;AACA,UAAI,YAAY,YAAY,aAAa,OAAO,GAAG,YAAY,aAAa,MAAM;AAClF,eAAS,IAAI,UAAU,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AAC9C,YAAIC,WAAU,UAAU,CAAC;AACzB,YAAI,MAAM,QAAQ,SAAS;AACzB,UAAAA,SAAQ,SAAS;AAAA,QACnB,OAAO;AACL,UAAAA,SAAQ,WAAW,MAAM,QAAQ,WAAW;AAAA,QAC9C;AAAA,MACF;AAAA,IACF;AACA,WAAO,iBAAiB,WAAW,iBAAiB,KAAK;AACzD,WAAO,QAAQ;AAAA,EACjB;AACA,WAAS,sBAAsB;AAC7B,QAAI,YAAY,QAAQ;AACtB,UAAI,GAAG,OAAO;AACZ,mBAAW,WAAY;AACrB,2BAAiB,EAAE,KAAK,SAAU,WAAW;AAC3C,gBAAI,WAAW;AACb,kCAAoB;AAAA,YACtB;AAAA,UACF,CAAC;AAAA,QACH,GAAG,YAAY,WAAW,GAAI;AAAA,MAChC;AAAA,IACF;AAAA,EACF;AACA,WAAS,mBAAmB;AAC1B,QAAI,UAAU,cAAc;AAC5B,QAAI,YAAY,UAAU,YAAY,cAAc;AAClD,UAAI,MAAM,GAAG,WAAW,OAAO,GAAG,YAAY,GAAG,YAAY;AAC7D,kBAAY,aAAa,KAAK,OAAO;AACrC,UAAI,SAAS,YAAY;AACzB,UAAI,YAAY,aAAa,UAAU,GAAG;AACxC,oBAAY,OAAO,cAAc,YAAY,KAAK,MAAM;AAAA,MAC1D;AAAA,IACF,OAAO;AACL,cAAQ,WAAW;AAAA,IACrB;AACA,WAAO,QAAQ;AAAA,EACjB;AACA,WAAS,0BAA0B;AACjC,QAAI,UAAU,cAAc;AAC5B,SAAK,YAAY,UAAU,GAAG,8BAA8B,OAAO,GAAG,UAAU,4BAA4B,YAAY;AACtH,UAAI,SAAS,SAAS,cAAc,QAAQ;AAC5C,aAAO,aAAa,OAAO,GAAG,UAAU,wBAAwB,CAAC;AACjE,aAAO,aAAa,WAAW,yEAAyE;AACxG,aAAO,aAAa,SAAS,0BAA0B;AACvD,aAAO,MAAM,UAAU;AACvB,eAAS,KAAK,YAAY,MAAM;AAChC,UAAI,kBAAkB,SAAU,OAAO;AACrC,YAAI,OAAO,kBAAkB,MAAM,QAAQ;AACzC;AAAA,QACF;AACA,YAAI,MAAM,SAAS,eAAe,MAAM,SAAS,eAAe;AAC9D;AAAA,QACF,WAAW,MAAM,SAAS,eAAe;AACvC,kBAAQ,wbAAuc;AAC/c,sBAAY,SAAS;AACrB,cAAI,GAAG,wBAAwB;AAC7B,eAAG,4BAA4B;AAAA,UACjC;AAAA,QACF;AACA,iBAAS,KAAK,YAAY,MAAM;AAChC,eAAO,oBAAoB,WAAW,eAAe;AACrD,gBAAQ,WAAW;AAAA,MACrB;AACA,aAAO,iBAAiB,WAAW,iBAAiB,KAAK;AAAA,IAC3D,OAAO;AACL,cAAQ,WAAW;AAAA,IACrB;AACA,WAAO,sBAAsB,QAAQ,SAAS,GAAG,uBAAuB,0DAA0D;AAAA,EACpI;AACA,WAAS,YAAY,MAAM;AACzB,QAAI,CAAC,QAAQ,QAAQ,WAAW;AAC9B,aAAO;AAAA,QACL,OAAO,SAAgB,SAAS;AAAA;AAC9B,mBAAO,SAAS,OAAO,MAAM,GAAG,eAAe,OAAO,CAAC;AACvD,mBAAO,cAAc,EAAE;AAAA,UACzB;AAAA;AAAA,QACA,QAAQ,SAAgB,SAAS;AAAA;AAC/B,kBAAM,eAAe,SAAS,gBAAgB,GAAG;AACjD,gBAAI,iBAAiB,OAAO;AAC1B,qBAAO,SAAS,QAAQ,GAAG,gBAAgB,OAAO,CAAC;AACnD;AAAA,YACF;AAGA,kBAAM,OAAO,SAAS,cAAc,MAAM;AAC1C,iBAAK,aAAa,UAAU,MAAM;AAClC,iBAAK,aAAa,UAAU,GAAG,gBAAgB,OAAO,CAAC;AACvD,iBAAK,MAAM,UAAU;AAGrB,kBAAM,OAAO;AAAA,cACX,eAAe,GAAG;AAAA,cAClB,WAAW,GAAG;AAAA,cACd,0BAA0B,QAAQ,YAAY,SAAS,KAAK;AAAA,YAC9D;AACA,uBAAW,CAAC,MAAM,KAAK,KAAK,OAAO,QAAQ,IAAI,GAAG;AAChD,oBAAM,QAAQ,SAAS,cAAc,OAAO;AAC5C,oBAAM,aAAa,QAAQ,QAAQ;AACnC,oBAAM,aAAa,QAAQ,IAAI;AAC/B,oBAAM,aAAa,SAAS,KAAK;AACjC,mBAAK,YAAY,KAAK;AAAA,YACxB;AAGA,qBAAS,KAAK,YAAY,IAAI;AAC9B,iBAAK,OAAO;AAAA,UACd;AAAA;AAAA,QACA,UAAU,SAAgB,SAAS;AAAA;AACjC,mBAAO,SAAS,OAAO,MAAM,GAAG,kBAAkB,OAAO,CAAC;AAC1D,mBAAO,cAAc,EAAE;AAAA,UACzB;AAAA;AAAA,QACA,mBAAmB,WAAY;AAC7B,cAAI,aAAa,GAAG,iBAAiB;AACrC,cAAI,OAAO,eAAe,aAAa;AACrC,mBAAO,SAAS,OAAO;AAAA,UACzB,OAAO;AACL,kBAAM;AAAA,UACR;AACA,iBAAO,cAAc,EAAE;AAAA,QACzB;AAAA,QACA,aAAa,SAAU,SAAS,YAAY;AAC1C,cAAI,UAAU,UAAU,GAAG;AACzB,yBAAa;AAAA,UACf;AACA,cAAI,WAAW,QAAQ,aAAa;AAClC,mBAAO,QAAQ;AAAA,UACjB,WAAW,GAAG,aAAa;AACzB,mBAAO,GAAG;AAAA,UACZ,OAAO;AACL,mBAAO,SAAS;AAAA,UAClB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAI,QAAQ,WAAW;AACrB,kBAAY,SAAS;AACrB,UAAI,2BAA2B,SAAU,UAAU,QAAQ,SAAS;AAClE,YAAI,OAAO,WAAW,OAAO,QAAQ,cAAc;AAEjD,iBAAO,OAAO,QAAQ,aAAa,KAAK,UAAU,QAAQ,OAAO;AAAA,QACnE,OAAO;AACL,iBAAO,OAAO,KAAK,UAAU,QAAQ,OAAO;AAAA,QAC9C;AAAA,MACF;AACA,UAAI,6BAA6B,SAAU,aAAa;AACtD,YAAI,eAAe,YAAY,gBAAgB;AAC7C,iBAAO,OAAO,KAAK,YAAY,cAAc,EAAE,OAAO,SAAU,SAAS,YAAY;AACnF,oBAAQ,UAAU,IAAI,YAAY,eAAe,UAAU;AAC3D,mBAAO;AAAA,UACT,GAAG,CAAC,CAAC;AAAA,QACP,OAAO;AACL,iBAAO,CAAC;AAAA,QACV;AAAA,MACF;AACA,UAAI,uBAAuB,SAAU,gBAAgB;AACnD,eAAO,OAAO,KAAK,cAAc,EAAE,OAAO,SAAU,SAAS,YAAY;AACvE,kBAAQ,KAAK,aAAa,MAAM,eAAe,UAAU,CAAC;AAC1D,iBAAO;AAAA,QACT,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG;AAAA,MACjB;AACA,UAAI,uBAAuB,SAAU,aAAa;AAChD,YAAI,iBAAiB,2BAA2B,WAAW;AAC3D,uBAAe,WAAW;AAC1B,YAAI,eAAe,YAAY,UAAU,QAAQ;AAC/C,yBAAe,SAAS;AAAA,QAC1B;AACA,eAAO,qBAAqB,cAAc;AAAA,MAC5C;AACA,UAAI,wBAAwB,WAAY;AACtC,eAAO,GAAG,eAAe;AAAA,MAC3B;AACA,aAAO;AAAA,QACL,OAAO,SAAgB,SAAS;AAAA;AAC9B,gBAAI,UAAU,cAAc;AAC5B,gBAAI,iBAAiB,qBAAqB,OAAO;AACjD,gBAAI,WAAW,MAAM,GAAG,eAAe,OAAO;AAC9C,gBAAI,MAAM,yBAAyB,UAAU,UAAU,cAAc;AACrE,gBAAI,YAAY;AAChB,gBAAI,SAAS;AACb,gBAAI,eAAe,WAAY;AAC7B,uBAAS;AACT,kBAAI,MAAM;AAAA,YACZ;AACA,gBAAI,iBAAiB,aAAa,SAAU,OAAO;AACjD,kBAAI,MAAM,IAAI,QAAQ,sBAAsB,CAAC,KAAK,GAAG;AACnD,oBAAI,WAAW,cAAc,MAAM,GAAG;AACtC,gCAAgB,UAAU,OAAO;AACjC,6BAAa;AACb,4BAAY;AAAA,cACd;AAAA,YACF,CAAC;AACD,gBAAI,iBAAiB,aAAa,SAAU,OAAO;AACjD,kBAAI,CAAC,WAAW;AACd,oBAAI,MAAM,IAAI,QAAQ,sBAAsB,CAAC,KAAK,GAAG;AACnD,sBAAI,WAAW,cAAc,MAAM,GAAG;AACtC,kCAAgB,UAAU,OAAO;AACjC,+BAAa;AACb,8BAAY;AAAA,gBACd,OAAO;AACL,0BAAQ,SAAS;AACjB,+BAAa;AAAA,gBACf;AAAA,cACF;AAAA,YACF,CAAC;AACD,gBAAI,iBAAiB,QAAQ,SAAU,OAAO;AAC5C,kBAAI,CAAC,QAAQ;AACX,wBAAQ,SAAS;AAAA,kBACf,QAAQ;AAAA,gBACV,CAAC;AAAA,cACH;AAAA,YACF,CAAC;AACD,mBAAO,QAAQ;AAAA,UACjB;AAAA;AAAA,QACA,QAAQ,SAAU,SAAS;AACzB,cAAI,UAAU,cAAc;AAC5B,cAAI,YAAY,GAAG,gBAAgB,OAAO;AAC1C,cAAI,MAAM,yBAAyB,WAAW,UAAU,uCAAuC;AAC/F,cAAI;AACJ,cAAI,iBAAiB,aAAa,SAAU,OAAO;AACjD,gBAAI,MAAM,IAAI,QAAQ,sBAAsB,CAAC,KAAK,GAAG;AACnD,kBAAI,MAAM;AAAA,YACZ;AAAA,UACF,CAAC;AACD,cAAI,iBAAiB,aAAa,SAAU,OAAO;AACjD,gBAAI,MAAM,IAAI,QAAQ,sBAAsB,CAAC,KAAK,GAAG;AACnD,kBAAI,MAAM;AAAA,YACZ,OAAO;AACL,sBAAQ;AACR,kBAAI,MAAM;AAAA,YACZ;AAAA,UACF,CAAC;AACD,cAAI,iBAAiB,QAAQ,SAAU,OAAO;AAC5C,gBAAI,OAAO;AACT,sBAAQ,SAAS;AAAA,YACnB,OAAO;AACL,iBAAG,WAAW;AACd,sBAAQ,WAAW;AAAA,YACrB;AAAA,UACF,CAAC;AACD,iBAAO,QAAQ;AAAA,QACjB;AAAA,QACA,UAAU,SAAgB,SAAS;AAAA;AACjC,gBAAI,UAAU,cAAc;AAC5B,gBAAI,cAAc,MAAM,GAAG,kBAAkB;AAC7C,gBAAI,iBAAiB,qBAAqB,OAAO;AACjD,gBAAI,MAAM,yBAAyB,aAAa,UAAU,cAAc;AACxE,gBAAI,iBAAiB,aAAa,SAAU,OAAO;AACjD,kBAAI,MAAM,IAAI,QAAQ,sBAAsB,CAAC,KAAK,GAAG;AACnD,oBAAI,MAAM;AACV,oBAAI,QAAQ,cAAc,MAAM,GAAG;AACnC,gCAAgB,OAAO,OAAO;AAAA,cAChC;AAAA,YACF,CAAC;AACD,mBAAO,QAAQ;AAAA,UACjB;AAAA;AAAA,QACA,mBAAmB,WAAY;AAC7B,cAAI,aAAa,GAAG,iBAAiB;AACrC,cAAI,OAAO,eAAe,aAAa;AACrC,gBAAI,MAAM,yBAAyB,YAAY,UAAU,aAAa;AACtE,gBAAI,iBAAiB,aAAa,SAAU,OAAO;AACjD,kBAAI,MAAM,IAAI,QAAQ,sBAAsB,CAAC,KAAK,GAAG;AACnD,oBAAI,MAAM;AAAA,cACZ;AAAA,YACF,CAAC;AAAA,UACH,OAAO;AACL,kBAAM;AAAA,UACR;AAAA,QACF;AAAA,QACA,aAAa,SAAU,SAAS;AAC9B,iBAAO,sBAAsB;AAAA,QAC/B;AAAA,MACF;AAAA,IACF;AACA,QAAI,QAAQ,kBAAkB;AAC5B,kBAAY,SAAS;AACrB,aAAO;AAAA,QACL,OAAO,SAAgB,SAAS;AAAA;AAC9B,gBAAI,UAAU,cAAc;AAC5B,gBAAI,WAAW,MAAM,GAAG,eAAe,OAAO;AAC9C,2BAAe,UAAU,YAAY,SAAU,OAAO;AACpD,6BAAe,YAAY,UAAU;AACrC,qBAAO,QAAQ,QAAQ,WAAW,MAAM;AACxC,kBAAI,QAAQ,cAAc,MAAM,GAAG;AACnC,8BAAgB,OAAO,OAAO;AAAA,YAChC,CAAC;AACD,mBAAO,QAAQ,QAAQ,WAAW,QAAQ,QAAQ;AAClD,mBAAO,QAAQ;AAAA,UACjB;AAAA;AAAA,QACA,QAAQ,SAAU,SAAS;AACzB,cAAI,UAAU,cAAc;AAC5B,cAAI,YAAY,GAAG,gBAAgB,OAAO;AAC1C,yBAAe,UAAU,YAAY,SAAU,OAAO;AACpD,2BAAe,YAAY,UAAU;AACrC,mBAAO,QAAQ,QAAQ,WAAW,MAAM;AACxC,eAAG,WAAW;AACd,oBAAQ,WAAW;AAAA,UACrB,CAAC;AACD,iBAAO,QAAQ,QAAQ,WAAW,QAAQ,SAAS;AACnD,iBAAO,QAAQ;AAAA,QACjB;AAAA,QACA,UAAU,SAAgB,SAAS;AAAA;AACjC,gBAAI,UAAU,cAAc;AAC5B,gBAAI,cAAc,MAAM,GAAG,kBAAkB,OAAO;AACpD,2BAAe,UAAU,YAAY,SAAU,OAAO;AACpD,6BAAe,YAAY,UAAU;AACrC,qBAAO,QAAQ,QAAQ,WAAW,MAAM;AACxC,kBAAI,QAAQ,cAAc,MAAM,GAAG;AACnC,8BAAgB,OAAO,OAAO;AAAA,YAChC,CAAC;AACD,mBAAO,QAAQ,QAAQ,WAAW,QAAQ,WAAW;AACrD,mBAAO,QAAQ;AAAA,UACjB;AAAA;AAAA,QACA,mBAAmB,WAAY;AAC7B,cAAI,aAAa,GAAG,iBAAiB;AACrC,cAAI,OAAO,eAAe,aAAa;AACrC,mBAAO,QAAQ,QAAQ,WAAW,QAAQ,UAAU;AAAA,UACtD,OAAO;AACL,kBAAM;AAAA,UACR;AAAA,QACF;AAAA,QACA,aAAa,SAAU,SAAS;AAC9B,cAAI,WAAW,QAAQ,aAAa;AAClC,mBAAO,QAAQ;AAAA,UACjB,WAAW,GAAG,aAAa;AACzB,mBAAO,GAAG;AAAA,UACZ,OAAO;AACL,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,UAAM,2BAA2B;AAAA,EACnC;AACA,QAAM,qBAAqB;AAC3B,MAAI,eAAe,WAAY;AAC7B,QAAI,EAAE,gBAAgB,eAAe;AACnC,aAAO,IAAI,aAAa;AAAA,IAC1B;AACA,iBAAa,QAAQ,WAAW,MAAM;AACtC,iBAAa,WAAW,SAAS;AACjC,QAAI,KAAK;AAKT,aAAS,qBAAqB;AAC5B,YAAM,cAAc,KAAK,IAAI;AAC7B,iBAAW,CAAC,KAAK,KAAK,KAAK,iBAAiB,GAAG;AAE7C,cAAM,SAAS,YAAY,KAAK;AAGhC,YAAI,WAAW,QAAQ,SAAS,aAAa;AAC3C,uBAAa,WAAW,GAAG;AAAA,QAC7B;AAAA,MACF;AAAA,IACF;AAKA,aAAS,iBAAiB;AACxB,iBAAW,CAAC,GAAG,KAAK,iBAAiB,GAAG;AACtC,qBAAa,WAAW,GAAG;AAAA,MAC7B;AAAA,IACF;AAMA,aAAS,mBAAmB;AAC1B,aAAO,OAAO,QAAQ,YAAY,EAAE,OAAO,CAAC,CAAC,GAAG,MAAM,IAAI,WAAW,kBAAkB,CAAC;AAAA,IAC1F;AAOA,aAAS,YAAY,OAAO;AAC1B,UAAI;AAGJ,UAAI;AACF,sBAAc,KAAK,MAAM,KAAK;AAAA,MAChC,SAAS,OAAO;AACd,eAAO;AAAA,MACT;AAGA,UAAI,SAAS,WAAW,KAAK,aAAa,eAAe,OAAO,YAAY,YAAY,UAAU;AAChG,eAAO,YAAY;AAAA,MACrB;AACA,aAAO;AAAA,IACT;AACA,OAAG,MAAM,SAAU,OAAO;AACxB,UAAI,CAAC,OAAO;AACV;AAAA,MACF;AACA,UAAI,MAAM,qBAAqB;AAC/B,UAAI,QAAQ,aAAa,QAAQ,GAAG;AACpC,UAAI,OAAO;AACT,qBAAa,WAAW,GAAG;AAC3B,gBAAQ,KAAK,MAAM,KAAK;AAAA,MAC1B;AACA,yBAAmB;AACnB,aAAO;AAAA,IACT;AACA,OAAG,MAAM,SAAU,OAAO;AACxB,yBAAmB;AACnB,YAAM,MAAM,qBAAqB,MAAM;AACvC,YAAM,QAAQ,KAAK,UAAU,iCACxB,QADwB;AAAA;AAAA,QAG3B,SAAS,KAAK,IAAI,IAAI,KAAK,KAAK;AAAA,MAClC,EAAC;AACD,UAAI;AACF,qBAAa,QAAQ,KAAK,KAAK;AAAA,MACjC,SAAS,OAAO;AAEd,uBAAe;AACf,qBAAa,QAAQ,KAAK,KAAK;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AACA,MAAI,gBAAgB,WAAY;AAC9B,QAAI,EAAE,gBAAgB,gBAAgB;AACpC,aAAO,IAAI,cAAc;AAAA,IAC3B;AACA,QAAI,KAAK;AACT,OAAG,MAAM,SAAU,OAAO;AACxB,UAAI,CAAC,OAAO;AACV;AAAA,MACF;AACA,UAAI,QAAQ,UAAU,qBAAqB,KAAK;AAChD,gBAAU,qBAAqB,OAAO,IAAI,iBAAiB,IAAI,CAAC;AAChE,UAAI,OAAO;AACT,eAAO,KAAK,MAAM,KAAK;AAAA,MACzB;AAAA,IACF;AACA,OAAG,MAAM,SAAU,OAAO;AACxB,gBAAU,qBAAqB,MAAM,OAAO,KAAK,UAAU,KAAK,GAAG,iBAAiB,EAAE,CAAC;AAAA,IACzF;AACA,OAAG,aAAa,SAAU,KAAK;AAC7B,gBAAU,KAAK,IAAI,iBAAiB,IAAI,CAAC;AAAA,IAC3C;AACA,QAAI,mBAAmB,SAAU,SAAS;AACxC,UAAI,MAAM,oBAAI,KAAK;AACnB,UAAI,QAAQ,IAAI,QAAQ,IAAI,UAAU,KAAK,GAAI;AAC/C,aAAO;AAAA,IACT;AACA,QAAI,YAAY,SAAU,KAAK;AAC7B,UAAI,OAAO,MAAM;AACjB,UAAI,KAAK,SAAS,OAAO,MAAM,GAAG;AAClC,eAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,KAAK;AAClC,YAAI,IAAI,GAAG,CAAC;AACZ,eAAO,EAAE,OAAO,CAAC,KAAK,KAAK;AACzB,cAAI,EAAE,UAAU,CAAC;AAAA,QACnB;AACA,YAAI,EAAE,QAAQ,IAAI,KAAK,GAAG;AACxB,iBAAO,EAAE,UAAU,KAAK,QAAQ,EAAE,MAAM;AAAA,QAC1C;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,QAAI,YAAY,SAAU,KAAK,OAAO,gBAAgB;AACpD,UAAI,SAAS,MAAM,MAAM,QAAQ,eAAoB,eAAe,YAAY,IAAI;AACpF,eAAS,SAAS;AAAA,IACpB;AAAA,EACF;AACA,WAAS,wBAAwB;AAC/B,QAAI;AACF,aAAO,IAAI,aAAa;AAAA,IAC1B,SAAS,KAAK;AAAA,IAAC;AACf,WAAO,IAAI,cAAc;AAAA,EAC3B;AACA,WAAS,aAAa,IAAI;AACxB,WAAO,WAAY;AACjB,UAAI,GAAG,eAAe;AACpB,WAAG,MAAM,SAAS,MAAM,UAAU,MAAM,KAAK,SAAS,CAAC;AAAA,MACzD;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAO,mBAAQ;AAMf,SAAS,cAAc,OAAO;AAC5B,QAAM,YAAY,OAAO,cAAc,GAAG,KAAK;AAC/C,SAAO,KAAK,SAAS;AACvB;AAMA,SAAe,aAAa,SAAS;AAAA;AACnC,UAAM,UAAU,IAAI,YAAY;AAChC,UAAM,OAAO,QAAQ,OAAO,OAAO;AACnC,QAAI,OAAO,WAAW,eAAe,OAAO,OAAO,WAAW,aAAa;AACzE,YAAM,IAAI,MAAM,kCAAkC;AAAA,IACpD;AACA,WAAO,MAAM,OAAO,OAAO,OAAO,WAAW,IAAI;AAAA,EACnD;AAAA;AAKA,SAAS,YAAY,OAAO;AAC1B,QAAM,CAAC,QAAQ,OAAO,IAAI,MAAM,MAAM,GAAG;AACzC,MAAI,OAAO,YAAY,UAAU;AAC/B,UAAM,IAAI,MAAM,4CAA4C;AAAA,EAC9D;AACA,MAAI;AACJ,MAAI;AACF,cAAU,gBAAgB,OAAO;AAAA,EACnC,SAAS,OAAO;AACd,UAAM,IAAI,MAAM,mEAAmE;AAAA,MACjF,OAAO;AAAA,IACT,CAAC;AAAA,EACH;AACA,MAAI;AACF,WAAO,KAAK,MAAM,OAAO;AAAA,EAC3B,SAAS,OAAO;AACd,UAAM,IAAI,MAAM,8DAA8D;AAAA,MAC5E,OAAO;AAAA,IACT,CAAC;AAAA,EACH;AACF;AAKA,SAAS,gBAAgB,OAAO;AAC9B,MAAI,SAAS,MAAM,WAAW,KAAK,GAAG,EAAE,WAAW,KAAK,GAAG;AAC3D,UAAQ,OAAO,SAAS,GAAG;AAAA,IACzB,KAAK;AACH;AAAA,IACF,KAAK;AACH,gBAAU;AACV;AAAA,IACF,KAAK;AACH,gBAAU;AACV;AAAA,IACF;AACE,YAAM,IAAI,MAAM,qCAAqC;AAAA,EACzD;AACA,MAAI;AACF,WAAO,iBAAiB,MAAM;AAAA,EAChC,SAAS,OAAO;AACd,WAAO,KAAK,MAAM;AAAA,EACpB;AACF;AAKA,SAAS,iBAAiB,OAAO;AAC/B,SAAO,mBAAmB,KAAK,KAAK,EAAE,QAAQ,QAAQ,CAAC,GAAG,MAAM;AAC9D,QAAI,OAAO,EAAE,WAAW,CAAC,EAAE,SAAS,EAAE,EAAE,YAAY;AACpD,QAAI,KAAK,SAAS,GAAG;AACnB,aAAO,MAAM;AAAA,IACf;AACA,WAAO,MAAM;AAAA,EACf,CAAC,CAAC;AACJ;AAMA,SAAS,SAAS,OAAO;AACvB,SAAO,OAAO,UAAU,YAAY,UAAU;AAChD;;;AC1gDA,IAAI;AAAA,CACH,SAAUC,0BAAyB;AAIlC,EAAAA,yBAAwBA,yBAAwB,aAAa,IAAI,CAAC,IAAI;AAKtE,EAAAA,yBAAwBA,yBAAwB,cAAc,IAAI,CAAC,IAAI;AAIvE,EAAAA,yBAAwBA,yBAAwB,oBAAoB,IAAI,CAAC,IAAI;AAI7E,EAAAA,yBAAwBA,yBAAwB,sBAAsB,IAAI,CAAC,IAAI;AAI/E,EAAAA,yBAAwBA,yBAAwB,eAAe,IAAI,CAAC,IAAI;AAIxE,EAAAA,yBAAwBA,yBAAwB,SAAS,IAAI,CAAC,IAAI;AAMlE,EAAAA,yBAAwBA,yBAAwB,gBAAgB,IAAI,CAAC,IAAI;AAIzE,EAAAA,yBAAwBA,yBAAwB,gBAAgB,IAAI,CAAC,IAAI;AAC3E,GAAG,4BAA4B,0BAA0B,CAAC,EAAE;AAmB5D,IAAM,oBAAN,MAAwB;AAAA,EACtB,YAAY,QAAQ,iBAAiB;AACnC,SAAK,SAAS;AACd,SAAK,kBAAkB;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQM,YAAY,OAAO,OAAO;AAAA;AAC9B,UAAI;AACF,aAAK,gBAAgB,MAAM,KAAK,gBAAgB,WAAW;AAC3D,aAAK,QAAQ,MAAM,KAAK,gBAAgB,aAAa,IAAI;AACzD,eAAO,MAAM,KAAK,gBAAgB,OAAO,KAAK;AAAA,MAChD,SAAS,OAAO;AACd,cAAM,IAAI,MAAM,yDAAyD,KAAK;AAAA,MAChF;AAAA,IACF;AAAA;AACF;AAoBA,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,cAAc;AAIZ,SAAK,mBAAmB,IAAI,QAAQ;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,sBAAsB;AACpB,SAAK,UAAU,cAAc,eAAa;AACxC,WAAK,iBAAiB,KAAK;AAAA,QACzB,MAAM;AAAA,QACN,MAAM,wBAAwB;AAAA,MAChC,CAAC;AAAA,IACH;AACA,SAAK,UAAU,eAAe,MAAM;AAClC,WAAK,iBAAiB,KAAK;AAAA,QACzB,MAAM,wBAAwB;AAAA,MAChC,CAAC;AAAA,IACH;AACA,SAAK,UAAU,uBAAuB,MAAM;AAC1C,WAAK,iBAAiB,KAAK;AAAA,QACzB,MAAM,wBAAwB;AAAA,MAChC,CAAC;AAAA,IACH;AACA,SAAK,UAAU,qBAAqB,MAAM;AACxC,WAAK,iBAAiB,KAAK;AAAA,QACzB,MAAM,wBAAwB;AAAA,MAChC,CAAC;AAAA,IACH;AACA,SAAK,UAAU,gBAAgB,MAAM;AACnC,WAAK,iBAAiB,KAAK;AAAA,QACzB,MAAM,wBAAwB;AAAA,MAChC,CAAC;AAAA,IACH;AACA,SAAK,UAAU,iBAAiB,MAAM;AACpC,WAAK,iBAAiB,KAAK;AAAA,QACzB,MAAM,wBAAwB;AAAA,MAChC,CAAC;AAAA,IACH;AACA,SAAK,UAAU,iBAAiB,WAAS;AACvC,WAAK,iBAAiB,KAAK;AAAA,QACzB,MAAM;AAAA,QACN,MAAM,wBAAwB;AAAA,MAChC,CAAC;AAAA,IACH;AACA,SAAK,UAAU,UAAU,mBAAiB;AACxC,WAAK,iBAAiB,KAAK;AAAA,QACzB,MAAM;AAAA,QACN,MAAM,wBAAwB;AAAA,MAChC,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB,oBAAoB;AACnC,UAAM,eAAe,CAAC;AACtB,eAAW,QAAQ,oBAAoB;AACrC,UAAI;AACJ,UAAI,OAAO,SAAS,UAAU;AAC5B,sBAAc;AAAA,UACZ,YAAY,IAAI,OAAO,MAAM,GAAG;AAAA,UAChC,aAAa,CAAC;AAAA,QAChB;AAAA,MACF,OAAO;AACL,sBAAc;AAAA,UACZ,YAAY,IAAI,OAAO,KAAK,KAAK,GAAG;AAAA,UACpC,aAAa,KAAK;AAAA,QACpB;AAAA,MACF;AACA,mBAAa,KAAK,WAAW;AAAA,IAC/B;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,qBAAqB,CAAC;AAAA,IACtB,0BAA0B;AAAA,IAC1B,eAAe;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,iBAAiB,MAAM;AAAA,IACvB,oBAAoB,MAAM;AAAA,EAC5B,GAAG;AACD,SAAK,2BAA2B;AAChC,SAAK,4BAA4B;AACjC,SAAK,2BAA2B;AAChC,SAAK,gBAAgB,aAAa,KAAK,EAAE,OAAO,GAAG;AACnD,SAAK,gBAAgB,KAAK,iBAAiB,kBAAkB;AAC7D,SAAK,iBAAiB,cAAc,YAAY,SAAS,aAAa;AACtE,SAAK,qBAAqB;AAC1B,SAAK,iBAAiB;AACtB,SAAK,oBAAoB;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA2CM,OAAmB;AAAA,+CAAd,UAAU,CAAC,GAAG;AACvB,WAAK,kBAAkB,OAAO;AAC9B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI;AACJ,WAAK,YAAY,IAAI,iBAAS,MAAM;AACpC,WAAK,oBAAoB;AACzB,YAAM,gBAAgB,MAAM,KAAK,UAAU,KAAK,WAAW;AAC3D,UAAI,iBAAiB,KAAK,2BAA2B;AACnD,cAAM,KAAK,gBAAgB;AAAA,MAC7B;AACA,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAsBM,QAAoB;AAAA,+CAAd,UAAU,CAAC,GAAG;AACxB,YAAM,KAAK,UAAU,MAAM,OAAO;AAClC,UAAI,KAAK,2BAA2B;AAClC,cAAM,KAAK,gBAAgB;AAAA,MAC7B;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASM,OAAO,aAAa;AAAA;AACxB,YAAM,UAAU;AAAA,QACd;AAAA,MACF;AACA,YAAM,KAAK,UAAU,OAAO,OAAO;AACnC,WAAK,eAAe;AAAA,IACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWM,WAEH;AAAA,+CAFY,UAAU;AAAA,MACvB,QAAQ;AAAA,IACV,GAAG;AACD,YAAM,KAAK,UAAU,SAAS,OAAO;AAAA,IACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,aAAa,MAAM,UAAU;AAC3B,QAAI;AACJ,cAAU,KAAK,UAAU,gBAAgB,MAAM,QAAQ;AACvD,QAAI,CAAC,SAAS;AACZ,gBAAU,KAAK,UAAU,aAAa,IAAI;AAAA,IAC5C;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,aAAa,aAAa,MAAM,UAAU;AACxC,QAAI,QAAQ,CAAC;AACb,QAAI,KAAK,UAAU,gBAAgB;AACjC,aAAO,KAAK,KAAK,UAAU,cAAc,EAAE,QAAQ,SAAO;AACxD,YAAI,YAAY,aAAa,KAAK;AAChC;AAAA,QACF;AACA,cAAM,iBAAiB,KAAK,UAAU,eAAe,GAAG;AACxD,cAAM,cAAc,eAAe,OAAO,KAAK,CAAC;AAChD,gBAAQ,MAAM,OAAO,WAAW;AAAA,MAClC,CAAC;AAAA,IACH;AACA,QAAI,cAAc,KAAK,UAAU,aAAa;AAC5C,YAAMC,cAAa,KAAK,UAAU,YAAY,OAAO,KAAK,CAAC;AAC3D,YAAM,KAAK,GAAGA,WAAU;AAAA,IAC1B;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa;AACX,QAAI,CAAC,KAAK,WAAW;AACnB,aAAO;AAAA,IACT;AACA,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,eAAe,cAAc,GAAG;AAC9B,WAAO,KAAK,UAAU,eAAe,WAAW;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYM,cAAmD;AAAA,+CAAvC,cAAc,KAAK,oBAAoB;AAGvD,UAAI,KAAK,gBAAgB;AACvB,YAAI,KAAK,eAAe,GAAG;AACzB,gBAAM,IAAI,MAAM,wDAAwD;AAAA,QAC1E;AACA,eAAO;AAAA,MACT;AACA,UAAI,CAAC,KAAK,WAAW;AACnB,cAAM,IAAI,MAAM,8CAA8C;AAAA,MAChE;AACA,UAAI;AACF,eAAO,MAAM,KAAK,UAAU,YAAY,WAAW;AAAA,MACrD,SAAS,OAAO;AACd,eAAO;AAAA,MACT;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWM,gBAAgB,cAAc,OAAO;AAAA;AACzC,UAAI,KAAK,gBAAgB,CAAC,aAAa;AACrC,eAAO,KAAK;AAAA,MACd;AACA,UAAI,CAAC,KAAK,UAAU,eAAe;AACjC,cAAM,IAAI,MAAM,+DAA+D;AAAA,MACjF;AACA,aAAO,KAAK,eAAe,MAAM,KAAK,UAAU,gBAAgB;AAAA,IAClE;AAAA;AAAA;AAAA;AAAA;AAAA,EAIM,WAAW;AAAA;AACf,aAAO,KAAK,UAAU;AAAA,IACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc;AACZ,QAAI,CAAC,KAAK,cAAc;AACtB,YAAM,IAAI,MAAM,oDAAoD;AAAA,IACtE;AACA,WAAO,KAAK,aAAa;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa;AACX,SAAK,UAAU,WAAW;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,iBAAiB,UAAU,IAAI,YAAY,GAAG;AAC5C,WAAO,KAAK,KAAK,SAAS,CAAC,EAAE,KAAK,IAAI,WAAS,QAAQ,QAAQ,IAAI,KAAK,0BAA0B,KAAK,gBAAgB,KAAK,IAAI,OAAO,CAAC;AAAA,EAC1I;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,sBAAsB;AACpB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,IAAI,eAAe;AACjB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,0BAA0B;AAC5B,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBA,IAAI,kBAAkB;AACpB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,aAAO,KAAK,qBAAqB,kBAAiB;AAAA,IACpD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,iBAAgB;AAAA,IAC3B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAmBH,IAAM,4BAAN,MAAM,2BAA0B;AAAA,EAC9B,YAAY,UAAU;AACpB,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQM,yBAAyB,KAAK;AAAA;AAClC,UAAI,KAAK,SAAS,kBAAkB,GAAG,GAAG;AACxC,eAAO,MAAM,KAAK,SAAS,YAAY;AAAA,MACzC;AACA,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,cAAc;AAAA,IACZ;AAAA,IACA;AAAA,EACF,GAAG;AAAA,IACD;AAAA,IACA;AAAA,EACF,GAAG;AACD,UAAM,WAAW,YAAY,WAAW,KAAK,YAAY,KAAK,EAAE,QAAQ,OAAO,YAAY,CAAC,IAAI;AAChG,UAAM,UAAU,WAAW,KAAK,GAAG;AACnC,WAAO,YAAY;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,KAAK,MAAM;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,KAAK;AACT,QAAI,CAAC,yBAAyB;AAC5B,aAAO,KAAK,OAAO,GAAG;AAAA,IACxB;AACA,UAAM,YAAY,CAAC,KAAK,SAAS,eAAe,GAAG,KAAK,aAAa,UAAU,UAAQ,KAAK,cAAc,KAAK,IAAI,CAAC,IAAI;AACxH,QAAI,WAAW;AACb,aAAO,KAAK,OAAO,GAAG;AAAA,IACxB;AACA,WAAO,cAAc,CAAC,KAAK,KAAK,yBAAyB,GAAG,CAAC,GAAG,GAAG,KAAK,SAAS,WAAW,CAAC,CAAC,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,GAAG,UAAU,MAAM,aAAa,KAAK,6BAA6B,KAAK,IAAI,IAAI,KAAK,OAAO,GAAG,CAAC,CAAC;AAAA,EACnN;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,6BAA6B,KAAK,MAAM;AACtC,WAAO,KAAK,SAAS,iBAAiB,IAAI,OAAO,EAAE,KAAK,SAAS,uBAAqB;AACpF,YAAM,QAAQ,IAAI,MAAM;AAAA,QACtB,SAAS;AAAA,MACX,CAAC;AACD,aAAO,KAAK,OAAO,KAAK;AAAA,IAC1B,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,kCAAkC,mBAAmB;AACxE,aAAO,KAAK,qBAAqB,4BAA8B,SAAS,eAAe,CAAC;AAAA,IAC1F;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,2BAA0B;AAAA,IACrC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AAcH,IAAM,aAAN,MAAM,YAAW;AAAA,EACf,OAAO;AACL,SAAK,OAAO,SAAS,mBAAmB,mBAAmB;AACzD,aAAO,KAAK,qBAAqB,aAAY;AAAA,IAC/C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,YAAY;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,WAAW,CAAC,iBAAiB;AAAA,QAC3B,SAAS;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT,CAAC;AAAA,MACD,SAAS,CAAC,YAAY;AAAA,IACxB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,YAAY;AAAA,MACtB,WAAW,CAAC,iBAAiB;AAAA,QAC3B,SAAS;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAcH,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,OAAO;AACL,SAAK,OAAO,SAAS,8BAA8B,mBAAmB;AACpE,aAAO,KAAK,qBAAqB,wBAAuB;AAAA,IAC1D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,UAAU;AAAA,IACtB,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,UAAU;AAAA,IACtB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,UAAU;AAAA,IACtB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAsBH,IAAI;AAAA,CACH,SAAUC,oBAAmB;AAK5B,EAAAA,mBAAkB,+BAA+B,IAAI;AAKrD,EAAAA,mBAAkB,qBAAqB,IAAI;AAI3C,EAAAA,mBAAkB,WAAW,IAAI;AAKjC,EAAAA,mBAAkB,YAAY,IAAI;AAIlC,EAAAA,mBAAkB,kBAAkB,IAAI;AAIxC,EAAAA,mBAAkB,oBAAoB,IAAI;AAI1C,EAAAA,mBAAkB,aAAa,IAAI;AAInC,EAAAA,mBAAkB,OAAO,IAAI;AAM7B,EAAAA,mBAAkB,cAAc,IAAI;AAIpC,EAAAA,mBAAkB,cAAc,IAAI;AACtC,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAQhD,IAAM,gBAAgB,UAAQ;AAQ9B,IAAM,uBAAuB,cAAY;AACvC,QAAM,iBAAiB,OAAO;AAAA,IAC5B,MAAM,kBAAkB;AAAA,EAC1B,CAAC;AACD,MAAI,CAAC,UAAU;AACb,mBAAe,IAAI;AAAA,MACjB,MAAM,kBAAkB;AAAA,IAC1B,CAAC;AACD,WAAO;AAAA,EACT;AACA,WAAS,UAAU,mBAAiB;AAClC,mBAAe,IAAI;AAAA,MACjB,MAAM,kBAAkB;AAAA,MACxB,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AACA,WAAS,cAAc,eAAa;AAClC,mBAAe,IAAI;AAAA,MACjB,MAAM,kBAAkB;AAAA,MACxB,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AACA,WAAS,eAAe,MAAM;AAC5B,mBAAe,IAAI;AAAA,MACjB,MAAM,kBAAkB;AAAA,IAC1B,CAAC;AAAA,EACH;AACA,WAAS,iBAAiB,CAAC,QAAQ,WAAW;AAC5C,mBAAe,IAAI;AAAA,MACjB,MAAM,kBAAkB;AAAA,MACxB,MAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACA,WAAS,qBAAqB,MAAM;AAClC,mBAAe,IAAI;AAAA,MACjB,MAAM,kBAAkB;AAAA,IAC1B,CAAC;AAAA,EACH;AACA,WAAS,uBAAuB,MAAM;AACpC,mBAAe,IAAI;AAAA,MACjB,MAAM,kBAAkB;AAAA,IAC1B,CAAC;AAAA,EACH;AACA,WAAS,gBAAgB,MAAM;AAC7B,mBAAe,IAAI;AAAA,MACjB,MAAM,kBAAkB;AAAA,IAC1B,CAAC;AAAA,EACH;AACA,WAAS,iBAAiB,MAAM;AAC9B,mBAAe,IAAI;AAAA,MACjB,MAAM,kBAAkB;AAAA,IAC1B,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAIA,IAAM,wBAAwB,IAAI,eAAe,wBAAwB;AAoEzE,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,YAAY,aAAa,eAAe,UAAU;AAChD,SAAK,cAAc;AACnB,SAAK,gBAAgB;AACrB,SAAK,WAAW;AAIhB,SAAK,QAAQ,CAAC;AAId,SAAK,aAAa;AAClB,SAAK,cAAc,MAAM;AACzB,UAAM,iBAAiB,OAAO,qBAAqB;AACnD,WAAO,MAAM;AACX,YAAM,gBAAgB,eAAe;AACrC,UAAI,cAAc,SAAS,kBAAkB,OAAO;AAClD;AAAA,MACF;AACA,YAAM,gBAAgB,cAAc,cAAc,IAAI;AACtD,UAAI,eAAe;AACjB,aAAK,OAAO;AAAA,MACd;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,SAAS;AACP,UAAM,YAAY,KAAK,eAAe;AACtC,QAAI,WAAW;AACb,WAAK,cAAc,mBAAmB,KAAK,WAAW;AAAA,IACxD,OAAO;AACL,WAAK,cAAc,MAAM;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB;AACf,UAAM,kBAAkB,KAAK,MAAM,KAAK,UAAQ,KAAK,SAAS,gBAAgB,MAAM,KAAK,QAAQ,CAAC;AAClG,UAAM,eAAe,KAAK,aAAa,KAAK,MAAM,KAAK,UAAQ,KAAK,SAAS,aAAa,IAAI,CAAC,IAAI;AACnG,WAAO,mBAAmB;AAAA,EAC5B;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,mBAAmB;AAChE,aAAO,KAAK,qBAAqB,oBAAsB,kBAAqB,WAAW,GAAM,kBAAqB,gBAAgB,GAAM,kBAAkB,gBAAQ,CAAC;AAAA,IACrK;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,cAAc,EAAE,CAAC;AAAA,MAClC,QAAQ;AAAA,QACN,OAAO,CAAC,GAAG,cAAc,OAAO;AAAA,QAChC,UAAU,CAAC,GAAG,sBAAsB,UAAU;AAAA,QAC9C,YAAY,CAAC,GAAG,wBAAwB,YAAY;AAAA,MACtD;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,CAAC,GAAG;AAAA,IACF,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,sBAAsB;AAAA,IAC/B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AA0BH,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,YAAY,QAAQ;AAClB,SAAK,SAAS;AAKd,SAAK,eAAe,OAAO,KAAK,IAAI,CAAC;AAKrC,SAAK,WAAW,IAAI,QAAQ;AAI5B,SAAK,qBAAqB,SAAS,MAAM,KAAK,aAAa,CAAC;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB;AAChB,UAAM,YAAY,kBAAkB,OAAO,WAAW,CAAC;AACvD,QAAI,CAAC,WAAW;AACd;AAAA,IACF;AACA,SAAK,OAAO,kBAAkB,MAAM;AAClC,YAAM,SAAS,CAAC,aAAa,cAAc,WAAW,SAAS,QAAQ;AACvE,aAAO,QAAQ,WAAS;AACtB,kBAAU,QAAQ,KAAK,EAAE,KAAK,aAAa,GAAG,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM,KAAK,mBAAmB,CAAC;AAAA,MACtH,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB;AACnB,SAAK,OAAO,IAAI,MAAM;AACpB,WAAK,aAAa,IAAI,KAAK,IAAI,CAAC;AAAA,IAClC,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,mBAAmB;AACrB,WAAO,KAAK,aAAa;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS,SAAS;AAChB,WAAO,KAAK,IAAI,IAAI,KAAK,mBAAmB;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,mBAAmB;AAClE,aAAO,KAAK,qBAAqB,sBAAwB,SAAY,MAAM,CAAC;AAAA,IAC9E;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,qBAAoB;AAAA,IAC/B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAoBH,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,YAAY,UAAU,cAAc;AAClC,SAAK,WAAW;AAChB,SAAK,eAAe;AACpB,SAAK,UAAU,KAAK;AACpB,SAAK,cAAc;AACnB,UAAM,iBAAiB,OAAO,qBAAqB;AACnD,WAAO,MAAM;AACX,YAAM,gBAAgB,eAAe;AACrC,UAAI,cAAc,SAAS,kBAAkB,cAAc;AACzD,aAAK,yBAAyB;AAAA,MAChC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,IAAI,iBAAiB;AACnB,WAAO;AAAA,MACL,gBAAgB;AAAA,MAChB,qBAAqB;AAAA,IACvB;AAAA,EACF;AAAA,EACA,6BAA6B;AAC3B,YAAQ,KAAK,QAAQ,qBAAqB;AAAA,MACxC,KAAK;AACH,aAAK,SAAS,MAAM,EAAE,MAAM,WAAS,QAAQ,MAAM,oCAAoC,KAAK,CAAC;AAC7F;AAAA,MACF,KAAK;AACH,aAAK,SAAS,OAAO,EAAE,MAAM,WAAS,QAAQ,MAAM,qCAAqC,KAAK,CAAC;AAC/F;AAAA,MACF;AACE;AAAA,IACJ;AAAA,EACF;AAAA,EACA,2BAA2B;AACzB,QAAI,CAAC,KAAK,eAAe,CAAC,KAAK,SAAS,eAAe;AACrD;AAAA,IACF;AACA,QAAI,KAAK,aAAa,SAAS,KAAK,QAAQ,cAAc,GAAG;AAC3D,WAAK,SAAS,YAAY,EAAE,MAAM,MAAM,KAAK,2BAA2B,CAAC;AAAA,IAC3E,OAAO;AACL,WAAK,2BAA2B;AAAA,IAClC;AAAA,EACF;AAAA,EACA,MAAM,SAAS;AACb,SAAK,UAAU,kCACV,KAAK,iBACL;AAEL,SAAK,cAAc;AACnB,SAAK,aAAa,gBAAgB;AAAA,EACpC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gCAAgC,mBAAmB;AACtE,aAAO,KAAK,qBAAqB,0BAA4B,SAAS,gBAAQ,GAAM,SAAS,mBAAmB,CAAC;AAAA,IACnH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,yBAAwB;AAAA,IACnC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AAwCH,SAAS,qBAAqB,SAAS;AACrC,SAAO;AAAA,IACL,WAAW,MAAM;AACf,YAAM,0BAA0B,OAAO,uBAAuB;AAC9D,8BAAwB,MAAM,OAAO;AAAA,IACvC;AAAA,EACF;AACF;AASA,IAAM,mBAAmB,CAAC,iBAAiB,CAAC,MAAM;AAChD,SAAO,OAAO,QAAQ,cAAc,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK,KAAK,MAAM;AACpE,UAAM,GAAG,IAAI,MAAM;AACnB,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AAiCA,IAAM,kBAAkB,qBAAmB;AACzC,SAAO,CAAC,MAAM,UAAU;AACtB,UAAM,WAAW,OAAO,gBAAQ;AAChC,UAAM,gBAAgB,UAAU,iBAAiB;AACjD,UAAM,eAAe;AAAA,MACnB,eAAe,iBAAiB,UAAU,cAAc;AAAA,MACxD,YAAY,UAAU,aAAa,SAAS,CAAC;AAAA,IAC/C;AACA,UAAM,WAAW;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,WAAO,gBAAgB,MAAM,OAAO,QAAQ;AAAA,EAC9C;AACF;AAYA,IAAM,gBAAgB;AAItB,IAAM,4BAA4B;AAYlC,IAAM,6BAA6B,WAAU,iCACxC,QADwC;AAAA,EAE3C,cAAc,MAAM,gBAAgB;AAAA,EACpC,yBAAyB,MAAM,2BAA2B;AAAA,EAC1D,mBAAmB,MAAM,sBAAsB,MAAM;AACvD;AASA,IAAM,2BAA2B,CAAO,IAAK,IAAU,OAEjD,eAFkC,IAAK,IAAU,KAEjD,WAFkC,KAAK,UAAU;AAAA,EACrD,oBAAoB,OAAK;AAC3B,GAAM;AACJ,MAAI,kBAAkB,GAAG,GAAG;AAC1B,WAAO,MAAM,SAAS,YAAY,EAAE,MAAM,MAAM,KAAK;AAAA,EACvD;AACA,SAAO;AACT;AAUA,IAAM,yBAAyB,CAAC,KAAK,MAAM,UAAU,cAAc;AACjE,QAAM;AAAA,IACJ,eAAe;AAAA,IACf,0BAA0B;AAAA,EAC5B,IAAI;AACJ,QAAM,gBAAgB,IAAI,MAAM;AAAA,IAC9B,YAAY;AAAA,MACV,CAAC,uBAAuB,GAAG,GAAG,YAAY,IAAI,SAAS,KAAK;AAAA,IAC9D;AAAA,EACF,CAAC;AACD,SAAO,KAAK,aAAa;AAC3B;AAiBA,IAAM,yCAAyC,IAAI,eAAe,kEAAkE;AAwDpI,IAAM,+BAA+B,CAAC,KAAK,SAAS;AAClD,QAAM,aAAa,OAAO,sCAAsC,KAAK,CAAC;AACtE,QAAM,WAAW,OAAO,gBAAQ;AAChC,QAAM,oBAAoB,WAAW,KAAK,CAAM,cAAU;AAAG,iBAAM,UAAU,eAAe,KAAK,MAAM,QAAQ;AAAA,IAAC;AAChH,MAAI,CAAC,mBAAmB;AACtB,WAAO,KAAK,GAAG;AAAA,EACjB;AACA,SAAO,KAAK,yBAAyB,KAAK,UAAU,iBAAiB,CAAC,EAAE,KAAK,SAAW,MAAM,SAAS,gBAAgB,uBAAuB,KAAK,MAAM,UAAU,iBAAiB,IAAI,KAAK,GAAG,CAAC,CAAC;AACpM;AAgBA,IAAM,0CAA0C,IAAI,eAAe,gFAAgF;AACnJ,IAAM,wBAAwB,CAAC;AAAA,EAC7B;AAAA,EACA;AACF,GAAG;AAAA,EACD;AAAA,EACA,cAAc,CAAC;AACjB,MAAM;AACJ,QAAM,iBAAiB,YAAY,WAAW,KAAK,YAAY,KAAK,EAAE,QAAQ,OAAO,YAAY,CAAC,IAAI;AACtG,QAAM,UAAU,WAAW,KAAK,GAAG;AACnC,SAAO,kBAAkB;AAC3B;AAwEA,IAAM,gCAAgC,CAAC,KAAK,SAAS;AACnD,QAAM,aAAa,OAAO,uCAAuC,KAAK,CAAC;AACvE,QAAM,oBAAoB,WAAW,KAAK,eAAa,sBAAsB,KAAK,SAAS,CAAC;AAC5F,MAAI,CAAC,mBAAmB;AACtB,WAAO,KAAK,GAAG;AAAA,EACjB;AACA,QAAM,WAAW,OAAO,gBAAQ;AAChC,SAAO,KAAK,yBAAyB,KAAK,UAAU,iBAAiB,CAAC,EAAE,KAAK,SAAW,MAAM,SAAS,gBAAgB,uBAAuB,KAAK,MAAM,UAAU,iBAAiB,IAAI,KAAK,GAAG,CAAC,CAAC;AACpM;AAiBA,IAAM,kCAAkC,CAAC,UAAU,YAAY;AAC7D,QAAM;AAAA,IACJ;AAAA,IACA,WAAW,CAAC;AAAA,EACd,IAAI;AACJ,MAAI,CAAC,aAAa;AAChB,WAAO,CAAC;AAAA,EACV;AACA,SAAO,sBAAsB,MAAY;AACvC,UAAM,WAAW,OAAO,mBAAmB;AAC3C,0BAAsB,UAAU,MAAM,SAAS,QAAQ,aAAW,QAAQ,UAAU,CAAC,CAAC;AACtF,UAAM,SAAS,KAAK,WAAW,EAAE,MAAM,WAAS,QAAQ,MAAM,kCAAkC,KAAK,CAAC;AAAA,EACxG,EAAC;AACH;AA0CA,SAAS,gBAAgB,SAAS;AAChC,QAAM,WAAW,IAAI,iBAAS,QAAQ,MAAM;AAC5C,QAAM,YAAY,QAAQ,aAAa,CAAC;AACxC,QAAM,iBAAiB,qBAAqB,QAAQ;AACpD,SAAO,yBAAyB,CAAC;AAAA,IAC/B,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG,GAAG,WAAW,gCAAgC,UAAU,OAAO,CAAC,CAAC;AACtE;", "names": ["config", "promise", "KeycloakEventTypeLegacy", "realmRoles", "KeycloakEventType"]}