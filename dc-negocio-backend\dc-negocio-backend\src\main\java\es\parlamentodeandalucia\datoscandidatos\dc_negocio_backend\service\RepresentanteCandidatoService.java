package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.RepresentanteCandidatoEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository.RepresentanteCandidatoRepository;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class RepresentanteCandidatoService {

    private final RepresentanteCandidatoRepository repo;

    public RepresentanteCandidatoService(RepresentanteCandidatoRepository repo) {
        this.repo = repo;
    }

    public List<RepresentanteCandidatoEntity> findAll() {
        return repo.findAll();
    }

    public Optional<RepresentanteCandidatoEntity> findById(Long id) {
        return repo.findById(id);
    }

    public RepresentanteCandidatoEntity create(RepresentanteCandidatoEntity rc) {
        return repo.save(rc);
    }

    public RepresentanteCandidatoEntity update(Long id, RepresentanteCandidatoEntity rc) {
        rc.setId(id);
        return repo.save(rc);
    }

    public void delete(Long id) {
        repo.deleteById(id);
    }
}
