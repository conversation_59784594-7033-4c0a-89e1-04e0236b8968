package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

import java.util.*;

@Service
public class CsvImportService {

    private static final Logger logger = LoggerFactory.getLogger(CsvImportService.class);

    @Autowired
    private CandidaturaService candidaturaService;

    @Autowired
    private CandidatoService candidatoService;

    @Autowired
    private FormacionPoliticaService formacionPoliticaService;

    @Autowired
    private es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository.FormacionPoliticaRepository formacionPoliticaRepository;

    @Autowired
    private CircunscripcionService circunscripcionService;

    @Autowired
    private TipoCandidatoService tipoCandidatoService;

    @Autowired
    private EstadoCandidatoService estadoCandidatoService;

    @Autowired
    private TipoCandidaturaService tipoCandidaturaService;

    @Autowired
    private EstadoCandidaturaService estadoCandidaturaService;

    @Autowired
    private ErrorFileService errorFileService;



    /**
     * Procesa un archivo CSV con el formato específico:
     * codigo interno;Siglas;Formacion politica;Circunscripcion;Orden;Titular/suplente;Orden Candidato;nombre;apellido1;apellido2
     */
    public Map<String, Object> procesarCsv(MultipartFile archivo, String usuarioCreacion, Long formacionPoliticaId) {
        logger.info("🚀 Iniciando procesamiento CSV. Archivo: {}, Usuario: {}",
                   archivo.getOriginalFilename(), usuarioCreacion);

        Map<String, Object> resultado = new HashMap<>();
        List<String> errores = new ArrayList<>();
        int candidatosCreados = 0;
        int candidaturasCreadas = 0;
        Set<String> candidaturasUnicas = new HashSet<>();

        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(archivo.getInputStream(), StandardCharsets.UTF_8))) {
            
            String linea;
            int numeroLinea = 0;
            boolean esPrimeraLinea = true;

            while ((linea = reader.readLine()) != null) {
                numeroLinea++;

                // Saltar la cabecera
                if (esPrimeraLinea) {
                    logger.info("📋 Cabecera CSV: {}", linea);
                    esPrimeraLinea = false;
                    continue;
                }

                logger.info("📝 Procesando línea {}: {}", numeroLinea, linea);
                try {
                    // Procesar candidato individualmente (sin afectar otros candidatos)
                    boolean candidatoCreado = procesarCandidatoIndividual(linea, numeroLinea, usuarioCreacion, candidaturasUnicas, errores);
                    if (candidatoCreado) {
                        candidatosCreados++;
                        logger.info("✅ Candidato creado exitosamente en línea {}", numeroLinea);
                    } else {
                        logger.warn("⚠️ Candidato en línea {} no fue creado (error registrado)", numeroLinea);
                    }
                } catch (Exception e) {
                    logger.error("❌ Error crítico en línea {}: {}", numeroLinea, e.getMessage(), e);
                    errores.add("Línea " + numeroLinea + ": Error crítico - " + e.getMessage());
                }
            }

            candidaturasCreadas = candidaturasUnicas.size();

        } catch (Exception e) {
            errores.add("Error al procesar el archivo: " + e.getMessage());
        }

        // Generar archivo de log (siempre, tanto para errores como para éxitos)
        String archivoLog = null;
        logger.info("📝 Generando archivo de log...");
        archivoLog = errorFileService.generarArchivoErrores(archivo.getOriginalFilename(), errores, usuarioCreacion);

        logger.info("📊 Resultado final - Candidatos: {}, Candidaturas: {}, Errores: {}",
                   candidatosCreados, candidaturasCreadas, errores.size());

        resultado.put("exito", errores.isEmpty());
        resultado.put("candidatosCreados", candidatosCreados);
        resultado.put("candidaturasCreadas", candidaturasCreadas);
        resultado.put("errores", errores);
        resultado.put("archivoLog", archivoLog);

        return resultado;
    }

    private boolean procesarLineaCsv(String linea, int numeroLinea, String usuarioCreacion, Set<String> candidaturasUnicas, FormacionPoliticaEntity formacionAutorizada) {
        logger.info("🔍 Analizando línea {}: {}", numeroLinea, linea);

        // Dividir por punto y coma
        String[] campos = linea.split(";");
        logger.info("📊 Campos extraídos: {} campos encontrados", campos.length);

        if (campos.length < 10) {
            logger.error("❌ Formato incorrecto en línea {}. Se esperan 10 campos, encontrados: {}", numeroLinea, campos.length);
            throw new RuntimeException("Línea incompleta. Se esperan 10 campos, encontrados: " + campos.length);
        }

        try {
            // Extraer campos según el formato
            String codigoInterno = campos[0].trim();
            String siglas = campos[1].trim();
            String nombreFormacion = campos[2].trim();
            String circunscripcionId = campos[3].trim(); // Es un ID numérico
            String orden = campos[4].trim();
            String titularSuplente = campos[5].trim(); // T o S
            String ordenCandidato = campos[6].trim();
            String nombre = campos[7].trim();
            String apellido1 = campos[8].trim();
            String apellido2 = campos[9].trim();

            // Usar la formación política detectada automáticamente (ya validada en detectarFormacionPolitica)
            logger.info("✅ Usando formación política: {} ({}) - Código: {}",
                       formacionAutorizada.getNombre(), formacionAutorizada.getSiglas(), formacionAutorizada.getCodigoInterno());

            // Validar campos obligatorios con detalle de línea
            List<String> erroresLinea = new ArrayList<>();

            if (orden.trim().isEmpty()) {
                erroresLinea.add("Línea " + numeroLinea + ", Campo 'Orden': Es obligatorio");
            }
            if (nombre.trim().isEmpty()) {
                erroresLinea.add("Línea " + numeroLinea + ", Campo 'nombre': Es obligatorio");
            }
            if (apellido1.trim().isEmpty()) {
                erroresLinea.add("Línea " + numeroLinea + ", Campo 'apellido1': Es obligatorio");
            }
            if (!titularSuplente.equals("T") && !titularSuplente.equals("S")) {
                erroresLinea.add("Línea " + numeroLinea + ", Campo 'Titular/suplente': Debe ser 'T' o 'S', encontrado: '" + titularSuplente + "'");
            }
            if (ordenCandidato.trim().isEmpty()) {
                erroresLinea.add("Línea " + numeroLinea + ", Campo 'Orden Candidato': Es obligatorio");
            }

            // Validar que circunscripción sea un número válido
            try {
                Long.parseLong(circunscripcionId);
            } catch (NumberFormatException e) {
                erroresLinea.add("Línea " + numeroLinea + ", Campo 'Circunscripcion': Debe ser un número válido, encontrado: '" + circunscripcionId + "'");
            }

            // Validar que orden sea un número válido
            try {
                Integer.parseInt(orden);
            } catch (NumberFormatException e) {
                erroresLinea.add("Línea " + numeroLinea + ", Campo 'Orden': Debe ser un número válido, encontrado: '" + orden + "'");
            }

            // Validar que orden candidato sea un número válido
            try {
                Integer.parseInt(ordenCandidato);
            } catch (NumberFormatException e) {
                erroresLinea.add("Línea " + numeroLinea + ", Campo 'Orden Candidato': Debe ser un número válido, encontrado: '" + ordenCandidato + "'");
            }

            // Si hay errores en esta línea, lanzar excepción con todos los errores
            if (!erroresLinea.isEmpty()) {
                throw new RuntimeException(String.join("; ", erroresLinea));
            }

            logger.info("✅ Validación de campos obligatorios completada para línea {}", numeroLinea);

            // Usar la formación política autorizada
            FormacionPoliticaEntity formacion = formacionAutorizada;

            // Buscar circunscripción por ID
            logger.info("🔍 Buscando circunscripción con ID: {}", circunscripcionId);
            CircunscripcionEntity circunscripcion;
            try {
                circunscripcion = buscarCircunscripcionPorId(Long.parseLong(circunscripcionId));
                logger.info("✅ Circunscripción encontrada: {}", circunscripcion.getNombre());
            } catch (RuntimeException e) {
                throw new RuntimeException("Línea " + numeroLinea + ", Campo 'Circunscripcion': " + e.getMessage());
            }

            // Crear o buscar candidatura basada en los 4 primeros campos del CSV
            // Una candidatura se define por: código interno + siglas + formación política + circunscripción
            String claveUnicaCandidatura = codigoInterno + "-" + siglas + "-" + nombreFormacion + "-" + circunscripcionId;
            logger.info("🔑 Clave única de candidatura: {}", claveUnicaCandidatura);

            CandidaturaEntity candidatura = crearOBuscarCandidatura(formacion, circunscripcion,
                Integer.parseInt(orden), usuarioCreacion);
            candidaturasUnicas.add(claveUnicaCandidatura);

            logger.info("📋 Candidatura procesada - Total candidaturas únicas: {}", candidaturasUnicas.size());
            
            // Determinar tipo de candidato
            String tipoValor = "T".equals(titularSuplente) ? "TITULAR" : "SUPLENTE";
            
            // Crear candidato
            crearCandidato(candidatura, nombre, apellido1, apellido2, tipoValor, 
                Integer.parseInt(ordenCandidato), usuarioCreacion);
            
            return true;
            
        } catch (NumberFormatException e) {
            throw new RuntimeException("Error en formato numérico: " + e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException("Error al procesar datos: " + e.getMessage());
        }
    }

    /**
     * Procesa un candidato individual sin afectar la transacción global
     * Retorna true si el candidato fue creado exitosamente, false si hubo errores
     */
    private boolean procesarCandidatoIndividual(String linea, int numeroLinea, String usuarioCreacion, Set<String> candidaturasUnicas, List<String> errores) {
        try {
            // Intentar procesar el candidato
            return procesarLineaCsvConDeteccionAutomatica(linea, numeroLinea, usuarioCreacion, candidaturasUnicas);
        } catch (Exception e) {
            // Registrar error específico del candidato
            String mensajeError = "Línea " + numeroLinea + ": " + e.getMessage();
            logger.error("❌ Error en candidato individual: {}", mensajeError);

            // Añadir error a la lista para el archivo de log
            errores.add(mensajeError);

            return false;
        }
    }

    /**
     * Procesa una línea del CSV con detección automática de formación política
     */
    private boolean procesarLineaCsvConDeteccionAutomatica(String linea, int numeroLinea, String usuarioCreacion, Set<String> candidaturasUnicas) {
        logger.info("🔍 Analizando línea {}: {}", numeroLinea, linea);

        // Dividir por punto y coma
        String[] campos = linea.split(";");
        logger.info("📊 Campos extraídos: {} campos encontrados", campos.length);

        if (campos.length < 10) {
            logger.error("❌ Formato incorrecto en línea {}. Se esperan 10 campos, encontrados: {}", numeroLinea, campos.length);
            throw new RuntimeException("Línea incompleta. Se esperan 10 campos, encontrados: " + campos.length);
        }

        try {
            // Detectar formación política para esta línea específica
            FormacionPoliticaEntity formacionLinea = detectarFormacionPolitica(linea, numeroLinea);
            logger.info("✅ Formación detectada para línea {}: {} ({})", numeroLinea, formacionLinea.getNombre(), formacionLinea.getSiglas());

            // Procesar la línea con la formación detectada
            return procesarLineaCsv(linea, numeroLinea, usuarioCreacion, candidaturasUnicas, formacionLinea);

        } catch (Exception e) {
            throw new RuntimeException("Error al procesar datos: " + e.getMessage(), e);
        }
    }

    /**
     * Detecta la formación política desde cualquier línea del CSV
     */
    private FormacionPoliticaEntity detectarFormacionPolitica(String linea, int numeroLinea) {
        String[] campos = linea.split(";");
        if (campos.length < 10) {
            throw new RuntimeException("Línea " + numeroLinea + ": Formato incorrecto para detectar formación política");
        }

        String codigoInterno = campos[0].trim();
        String siglas = campos[1].trim();
        String nombreFormacion = campos[2].trim();

        logger.info("🔍 Detectando formación política - Código: {}, Siglas: {}, Nombre: {}",
                   codigoInterno, siglas, nombreFormacion);

        // Buscar por código interno primero
        Optional<FormacionPoliticaEntity> porCodigo = formacionPoliticaRepository.findByCodigoInterno(codigoInterno);
        if (porCodigo.isPresent()) {
            FormacionPoliticaEntity formacion = porCodigo.get();
            logger.info("✅ Formación encontrada por código interno: {}", formacion.getNombre());
            return formacion;
        }

        // Si no se encuentra por código, buscar por siglas
        Optional<FormacionPoliticaEntity> porSiglas = formacionPoliticaRepository.findBySiglas(siglas);
        if (porSiglas.isPresent()) {
            FormacionPoliticaEntity formacion = porSiglas.get();
            logger.warn("⚠️ Formación encontrada por siglas pero código interno no coincide. CSV: {}, BD: {}",
                       codigoInterno, formacion.getCodigoInterno());
            return formacion;
        }

        // Si no se encuentra, error
        throw new RuntimeException("Línea " + numeroLinea + ": Formación política no encontrada. " +
                                 "Código: '" + codigoInterno + "', Siglas: '" + siglas + "', Nombre: '" + nombreFormacion + "'");
    }

    private FormacionPoliticaEntity crearOBuscarFormacionPolitica(String nombre, String siglas, String codigoInterno) {
        // Buscar por nombre o siglas primero
        Optional<FormacionPoliticaEntity> existente = formacionPoliticaRepository.findByNombre(nombre);
        if (!existente.isPresent()) {
            existente = formacionPoliticaRepository.findBySiglas(siglas);
        }

        if (existente.isPresent()) {
            return existente.get();
        }

        // Si no existe, lanzar error con información detallada de la entrada
        throw new RuntimeException("Formación política no encontrada - Código interno: '" + codigoInterno +
                                 "', Siglas: '" + siglas + "', Nombre: '" + nombre + "'. " +
                                 "Las formaciones políticas deben existir previamente en la base de datos.");
    }

    private CircunscripcionEntity buscarCircunscripcionPorId(Long id) {
        Optional<CircunscripcionEntity> existente = circunscripcionService.findById(id);

        if (existente.isPresent()) {
            return existente.get();
        }

        // Si no existe, lanzar error - las circunscripciones deben existir previamente
        throw new RuntimeException("Circunscripción no encontrada con ID: " + id + ". " +
                                 "Las circunscripciones deben existir previamente en la base de datos.");
    }

    private CandidaturaEntity crearOBuscarCandidatura(FormacionPoliticaEntity formacion,
                                                     CircunscripcionEntity circunscripcion,
                                                     int orden,
                                                     String usuarioCreacion) {
        Optional<CandidaturaEntity> existente = candidaturaService.findByFormacionPoliticaAndCircunscripcion(
            formacion.getId(), circunscripcion.getId());

        if (existente.isPresent()) {
            return existente.get();
        }

        // Si no existe, crear nueva usando el constructor
        CandidaturaEntity nueva = new CandidaturaEntity(formacion, circunscripcion, orden, usuarioCreacion);

        // Asignar tipo IMPORTADA y estado PROCLAMADA para candidaturas de CSV
        nueva.setTipoCandidatura(tipoCandidaturaService.findByValor("IMPORTADA")
            .orElseThrow(() -> new RuntimeException("Tipo IMPORTADA no encontrado")));
        nueva.setEstadoCandidatura(estadoCandidaturaService.findByValor("PROCLAMADA")
            .orElseThrow(() -> new RuntimeException("Estado PROCLAMADA no encontrado")));

        return candidaturaService.save(nueva);
    }

    @Transactional
    private void crearCandidato(CandidaturaEntity candidatura, String nombre, String apellido1,
                               String apellido2, String tipoCandidato, int ordenCandidato, String usuarioCreacion) {

        // Validar que solo haya 1 titular y 1 suplente por orden en la misma candidatura
        validarTitularidadPorOrden(candidatura, tipoCandidato, ordenCandidato);

        // Crear candidato con los campos disponibles
        CandidatoEntity candidato = new CandidatoEntity();
        candidato.setNombre(nombre);
        candidato.setApellido1(apellido1);
        candidato.setApellido2(apellido2);
        candidato.setOrden((long) ordenCandidato);
        candidato.setCandidatura(candidatura);
        candidato.setUsuarioCreacion(usuarioCreacion);

        // Asignar tipo según T/S del CSV
        TipoCandidatoEntity tipo = tipoCandidatoService.findByValor(tipoCandidato)
            .orElseThrow(() -> new RuntimeException("Tipo de candidato no encontrado: " + tipoCandidato));
        candidato.setTipo(tipo);

        // Asignar estado PROCLAMADO para importaciones
        EstadoCandidatoEntity estado = estadoCandidatoService.findByValor("PROCLAMADO")
            .orElseThrow(() -> new RuntimeException("Estado PROCLAMADO no encontrado"));
        candidato.setEstado(estado);

        candidatoService.save(candidato);
    }

    private void validarTitularidadPorOrden(CandidaturaEntity candidatura, String tipoCandidato, int ordenCandidato) {
        // Buscar candidatos existentes con el mismo orden en la misma candidatura
        List<CandidatoEntity> candidatosExistentes = candidatoService.findByCandidaturaAndOrden(candidatura.getId(), (long) ordenCandidato);

        for (CandidatoEntity existente : candidatosExistentes) {
            if (existente.getTipo().getValor().equals(tipoCandidato)) {
                throw new RuntimeException("Ya existe un candidato " + tipoCandidato.toLowerCase() +
                    " con orden " + ordenCandidato + " en esta candidatura");
            }
        }

        // Validar que no haya más de 2 candidatos por orden (1 titular + 1 suplente máximo)
        if (candidatosExistentes.size() >= 2) {
            throw new RuntimeException("Ya existen 2 candidatos (titular y suplente) con orden " + ordenCandidato +
                " en esta candidatura. No se pueden añadir más.");
        }
    }
}
