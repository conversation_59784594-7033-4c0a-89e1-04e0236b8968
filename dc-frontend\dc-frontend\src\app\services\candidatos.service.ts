import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

export interface CandidatoEntity {
  id: number;
  nombre: string;
  apellido1: string;
  apellido2: string;
  orden: number;
  tipo?: { id: number; valor?: string };
  estado?: { id: number; valor?: string };
}

export interface TipoCandidato { id: number; valor: string; descripcion?: string }
export interface EstadoCandidato { id: number; valor: string }

export interface CandidatoUpsert {
  id?: number;
  nombre: string;
  apellido1: string;
  apellido2?: string;
  orden?: number;
  tipo?: { id: number };
  estado?: { id: number };
  candidatura: { id: number };
}

@Injectable({ providedIn: 'root' })
export class CandidatosService {
  private readonly apiUrl = environment.apiUrl + '/candidatos';
  private readonly tiposUrl = environment.apiUrl + '/tipos-candidato';
  private readonly estadosUrl = environment.apiUrl + '/estados-candidato';

  constructor(private http: HttpClient) {}

  getByCandidatura(candidaturaId: number): Observable<CandidatoEntity[]> {
    return this.http.get<CandidatoEntity[]>(`${this.apiUrl}/candidatura/${candidaturaId}`);
  }

  create(payload: CandidatoUpsert): Observable<CandidatoEntity> {
    return this.http.post<CandidatoEntity>(this.apiUrl, payload);
  }

  update(id: number, payload: CandidatoUpsert): Observable<CandidatoEntity> {
    return this.http.put<CandidatoEntity>(`${this.apiUrl}/${id}`, payload);
  }

  delete(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }

  getTipos(): Observable<TipoCandidato[]> {
    return this.http.get<TipoCandidato[]>(this.tiposUrl);
  }

  getEstados(): Observable<EstadoCandidato[]> {
    return this.http.get<EstadoCandidato[]>(this.estadosUrl);
  }
}

