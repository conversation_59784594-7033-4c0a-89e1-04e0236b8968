/* styles.css */

/* ===== Layout general ===== */
:host,
.home-layout {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  background-color: #f9f9f9;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1200px;    
  margin: 0 auto;        
  padding: 20px 24px;  
  overflow-y: auto;
}


/* ==== Estadísticas ==== */
.stats-cards {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 16px;
  margin-bottom: 24px;
  overflow: visible;
}

.card-summary {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  padding: 12px 16px;
}
.card-icon {
  font-size: 24px;
  color: #93c01f;
  margin-right: 16px;
  flex-shrink: 0;
}
.card-info { display: flex; flex-direction: column; }
.card-title { font-size: 14px; color: #666; }
.card-value { font-size: 20px; font-weight: 600; color: #222; }
.card-growth { font-size: 12px; margin-top: 4px; color: #28a745; }
.card-growth.negative { color: #e11d48; }

/* ==== Dashboard Panels con Grid Áreas ==== */
.dashboard-panels {
  display: grid;
  grid-template-columns: 2fr 1fr;
  grid-template-rows: auto auto auto;
  grid-template-areas:
    "ultimos parlamento"
    "introducido correos"
    ". usuarios-recientes";
  gap: 20px;
  overflow: visible;
}

.panel-ultimos            { grid-area: ultimos; }
.panel-parlamento         { grid-area: parlamento; }
.panel-introducido        { grid-area: introducido; }
.panel-correos            { grid-area: correos; }
.panel-usuarios-recientes { grid-area: usuarios-recientes; }

.panel {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}
.panel-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}
.panel-header h3 {
  margin: 0;
  font-size: 16px;
}

/* ==== Botones “Ver todo” ==== */
.btn-panel {
  margin-left: auto;
  color: #4A4A4A;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
}

/* ==== Tablas ==== */
.table-simple {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0 ;
  padding: 12px;
  text-align: left;
  font-size: 14px;
}

/* ==== Etiquetas de estado y rol ==== */
.status {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
}
.status.active   { background: #d1f7d8; color: #28a745; }
.status.inactive { background: #f8d7da; color: #e11d48; }

.tag {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
}

/* ==== Gráficas ==== */
.charts-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-top: 12px;
}
.chart-placeholder {
  height: 120px;
  background: #f0f0f0;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 14px;
}
