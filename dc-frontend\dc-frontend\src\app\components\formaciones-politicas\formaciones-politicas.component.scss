:host {
  display: block;
  font-family: 'Inter', sans-serif;
}

.layout-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: auto;
}

.page-wrapper {
  margin-top: 60px;
  margin-bottom: 50px;
  overflow-y: auto;
  background-color: #f8f8f8;
  padding: 0;
}

.breadcrumbs {
  margin: 10px auto;
  max-width: 1400px;
  width: calc(100% - 40px);
  font-size: 14px;
  color: #666;
  display: flex;
  align-items: center;
}

.breadcrumbs .separator {
  margin: 0 5px;
  color: #999;
}

.breadcrumbs strong {
  color: #333;
  font-weight: 600;
}

.title-section {
  margin: 0 auto 20px auto;
  max-width: 1400px;
  width: calc(100% - 40px);
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: nowrap;
}

.title-section .back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  color: #333;
  text-decoration: none;
  padding: 0;
  flex-shrink: 0;
}

.title-section .back-button svg {
  width: 24px;
  height: 24px;
  color: #555;
}

.title-section .page-main-title {
  font-size: 28px;
  font-weight: 600;
  margin-left: 5px;
  white-space: nowrap;
  flex-shrink: 0;
}

.title-section h1 {
  font-size: 28px;
  font-weight: 600;
  margin-left: 5px;
  flex-grow: 1;
  white-space: nowrap;
}

.title-section .actions {
  flex-shrink: 0;
}

.title-section .btn-primary {
  background-color: #7aa12d;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-section .btn-primary svg {
  width: 20px;
  height: 20px;
}

.usuarios-page-container {
  flex: 1;
  background-color: #fff;
  padding: 16px;
  margin: 0 auto 20px auto;
  max-width: 1400px;
  width: 1400px;
  display: flex;
  flex-direction: column;
}

.filter-actions-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.filter-actions-bar > * {
  margin: 4px 8px 4px 0;
}

.btn-text {
  background: none;
  border: none;
  color: #7aa12d;
  font-weight: 500;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
}

.btn-text:hover {
  text-decoration: underline;
}

.dropdown-masivo {
  position: relative;
}

.btn-massive {
  display: flex;
  align-items: center;
  background-color: white;
  border: 1px solid #b5cc63;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 14px;
  font-weight: 500;
  color: #7aa12d;
  cursor: pointer;
  gap: 6px;
}

.btn-massive svg {
  width: 14px;
  height: 14px;
  margin-left: 4px;
}

.btn-massive:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    border-color: #cccccc;
    color: #999;
    background-color: #f0f0f0;
}

.dropdown-options {
  position: absolute;
  top: 110%;
  right: 0;
  background-color: white;
  border-radius: 6px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  list-style: none;
  padding: 8px 0;
  margin: 0;
  width: 160px;
  z-index: 1000;
}

.dropdown-options li {
  padding: 8px 16px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.dropdown-options li:hover {
  background-color: #f4f9f2;
}


::ng-deep .p-datatable {
  font-size: 13px;
}

::ng-deep .p-datatable .p-datatable-tbody > tr.row-par {
  background-color: white;
}

::ng-deep .p-datatable .p-datatable-tbody > tr.row-impar {
  background-color: #F5F5F5;
}

::ng-deep .p-datatable .p-datatable-tbody > tr:hover {
  background-color: #eef7e4 !important;
}

::ng-deep .p-datatable .p-datatable-tbody > tr.p-highlight {
  background-color: #FAFFF0 !important;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.status-yes {
  background-color: #e6ffe6;
  color: #28a745;
}

.status-no {
  background-color: #ffe6e6;
  color: #dc3545;
}

.acciones-cell {
  position: relative;
}

.dropdown {
  position: relative;
  display: inline-block;
}

.btn-icon.dropdown-toggle {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  background-color: transparent;
  border: none;
  padding: 6px 10px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  color: #2d2d2d;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.btn-icon.dropdown-toggle:hover {
  background-color: #FAFFF0;
}

.btn-icon.dropdown-toggle svg {
  width: 16px;
  height: 16px;
  margin-left: 4px;
  vertical-align: middle;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  z-index: 1000;
  background-color: white;
  border-radius: 6px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  min-width: 140px;
  margin-top: 4px;
  padding: 4px 0;
}

.dropdown-item {
  display: block;
  padding: 8px 16px;
  font-size: 14px;
  color: #333;
  text-align: left;
  cursor: pointer;
  white-space: nowrap;
}

.dropdown-item:hover {
  background-color: #f4f9f2;
}

.footer-fixed {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 10;
  background-color: #fff;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
  height: 50px;
}

.header-fixed {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 10;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  height: 60px;
}

::ng-deep .p-paginator {
  border: none;
  background: transparent;
  padding: 16px 0;
  justify-content: flex-end;
}

::ng-deep .p-paginator .p-paginator-page {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 50%;
  color: #333;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 4px;
  font-size: 14px;
}

::ng-deep .p-paginator .p-paginator-page.p-highlight {
  background-color: #e6f4e6;
  color: #7aa12d;
  font-weight: 600;
}

::ng-deep .p-paginator .p-paginator-page:hover {
  background-color: #f4f9f2;
}

::ng-deep .p-paginator .p-paginator-first,
::ng-deep .p-paginator .p-paginator-prev,
::ng-deep .p-paginator .p-paginator-next,
::ng-deep .p-paginator .p-paginator-last {
  background-color: transparent;
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #555;
  transition: background-color 0.2s ease;
}

::ng-deep .p-paginator .p-paginator-first:hover,
::ng-deep .p-paginator .p-paginator-prev:hover,
::ng-deep .p-paginator .p-paginator-next:hover,
::ng-deep .p-paginator .p-paginator-last:hover {
  background-color: #f4f9f2;
}

::ng-deep .p-paginator .p-dropdown {
  border: 1px solid #b5cc63;
  border-radius: 8px;
  padding: 2px 8px;
  margin-left: 16px;
  font-size: 14px;
  font-weight: 500;
  color: #7aa12d;
  box-shadow: none;
  height: 32px;
}

.p-dropdown-panel {
  z-index: 9999 !important;
}

:host ::ng-deep .p-column-header-content {
  display: flex;
  align-items: center;
  gap: 6px;
  flex-wrap: nowrap;
}

:host ::ng-deep .p-sortable-column-icon,
:host ::ng-deep .p-column-filter-menu-button {
  margin-top: 0 !important;
  position: static !important;
}

:host ::ng-deep .p-column-header {
  white-space: nowrap;
  padding-top: 8px !important;
  padding-bottom: 8px !important;
}

:host ::ng-deep .p-column-filter-menu-button {
  display: inline-flex;
  align-items: center;
  height: 20px;
}

/* Sidebar styles */
p-sidebar {
    ::ng-deep .p-sidebar {
        padding: 20px;
    }
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.form-group input, .form-group p-dropdown {
    width: 100%;
}

.form-group p-dropdown ::ng-deep .p-dropdown {
    width: 100%;
}

.checkbox-group {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.sidebar-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.sidebar-footer .btn-secondary {
    background-color: #fff;
    color: #7aa12d;
    border: 1px solid #7aa12d;
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: 500;
    cursor: pointer;
}