package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.controller;

import es.parlamentodeandalucia.datoscandidatos.dto.Representante;
import es.parlamentodeandalucia.datoscandidatos.dto.Candidato;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/api/representantes")
@CrossOrigin(origins = {"http://localhost:4200", "https://localhost:4200"})
public class RepresentanteController {

    @GetMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('REPRESENTANTE')")
    public ResponseEntity<List<Representante>> getRepresentantes(@AuthenticationPrincipal Jwt jwt) {
        
        // Datos de ejemplo
        List<Representante> representantes = new ArrayList<>();
        
        Representante rep1 = new Representante()
            .id(1)
            .nombre("Juan Pérez")
            .email("<EMAIL>")
            .circunscripcion("Sevilla");
        representantes.add(rep1);
        
        Representante rep2 = new Representante()
            .id(2)
            .nombre("María García")
            .email("<EMAIL>")
            .circunscripcion("Málaga");
        representantes.add(rep2);
        
        return ResponseEntity.ok(representantes);
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('REPRESENTANTE')")
    public ResponseEntity<Representante> getRepresentante(@PathVariable Integer id, @AuthenticationPrincipal Jwt jwt) {
        
        Representante representante = new Representante()
            .id(id)
            .nombre("Representante " + id)
            .email("representante" + id + "@parlamento.es")
            .circunscripcion("Circunscripción " + id);
        
        return ResponseEntity.ok(representante);
    }

    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Representante> createRepresentante(
            @RequestBody Representante representante,
            @AuthenticationPrincipal Jwt jwt) {
        
        representante.setId((int) System.currentTimeMillis());
        return ResponseEntity.status(201).body(representante);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Representante> updateRepresentante(
            @PathVariable Integer id,
            @RequestBody Representante representante,
            @AuthenticationPrincipal Jwt jwt) {
        
        representante.setId(id);
        return ResponseEntity.ok(representante);
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteRepresentante(@PathVariable Integer id, @AuthenticationPrincipal Jwt jwt) {
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/{id}/candidatos")
    @PreAuthorize("hasRole('ADMIN') or hasRole('REPRESENTANTE')")
    public ResponseEntity<List<Candidato>> getCandidatosByRepresentante(
            @PathVariable Integer id,
            @AuthenticationPrincipal Jwt jwt) {
        
        // Datos de ejemplo
        List<Candidato> candidatos = new ArrayList<>();
        
        Candidato candidato1 = new Candidato()
            .id(1)
            .nombre("Ana")
            .apellidos("López Martín")
            .circunscripcion("Sevilla")
            .formacionPolitica("Partido A")
            .estado(Candidato.EstadoEnum.PENDIENTE);
        candidatos.add(candidato1);
        
        return ResponseEntity.ok(candidatos);
    }
}
