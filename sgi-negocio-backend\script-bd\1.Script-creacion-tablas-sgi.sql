-- ========================================
-- SCRIPT SGI - SISTEMA DE GESTIÓN DE IDENTIDADES
-- Base de datos externa para Keycloak User Storage SPI
-- ========================================

-- ========================================
-- TABLAS MAESTRAS
-- ========================================

-- Tabla de tipos de identificadores
CREATE TABLE IF NOT EXISTS sgi_t_tipo_identificador (
    sgi_id_tipo_identificador INT8 NOT NULL,
    sgi_tx_valor VARCHAR(255) UNIQUE,
    CONSTRAINT sgi_t_tipo_identificador_pkey PRIMARY KEY (sgi_id_tipo_identificador)
);

-- Tabla de estados
CREATE TABLE IF NOT EXISTS sgi_t_estado (
    sgi_id_estado INT8 NOT NULL,
    sgi_tx_valor VARCHAR(255) UNIQUE,
    CONSTRAINT sgi_t_estado_pkey PRIMARY KEY (sgi_id_estado)
);

-- ========================================
-- TABLA PRINCIPAL DE IDENTIDADES
-- ========================================

-- Tabla principal de identidades
CREATE TABLE IF NOT EXISTS sgi_t_identidad (
    sgi_id_identidad INT8 NOT NULL,
    sgi_tx_nombre VARCHAR(255),
    sgi_tx_apellido1 VARCHAR(255),
    sgi_tx_apellido2 VARCHAR(255),
    sgi_fk_tipo_identificador INT8,
    sgi_tx_identificador VARCHAR(255) UNIQUE,
    sgi_tx_email VARCHAR(255),
    sgi_tx_movil VARCHAR(50),
    CONSTRAINT sgi_t_identidad_pkey PRIMARY KEY (sgi_id_identidad),
    CONSTRAINT sgi_t_identidad_tipo_identificador_fkey
        FOREIGN KEY (sgi_fk_tipo_identificador)
        REFERENCES sgi_t_tipo_identificador(sgi_id_tipo_identificador)
);

-- ========================================
-- TABLA DE CREDENCIALES (MAPEA CON KEYCLOAK)
-- ========================================

-- Tabla de credenciales - MAPEA CON KEYCLOAK
CREATE TABLE IF NOT EXISTS sgi_t_credencial (
    sgi_id_credencial INT8 NOT NULL,
    sgi_tx_username VARCHAR(255) NOT NULL UNIQUE,
    sgi_tx_password VARCHAR(255),
    sgi_fk_estado INT8,
    sgi_fh_alta DATE,
    sgi_fh_baja DATE,
    sgi_fk_identidad INT8,  -- Referencia a sgi_t_identidad
    CONSTRAINT sgi_t_credencial_pkey PRIMARY KEY (sgi_id_credencial),
    CONSTRAINT sgi_t_credencial_estado_fkey
        FOREIGN KEY (sgi_fk_estado)
        REFERENCES sgi_t_estado(sgi_id_estado),
    CONSTRAINT sgi_t_credencial_identidad_fkey
        FOREIGN KEY (sgi_fk_identidad)
        REFERENCES sgi_t_identidad(sgi_id_identidad)
);

-- ========================================
-- TABLAS DE GESTIÓN DE ROLES Y PERMISOS
-- ========================================

-- Tabla de funcionalidades
CREATE TABLE IF NOT EXISTS sgi_t_funcionalidad (
    sgi_id_funcionalidad INT8 NOT NULL,
    sgi_tx_codigo VARCHAR(50) UNIQUE,
    sgi_tx_descripcion VARCHAR(255) UNIQUE,
    sgi_bl_activa BOOL,
    CONSTRAINT sgi_t_funcionalidad_pkey PRIMARY KEY (sgi_id_funcionalidad)
);

-- Tabla de permisos
CREATE TABLE IF NOT EXISTS sgi_t_permiso (
    sgi_id_permiso INT8 NOT NULL,
    sgi_tx_codigo VARCHAR(50) UNIQUE,
    sgi_tx_descripcion VARCHAR(255) UNIQUE,
    sgi_fk_funcionalidad INT8,
    sgi_bl_activo BOOL,
    CONSTRAINT sgi_t_permiso_pkey PRIMARY KEY (sgi_id_permiso),
    CONSTRAINT sgi_t_permiso_funcionalidad_fkey
        FOREIGN KEY (sgi_fk_funcionalidad)
        REFERENCES sgi_t_funcionalidad(sgi_id_funcionalidad)
);

-- Tabla de roles
CREATE TABLE IF NOT EXISTS sgi_t_rol (
    sgi_id_rol INT8 NOT NULL,
    sgi_tx_codigo VARCHAR(50) UNIQUE,
    sgi_tx_descripcion VARCHAR(255) UNIQUE,
    sgi_bl_activo BOOL,
    CONSTRAINT sgi_t_rol_pkey PRIMARY KEY (sgi_id_rol)
);

-- Tabla de relación roles-permisos
CREATE TABLE IF NOT EXISTS sgi_t_rol_permiso (
    sgi_id_rol_permiso INT8 NOT NULL,
    sgi_fk_rol INT8,
    sgi_fk_permiso INT8,
    CONSTRAINT sgi_t_rol_permiso_pkey PRIMARY KEY (sgi_id_rol_permiso),
    CONSTRAINT sgi_t_rol_permiso_rol_fkey
        FOREIGN KEY (sgi_fk_rol)
        REFERENCES sgi_t_rol(sgi_id_rol),
    CONSTRAINT sgi_t_rol_permiso_permiso_fkey
        FOREIGN KEY (sgi_fk_permiso)
        REFERENCES sgi_t_permiso(sgi_id_permiso)
);

-- ========================================
-- TABLAS DE USUARIOS Y ASIGNACIONES
-- ========================================

-- Tabla de usuarios del SGI
CREATE TABLE IF NOT EXISTS sgi_t_usuario (
    sgi_id_usuario INT8 NOT NULL,
    sgi_tx_ldap_uid VARCHAR(255),
    sgi_fh_alta DATE,
    sgi_fh_baja DATE,
    sgi_fh_last_login DATE,
    sgi_bl_activo BOOL,
    CONSTRAINT sgi_t_usuario_pkey PRIMARY KEY (sgi_id_usuario)
);

-- Tabla de relación usuarios-roles
CREATE TABLE IF NOT EXISTS sgi_t_usuario_rol (
    sgi_id_usuario_rol INT8 NOT NULL,
    sgi_fk_usuario INT8,
    sgi_fk_rol INT8,
    sgi_bl_defecto BOOL,
    CONSTRAINT sgi_t_usuario_rol_pkey PRIMARY KEY (sgi_id_usuario_rol),
    CONSTRAINT sgi_t_usuario_rol_usuario_fkey
        FOREIGN KEY (sgi_fk_usuario)
        REFERENCES sgi_t_usuario(sgi_id_usuario),
    CONSTRAINT sgi_t_usuario_rol_rol_fkey
        FOREIGN KEY (sgi_fk_rol)
        REFERENCES sgi_t_rol(sgi_id_rol)
);

-- ========================================
-- ÍNDICES PARA OPTIMIZACIÓN
-- ========================================

-- Índices para identidades
CREATE INDEX IF NOT EXISTS idx_sgi_identidad_identificador ON sgi_t_identidad(sgi_tx_identificador);
CREATE INDEX IF NOT EXISTS idx_sgi_identidad_email ON sgi_t_identidad(sgi_tx_email);
CREATE INDEX IF NOT EXISTS idx_sgi_identidad_tipo ON sgi_t_identidad(sgi_fk_tipo_identificador);

-- Índices para credenciales
CREATE INDEX IF NOT EXISTS idx_sgi_credencial_username ON sgi_t_credencial(sgi_tx_username);
CREATE INDEX IF NOT EXISTS idx_sgi_credencial_estado ON sgi_t_credencial(sgi_fk_estado);
CREATE INDEX IF NOT EXISTS idx_sgi_credencial_identidad ON sgi_t_credencial(sgi_fk_identidad);

-- Índices para roles y permisos
CREATE INDEX IF NOT EXISTS idx_sgi_permiso_funcionalidad ON sgi_t_permiso(sgi_fk_funcionalidad);
CREATE INDEX IF NOT EXISTS idx_sgi_rol_permiso_rol ON sgi_t_rol_permiso(sgi_fk_rol);
CREATE INDEX IF NOT EXISTS idx_sgi_rol_permiso_permiso ON sgi_t_rol_permiso(sgi_fk_permiso);
CREATE INDEX IF NOT EXISTS idx_sgi_usuario_rol_usuario ON sgi_t_usuario_rol(sgi_fk_usuario);
CREATE INDEX IF NOT EXISTS idx_sgi_usuario_rol_rol ON sgi_t_usuario_rol(sgi_fk_rol);

-- ========================================
-- COMENTARIOS DE DOCUMENTACIÓN
-- ========================================

-- Tablas maestras
COMMENT ON TABLE sgi_t_tipo_identificador IS 'Tipos de identificadores: NIF, NIE, CIF';
COMMENT ON TABLE sgi_t_estado IS 'Estados del sistema: ACTIVO, BLOQUEADO';

-- Tabla principal
COMMENT ON TABLE sgi_t_identidad IS 'Tabla principal de identidades del SGI';
COMMENT ON COLUMN sgi_t_identidad.sgi_tx_movil IS 'Número de teléfono móvil';

-- Tabla de credenciales (mapea con Keycloak)
COMMENT ON TABLE sgi_t_credencial IS 'Tabla de credenciales que mapea con Keycloak User Storage SPI';
COMMENT ON COLUMN sgi_t_credencial.sgi_tx_username IS 'Nombre de usuario único (mapea con Keycloak)';
COMMENT ON COLUMN sgi_t_credencial.sgi_fk_identidad IS 'Referencia a la identidad asociada';

-- Tablas de roles y permisos
COMMENT ON TABLE sgi_t_funcionalidad IS 'Funcionalidades del sistema SGI';
COMMENT ON TABLE sgi_t_permiso IS 'Permisos específicos por funcionalidad';
COMMENT ON TABLE sgi_t_rol IS 'Roles del sistema SGI';
COMMENT ON TABLE sgi_t_rol_permiso IS 'Relación muchos a muchos entre roles y permisos';

-- Tablas de usuarios
COMMENT ON TABLE sgi_t_usuario IS 'Usuarios del sistema SGI';
COMMENT ON TABLE sgi_t_usuario_rol IS 'Asignación de roles a usuarios';

-- ========================================
-- DATOS INICIALES DE PRUEBA
-- ========================================

-- Tipos de identificadores
INSERT INTO sgi_t_tipo_identificador (sgi_id_tipo_identificador, sgi_tx_valor) VALUES
(1, 'NIF'),
(2, 'NIE'),
(3, 'CIF')
ON CONFLICT (sgi_id_tipo_identificador) DO NOTHING;

-- Estados
INSERT INTO sgi_t_estado (sgi_id_estado, sgi_tx_valor) VALUES
(1, 'ACTIVO'),
(2, 'BLOQUEADO')
ON CONFLICT (sgi_id_estado) DO NOTHING;

-- Funcionalidades del sistema
INSERT INTO sgi_t_funcionalidad (sgi_id_funcionalidad, sgi_tx_codigo, sgi_tx_descripcion, sgi_bl_activa) VALUES
(1, 'USUARIOS', 'GESTIÓN DE USUARIOS', true),
(2, 'ROLES', 'GESTIÓN DE ROLES', true),
(3, 'PERMISOS', 'GESTIÓN DE PERMISOS', true),
(4, 'FUNCIONALIDADES', 'GESTIÓN DE FUNCIONALIDADES', true),
(5, 'IDENTIDADES', 'GESTIÓN DE IDENTIDADES', true)
ON CONFLICT (sgi_id_funcionalidad) DO NOTHING;

-- Permisos básicos
INSERT INTO sgi_t_permiso (sgi_id_permiso, sgi_tx_codigo, sgi_tx_descripcion, sgi_fk_funcionalidad, sgi_bl_activo) VALUES
(1, 'CREAR', 'CREAR', 1, true),
(2, 'CONSULTAR', 'CONSULTAR', 1, true),
(3, 'EDITAR', 'EDITAR', 1, true),
(4, 'ELIMINAR', 'ELIMINAR', 1, true),
(5, 'ACTIVAR', 'ACTIVAR', 1, true),
(6, 'DESACTIVAR', 'DESACTIVAR', 1, true)
ON CONFLICT (sgi_id_permiso) DO NOTHING;

-- Rol de administrador del sistema
INSERT INTO sgi_t_rol (sgi_id_rol, sgi_tx_codigo, sgi_tx_descripcion, sgi_bl_activo) VALUES
(1, 'ADMIN', 'ADMINISTRADOR DEL SISTEMA', true)
ON CONFLICT (sgi_id_rol) DO NOTHING;

-- Asignar todos los permisos al rol admin
INSERT INTO sgi_t_rol_permiso (sgi_id_rol_permiso, sgi_fk_rol, sgi_fk_permiso) VALUES
(1, 1, 1),
(2, 1, 2),
(3, 1, 3),
(4, 1, 4),
(5, 1, 5),
(6, 1, 6)
ON CONFLICT (sgi_id_rol_permiso) DO NOTHING;

-- Identidades de prueba
INSERT INTO sgi_t_identidad (
    sgi_id_identidad,
    sgi_tx_nombre,
    sgi_tx_apellido1,
    sgi_tx_apellido2,
    sgi_fk_tipo_identificador,
    sgi_tx_identificador,
    sgi_tx_email,
    sgi_tx_movil
) VALUES
(1, 'Administrador', 'SGI', 'Sistema', 1, '12345678A', '<EMAIL>', '600123456'),
(2, 'Usuario', 'Prueba', 'Test', 1, '87654321B', '<EMAIL>', '600654321'),
(3, 'Validador', 'SGI', 'Datos', 1, '11223344C', '<EMAIL>', '600112233')
ON CONFLICT (sgi_id_identidad) DO NOTHING;

-- Credenciales de prueba (contraseñas: "password123" hasheada con BCrypt)
-- Hash BCrypt de "password123": $2a$10$N9qo8uLOickgx2ZMRZoMye1J8.6rOGTQYI8Z8K3OFqZr5.QdwF262
INSERT INTO sgi_t_credencial (
    sgi_id_credencial,
    sgi_tx_username,
    sgi_tx_password,
    sgi_fk_estado,
    sgi_fh_alta,
    sgi_fk_identidad
) VALUES
(1, 'admin.sgi', '$2a$10$N9qo8uLOickgx2ZMRZoMye1J8.6rOGTQYI8Z8K3OFqZr5.QdwF262', 1, CURRENT_DATE, 1),
(2, 'usuario.test', '$2a$10$N9qo8uLOickgx2ZMRZoMye1J8.6rOGTQYI8Z8K3OFqZr5.QdwF262', 1, CURRENT_DATE, 2),
(3, 'validador.sgi', '$2a$10$N9qo8uLOickgx2ZMRZoMye1J8.6rOGTQYI8Z8K3OFqZr5.QdwF262', 1, CURRENT_DATE, 3)
ON CONFLICT (sgi_id_credencial) DO NOTHING;

-- Usuarios del SGI
INSERT INTO sgi_t_usuario (sgi_id_usuario, sgi_tx_ldap_uid, sgi_fh_alta, sgi_bl_activo) VALUES
(1, 'admin.sgi', CURRENT_DATE, true),
(2, 'usuario.test', CURRENT_DATE, true),
(3, 'validador.sgi', CURRENT_DATE, true)
ON CONFLICT (sgi_id_usuario) DO NOTHING;

-- Asignar rol de administrador al usuario admin
INSERT INTO sgi_t_usuario_rol (sgi_id_usuario_rol, sgi_fk_usuario, sgi_fk_rol, sgi_bl_defecto) VALUES
(1, 1, 1, true)
ON CONFLICT (sgi_id_usuario_rol) DO NOTHING;
