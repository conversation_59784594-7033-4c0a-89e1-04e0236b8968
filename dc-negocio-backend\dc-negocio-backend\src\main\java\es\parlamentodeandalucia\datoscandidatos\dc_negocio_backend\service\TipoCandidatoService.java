package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.TipoCandidatoEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository.TipoCandidatoRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class TipoCandidatoService {

    @Autowired
    private TipoCandidatoRepository tipoCandidatoRepository;

    /**
     * Obtiene todos los tipos de candidato
     */
    public List<TipoCandidatoEntity> findAll() {
        return tipoCandidatoRepository.findAll();
    }

    /**
     * Busca un tipo de candidato por ID
     */
    public Optional<TipoCandidatoEntity> findById(Long id) {
        return tipoCandidatoRepository.findById(id);
    }

    /**
     * Busca un tipo de candidato por valor
     */
    public Optional<TipoCandidatoEntity> findByValor(String valor) {
        return tipoCandidatoRepository.findByValor(valor);
    }

    /**
     * Obtiene el tipo TITULAR
     */
    public Optional<TipoCandidatoEntity> getTitular() {
        return findByValor("TITULAR");
    }

    /**
     * Obtiene el tipo SUPLENTE
     */
    public Optional<TipoCandidatoEntity> getSuplente() {
        return findByValor("SUPLENTE");
    }

    /**
     * Busca tipos que contengan el texto especificado
     */
    public List<TipoCandidatoEntity> findByTextoContaining(String texto) {
        return tipoCandidatoRepository.findByTextoContaining(texto);
    }

    /**
     * Verifica si existe un tipo con el valor especificado
     */
    public boolean existsByValor(String valor) {
        return tipoCandidatoRepository.existsByValor(valor);
    }

    /**
     * Guarda un tipo de candidato
     */
    public TipoCandidatoEntity save(TipoCandidatoEntity tipoCandidato) {
        return tipoCandidatoRepository.save(tipoCandidato);
    }

    /**
     * Elimina un tipo de candidato por ID
     */
    public void deleteById(Long id) {
        tipoCandidatoRepository.deleteById(id);
    }

    /**
     * Valida que el tipo de candidato sea válido
     */
    public boolean isValidTipo(String valor) {
        return "TITULAR".equals(valor) || "SUPLENTE".equals(valor);
    }
}
