package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service;

import es.parlamentodeandalucia.datoscandidatos.dto.Usuario;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.RolEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.UsuarioEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository.RolRepository;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository.UsuarioRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

@Service
@Transactional
public class UsuarioService {
    private static final Logger logger = LoggerFactory.getLogger(UsuarioService.class);

    @Autowired
    private UsuarioRepository usuarioRepository;

    @Autowired
    private RolRepository rolRepository;

    // Buscar todos los usuarios
    public List<UsuarioEntity> buscarTodos() {
        return usuarioRepository.findAll();
    }

    // Buscar por nombre
    public List<UsuarioEntity> buscarPorNombre(String nombre) {
        return usuarioRepository.findByNombreContainingIgnoreCase(nombre);
    }

    // Buscar por email
    public List<UsuarioEntity> buscarPorEmail(String email) {
        return usuarioRepository.findByEmailContainingIgnoreCase(email);
    }

    // Buscar todos los usuarios que tengan un rol específico
    public List<UsuarioEntity> buscarUsuariosPorRol(String nombreRol) {
        return usuarioRepository.findByRolesValorIgnoreCase(nombreRol);
    }

    // Buscar usuarios por rol y estado
    public List<UsuarioEntity> buscarUsuariosPorRolYEstado(String nombreRol, String estado) {
        return usuarioRepository.findByRolesValorIgnoreCaseAndEstadoIgnoreCase(nombreRol, estado);
    }

    // Actualizar estado y activo de un usuario
    public UsuarioEntity actualizarEstado(UsuarioEntity usuario) {
        Optional<UsuarioEntity> existente = usuarioRepository.findByKeycloakId(usuario.getKeycloakId());
        if (existente.isPresent()) {
            UsuarioEntity u = existente.get();
            u.setEstado(usuario.getEstado());
            u.setActivo("activo".equalsIgnoreCase(usuario.getEstado()));
            return usuarioRepository.save(u);
        } else {
            throw new RuntimeException("Usuario no encontrado con ID: " + usuario.getKeycloakId());
        }
    }

    // Modificar todos los datos de un usuario
    public UsuarioEntity actualizarUsuario(UsuarioEntity usuario) {
        return usuarioRepository.save(usuario);
    }

    // Eliminar un usuario por su ID de Keycloak
    public boolean eliminarUsuario(String keycloakId) {
        Optional<UsuarioEntity> usuario = usuarioRepository.findByKeycloakId(keycloakId);
        if (usuario.isPresent()) {
            usuarioRepository.delete(usuario.get());
            return true;
        }
        return false;
    }

    // Crear un nuevo usuario
    public UsuarioEntity crearUsuario(UsuarioEntity usuario) {
        return usuarioRepository.save(usuario);
    }

    // Buscar usuario por ID de Keycloak
    public Optional<UsuarioEntity> buscarPorKeycloakId(String keycloakId) {
        return usuarioRepository.findByKeycloakId(keycloakId);
    }

    // Método requerido por AuthController: sincronizar usuario desde Keycloak
    public UsuarioEntity sincronizarUsuarioKeycloak(Jwt jwt) {
        logger.info("=== SINCRONIZANDO USUARIO DESDE KEYCLOAK ===");
        logger.info("JWT Claims completos: {}", jwt.getClaims());

        String keycloakId = jwt.getSubject();
        String username = jwt.getClaimAsString("username");
        String email = jwt.getClaimAsString("email");
        String nombre = jwt.getClaimAsString("nombre");
        String apellido1 = jwt.getClaimAsString("apellido1");
        String apellido2 = jwt.getClaimAsString("apellido2");

        logger.info("Datos extraídos - username: {}, email: {}, nombre: {}, apellido1: {}, apellido2: {}",
                   username, email, nombre, apellido1, apellido2);

        Optional<UsuarioEntity> opt = usuarioRepository.findByKeycloakId(keycloakId);

        UsuarioEntity usuario;
        if (opt.isPresent()) {
            usuario = opt.get();
            usuario.setUltimoAcceso(LocalDateTime.now());
            // Actualizar datos si han cambiado
            if (username != null) usuario.setUsername(username);
            if (email != null) usuario.setEmail(email);
            if (nombre != null) usuario.setNombre(nombre);
            if (apellido1 != null) usuario.setApellido1(apellido1);
            if (apellido2 != null) usuario.setApellido2(apellido2);
        } else {
            // Crear nuevo usuario con datos del JWT
            usuario = new UsuarioEntity();
            usuario.setKeycloakId(keycloakId);
            usuario.setUsername(username != null ? username : "usuario_" + keycloakId.substring(0, 8));
            usuario.setEmail(email != null ? email : "");
            usuario.setNombre(nombre != null ? nombre : (username != null ? username : "Usuario"));
            usuario.setApellido1(apellido1 != null ? apellido1 : "");
            usuario.setApellido2(apellido2 != null ? apellido2 : "");
            usuario.setEstado("activo");
            usuario.setFechaCreacion(LocalDateTime.now());
            usuario.setUltimoAcceso(LocalDateTime.now());

            // Asignar rol por defecto
            RolEntity rolDefault = rolRepository.findByValorIgnoreCase("USER")
                    .orElse(rolRepository.findByValorIgnoreCase("CANDIDATO")
                            .orElse(null));
            if (rolDefault != null) {
                usuario.setRoles(Set.of(rolDefault));
            }
        }

        logger.info("Usuario final antes de guardar: {}", usuario);
        return usuarioRepository.save(usuario);
    }

    // Método requerido por AuthController: convertir UsuarioEntity a DTO Usuario
    public Usuario convertirADto(UsuarioEntity entity) {
        Usuario dto = new Usuario();
        dto.setId(Math.abs(entity.getKeycloakId().hashCode())); // Generamos ID artificial
        dto.setUsername(entity.getUsername());
        dto.setNombre(entity.getNombre());
        dto.setApellido1(entity.getApellido1());
        dto.setApellido2(entity.getApellido2());
        dto.setNombreCompleto(entity.getNombreCompleto());
        dto.setEmail(entity.getEmail());
        dto.setRol(
                entity.getRoles().stream()
                        .map(RolEntity::getValor)
                        .findFirst() // o `.collect(Collectors.toList())` si quieres todos
                        .orElse("SIN_ROL"));

        dto.setEstado(entity.getEstado() != null && entity.getEstado().equalsIgnoreCase("activo")
                ? Usuario.EstadoEnum.ACTIVO
                : Usuario.EstadoEnum.INACTIVO);
        return dto;
    }



    // Asignar roles a un usuario por su ID de Keycloak
    public UsuarioEntity asignarRoles(String keycloakId, List<Long> idsRoles) {
        UsuarioEntity usuario = usuarioRepository.findByKeycloakId(keycloakId)
                .orElseThrow(() -> new RuntimeException("Usuario no encontrado"));

        Set<RolEntity> nuevosRoles = new HashSet<>(rolRepository.findAllById(idsRoles));
        usuario.setRoles(nuevosRoles); // reemplaza por completo
        return usuarioRepository.save(usuario);
    }

}
