package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.CandidaturaEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.CircunscripcionEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.FormacionPoliticaEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.EstadoCandidaturaEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.TipoCandidaturaEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository.CandidaturaRepository;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository.CircunscripcionRepository;


@Service
@Transactional
public class CandidaturaService {

    @Autowired
    private CandidaturaRepository candidaturaRepository;

    @Autowired
    private CircunscripcionRepository circunscripcionRepository;

    @Autowired
    private CircunscripcionService circunscripcionService;

    @Autowired
    private EstadoCandidaturaService estadoCandidaturaService;

    @Autowired
    private TipoCandidaturaService tipoCandidaturaService;

    // Constructor for dependency injection
    public CandidaturaService(CandidaturaRepository candidaturaRepository,
                             CircunscripcionRepository circunscripcionRepository,
                             CircunscripcionService circunscripcionService,
                             EstadoCandidaturaService estadoCandidaturaService,
                             TipoCandidaturaService tipoCandidaturaService) {
        this.candidaturaRepository = candidaturaRepository;
        this.circunscripcionRepository = circunscripcionRepository;
        this.circunscripcionService = circunscripcionService;
        this.estadoCandidaturaService = estadoCandidaturaService;
        this.tipoCandidaturaService = tipoCandidaturaService;
    }

    // Métodos CRUD básicos
    @Transactional(readOnly = true)
    public List<CandidaturaEntity> findAll() {
        return candidaturaRepository.findAllOrderedByFormacionAndCircunscripcion();
    }

    @Transactional(readOnly = true)
    public Optional<CandidaturaEntity> findById(Long id) {
        return candidaturaRepository.findById(id);
    }

    public Optional<CandidaturaEntity> findByIdWithCandidatos(Long id) {
        return candidaturaRepository.findByIdWithCandidatos(id);
    }

    public Optional<CandidaturaEntity> findByIdWithAllRelations(Long id) {
        return candidaturaRepository.findByIdWithAllRelations(id);
    }

    @Transactional
    public CandidaturaEntity save(CandidaturaEntity candidatura) {
        // Validaciones antes de guardar
        validateCandidatura(candidatura);

        // Si es nueva candidatura, asignar estado y tipo por defecto
        if (candidatura.getId() == null) {
            if (candidatura.getEstadoCandidatura() == null) {
                EstadoCandidaturaEntity estadoManual = estadoCandidaturaService.getManual()
                    .orElseThrow(() -> new RuntimeException("Estado MANUAL no encontrado"));
                candidatura.setEstadoCandidatura(estadoManual);
            }

            if (candidatura.getTipoCandidatura() == null) {
                TipoCandidaturaEntity tipoManual = tipoCandidaturaService.getManual()
                    .orElseThrow(() -> new RuntimeException("Tipo MANUAL no encontrado"));
                candidatura.setTipoCandidatura(tipoManual);
            }

            candidatura.setFechaCreacion(LocalDate.now());
        }

        CandidaturaEntity savedCandidatura = candidaturaRepository.save(candidatura);

        // Update circunscripcion status after saving candidatura
        if (savedCandidatura.getCircunscripcion() != null && savedCandidatura.getCircunscripcion().getId() != null) {
            circunscripcionRepository.findByIdConCandidaturas(savedCandidatura.getCircunscripcion().getId());
        }

        return savedCandidatura;
    }


    @Transactional
    public void deleteById(Long id) {
        Optional<CandidaturaEntity> candidaturaOptional = candidaturaRepository.findById(id);
        if (candidaturaOptional.isPresent()) {
            CandidaturaEntity candidatura = candidaturaOptional.get();
            CircunscripcionEntity associatedCircunscripcion = candidatura.getCircunscripcion();

            // Verificar si se puede eliminar (lógica de negocio)
            if (!canDelete(candidatura)) {
                throw new IllegalStateException("No se puede eliminar la candidatura en su estado actual");
            }

            candidaturaRepository.delete(candidatura);

            if (associatedCircunscripcion != null && associatedCircunscripcion.getId() != null) {
                circunscripcionRepository.findByIdConCandidaturas(associatedCircunscripcion.getId());
            }
        } else {
            throw new RuntimeException("Candidatura no encontrada con ID: " + id);
        }
    }

    // Métodos de búsqueda con filtros
    public Page<CandidaturaEntity> findWithFilters(Long formacionPoliticaId, Long circunscripcionId,
                                                  Long estadoCandidaturaId, Long tipoCandidaturaId,
                                                  Pageable pageable) {
        return candidaturaRepository.findWithFilters(formacionPoliticaId, circunscripcionId,
                                                    estadoCandidaturaId, tipoCandidaturaId, pageable);
    }

    public List<CandidaturaEntity> findByFormacionPolitica(FormacionPoliticaEntity formacionPolitica) {
        return candidaturaRepository.findByFormacionPolitica(formacionPolitica);
    }

    public List<CandidaturaEntity> findByCircunscripcion(CircunscripcionEntity circunscripcion) {
        return candidaturaRepository.findByCircunscripcion(circunscripcion);
    }

    public List<CandidaturaEntity> findByEstadoCandidatura(EstadoCandidaturaEntity estadoCandidatura) {
        return candidaturaRepository.findByEstadoCandidatura(estadoCandidatura);
    }

    public Optional<CandidaturaEntity> findByFormacionPoliticaAndCircunscripcion(Long formacionPoliticaId, Long circunscripcionId) {
        return candidaturaRepository.findByFormacionPoliticaIdAndCircunscripcionId(formacionPoliticaId, circunscripcionId);
    }

    public Long countByEstado(Long estadoId) {
        return candidaturaRepository.countByEstadoCandidaturaId(estadoId);
    }

    @Transactional
    public CandidaturaEntity updateCandidatura(Long id, CandidaturaEntity updatedCandidatura) {
        return candidaturaRepository.findById(id).map(candidatura -> {
            // Guarda la circunscripción anterior para actualizar su estado si cambia
            CircunscripcionEntity oldCircunscripcion = candidatura.getCircunscripcion();

            candidatura.setCircunscripcion(updatedCandidatura.getCircunscripcion());
            candidatura.setFormacionPolitica(updatedCandidatura.getFormacionPolitica());
            candidatura.setOrden(updatedCandidatura.getOrden());

            CandidaturaEntity savedCandidatura = candidaturaRepository.save(candidatura);

            // Si la circunscripción ha cambiado, actualiza el estado de la antigua y nueva circunscripción
            if (savedCandidatura.getCircunscripcion() != null && savedCandidatura.getCircunscripcion().getId() != null) {
                circunscripcionRepository.findByIdConCandidaturas(savedCandidatura.getCircunscripcion().getId());
            }
            // Si la circunscripción antigua es diferente de la nueva, recarga la antigua
            if (oldCircunscripcion != null && oldCircunscripcion.getId() != null &&
                (savedCandidatura.getCircunscripcion() == null || !oldCircunscripcion.getId().equals(savedCandidatura.getCircunscripcion().getId()))) {
                circunscripcionRepository.findByIdConCandidaturas(oldCircunscripcion.getId());
            }

            return savedCandidatura;
        }).orElseThrow(() -> new RuntimeException("Candidatura no encontrada con ID: " + id));
    }

    // Métodos de validación y lógica de negocio
    private void validateCandidatura(CandidaturaEntity candidatura) {
        if (candidatura.getFormacionPolitica() == null) {
            throw new IllegalArgumentException("La formación política es obligatoria");
        }

        if (candidatura.getCircunscripcion() == null) {
            throw new IllegalArgumentException("La circunscripción es obligatoria");
        }

        // Verificar duplicados
        if (candidaturaRepository.existsDuplicate(
                candidatura.getFormacionPolitica().getId(),
                candidatura.getCircunscripcion().getId(),
                candidatura.getId())) {
            throw new IllegalArgumentException("Ya existe una candidatura para esta formación política en esta circunscripción");
        }
    }

    private boolean canDelete(CandidaturaEntity candidatura) {
        // No se puede eliminar si está validada
        if (candidatura.getEstadoCandidatura() != null) {
            String estado = candidatura.getEstadoCandidatura().getValor();
            return !"VALIDADA_POR_JEA".equals(estado) && !"VALIDADA_POR_JEP".equals(estado);
        }
        return true;
    }

    public CandidaturaEntity validarCandidatura(Long id, String usuarioValidacion, String comentario) {
        CandidaturaEntity candidatura = findById(id)
            .orElseThrow(() -> new RuntimeException("Candidatura no encontrada"));

        EstadoCandidaturaEntity estadoValidado = estadoCandidaturaService.getValidada()
            .orElseThrow(() -> new RuntimeException("Estado VALIDADA no encontrado"));

        candidatura.setEstadoCandidatura(estadoValidado);
        candidatura.setUsuarioValidacion(usuarioValidacion);
        candidatura.setFechaValidacion(LocalDate.now());
        candidatura.setComentarioValidacion(comentario);

        return candidaturaRepository.save(candidatura);
    }

    public CandidaturaEntity rechazarCandidatura(Long id, String usuarioValidacion, String observacionRechazo) {
        CandidaturaEntity candidatura = findById(id)
            .orElseThrow(() -> new RuntimeException("Candidatura no encontrada"));

        EstadoCandidaturaEntity estadoRechazado = estadoCandidaturaService.getRechazada()
            .orElseThrow(() -> new RuntimeException("Estado RECHAZADA no encontrado"));

        candidatura.setEstadoCandidatura(estadoRechazado);
        candidatura.setUsuarioValidacion(usuarioValidacion);
        candidatura.setFechaValidacion(LocalDate.now());
        candidatura.setObservacionRechazo(observacionRechazo);

        return candidaturaRepository.save(candidatura);
    }

}
