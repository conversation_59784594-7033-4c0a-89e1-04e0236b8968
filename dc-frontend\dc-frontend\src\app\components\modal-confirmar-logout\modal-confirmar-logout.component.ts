import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-modal-confirmar-logout',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './modal-confirmar-logout.component.html',
  styleUrls: ['./modal-confirmar-logout.component.scss']
})
export class ModalConfirmarLogoutComponent {
  @Input() visible = false;
  @Output() cerrar = new EventEmitter<void>();
  @Output() logoutFrontend = new EventEmitter<void>();
  @Output() logoutKeycloak = new EventEmitter<void>();

  cerrarModal() {
    this.cerrar.emit();
  }

  logoutParcial() {
    this.logoutFrontend.emit();
  }

  logoutTotal() {
    this.logoutKeycloak.emit();
  }
}
