package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity;

import jakarta.persistence.*;

@Entity
@Table(name = "dac_t_plantilla")
public class PlantillaEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "dac_id_plantilla")
    private Long id;

    @Column(name = "dac_tx_nombre")
    private String nombre;

    @Column(name = "dac_bf_contenido")
    private byte[] contenido;

    @Column(name = "dac_bl_activa")
    private Boolean activa;

    @ManyToOne
    @JoinColumn(name = "dac_fk_tipo_documento")
    private TipoDocumentoEntity tipoDocumento;

    // Constructor por defecto
    public PlantillaEntity() {
    }

    // Constructor principal
    public PlantillaEntity(Long id, String nombre, byte[] contenido, Boolean activa, TipoDocumentoEntity tipoDocumento) {
        this.id = id;
        this.nombre = nombre;
        this.contenido = contenido;
        this.activa = activa;
        this.tipoDocumento = tipoDocumento;
    }

    // Getters y Setters

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNombre() {
        return nombre;
    }

    public void setNombre(String nombre) {
        this.nombre = nombre;
    }

    public byte[] getContenido() {
        return contenido;
    }

    public void setContenido(byte[] contenido) {
        this.contenido = contenido;
    }

    public Boolean getActiva() {
        return activa;
    }

    public void setActiva(Boolean activa) {
        this.activa = activa;
    }

    public TipoDocumentoEntity getTipoDocumento() {
        return tipoDocumento;
    }

    public void setTipoDocumento(TipoDocumentoEntity tipoDocumento) {
        this.tipoDocumento = tipoDocumento;
    }
}
