package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.EstadoCandidaturaEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface EstadoCandidaturaRepository extends JpaRepository<EstadoCandidaturaEntity, Long> {

    /**
     * Busca un estado de candidatura por su valor
     * @param valor El valor del estado (ej: "IMPORTADA", "MANUAL", "VALIDADA_POR_JEA", etc.)
     * @return Optional con el estado encontrado
     */
    Optional<EstadoCandidaturaEntity> findByValor(String valor);

    /**
     * Verifica si existe un estado de candidatura con el valor especificado
     * @param valor El valor a verificar
     * @return true si existe, false en caso contrario
     */
    boolean existsByValor(String valor);

    /**
     * Busca estados que contengan el texto especificado en el valor
     * @param texto Texto a buscar
     * @return Lista de estados que coinciden
     */
    @Query("SELECT e FROM EstadoCandidaturaEntity e WHERE " +
           "LOWER(e.valor) LIKE LOWER(CONCAT('%', :texto, '%'))")
    java.util.List<EstadoCandidaturaEntity> findByTextoContaining(@Param("texto") String texto);
}
