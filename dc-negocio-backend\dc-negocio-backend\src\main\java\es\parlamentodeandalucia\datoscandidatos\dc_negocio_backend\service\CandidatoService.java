package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.CandidatoEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.CandidaturaEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.TipoCandidatoEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.EstadoCandidatoEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository.CandidatoRepository;
import jakarta.transaction.Transactional;

@Service
@Transactional
public class CandidatoService {

    @Autowired
    private CandidatoRepository candidatoRepository;

    @Autowired
    private TipoCandidatoService tipoCandidatoService;

    @Autowired
    private EstadoCandidatoService estadoCandidatoService;

    @Autowired
    private ValidacionService validacionService;

    @Autowired
    private AuditoriaService auditoriaService;

    // Métodos CRUD básicos
    public List<CandidatoEntity> findAll() {
        return candidatoRepository.findAll();
    }

    public Page<CandidatoEntity> findAll(Pageable pageable) {
        return candidatoRepository.findAll(pageable);
    }

    public Optional<CandidatoEntity> findById(Long id) {
        return candidatoRepository.findById(id);
    }

    public List<CandidatoEntity> findByCandidaturaAndOrden(Long candidaturaId, Long orden) {
        return candidatoRepository.findByCandidaturaIdAndOrden(candidaturaId, orden);
    }

    public CandidatoEntity save(CandidatoEntity candidato) {
        // Validaciones antes de guardar
        List<String> errores = validacionService.validarCandidato(candidato);
        if (!errores.isEmpty()) {
            String mensajeError = String.join(", ", errores);
            auditoriaService.registrarErrorValidacion("Candidato", "SISTEMA", mensajeError);
            throw new IllegalArgumentException(mensajeError);
        }

        boolean esNuevo = candidato.getId() == null;

        // Si es nuevo candidato, asignar orden automáticamente
        if (esNuevo && candidato.getCandidatura() != null && candidato.getOrden() == null) {
            Long nextOrden = candidatoRepository.findNextOrdenInCandidatura(candidato.getCandidatura().getId());
            candidato.setOrden(nextOrden);
        }

        // Si no tiene estado, asignar estado por defecto
        if (candidato.getEstado() == null) {
            EstadoCandidatoEntity estadoManual = estadoCandidatoService.getManual()
                .orElseThrow(() -> new RuntimeException("Estado MANUAL no encontrado"));
            candidato.setEstado(estadoManual);
        }

        CandidatoEntity saved = candidatoRepository.save(candidato);

        // Auditoría
        if (esNuevo) {
            auditoriaService.registrarCreacion("Candidato", saved.getId(),
                candidato.getUsuarioCreacion() != null ? candidato.getUsuarioCreacion() : "SISTEMA",
                String.format("Nombre: %s %s %s, Candidatura: %d",
                    saved.getNombre(), saved.getApellido1(),
                    saved.getApellido2() != null ? saved.getApellido2() : "",
                    saved.getCandidatura().getId()));
        } else {
            auditoriaService.registrarActualizacion("Candidato", saved.getId(),
                candidato.getUsuarioCreacion() != null ? candidato.getUsuarioCreacion() : "SISTEMA",
                "Datos actualizados");
        }

        return saved;
    }

    public void deleteById(Long id) {
        CandidatoEntity candidato = findById(id)
            .orElseThrow(() -> new RuntimeException("Candidato no encontrado"));

        // Validar si se puede eliminar
        List<String> errores = validacionService.validarEliminacionCandidato(candidato);
        if (!errores.isEmpty()) {
            String mensajeError = String.join(", ", errores);
            auditoriaService.registrarErrorValidacion("Candidato", "SISTEMA", mensajeError);
            throw new IllegalStateException(mensajeError);
        }

        // Auditoría antes de eliminar
        auditoriaService.registrarEliminacion("Candidato", id, "SISTEMA",
            String.format("Candidato: %s %s %s", candidato.getNombre(),
                candidato.getApellido1(), candidato.getApellido2() != null ? candidato.getApellido2() : ""));

        candidatoRepository.deleteById(id);
    }

    // Métodos legacy eliminados según Script 2 (activo y circunscripcion no existen)

    // Nuevos métodos basados en el modelo completo
    public List<CandidatoEntity> findByCandidatura(Long candidaturaId) {
        return candidatoRepository.findByCandidaturaIdOrderByOrden(candidaturaId);
    }

    public List<CandidatoEntity> findByTipo(TipoCandidatoEntity tipo) {
        return candidatoRepository.findByTipo(tipo);
    }

    public List<CandidatoEntity> findByEstado(EstadoCandidatoEntity estado) {
        return candidatoRepository.findByEstado(estado);
    }

    public List<CandidatoEntity> findByTextoContaining(String texto) {
        return candidatoRepository.findByTextoContaining(texto);
    }

    public Page<CandidatoEntity> findWithFilters(Long candidaturaId, Long tipoId, Long estadoId,
                                                String nombre, String apellido1, Pageable pageable) {
        return candidatoRepository.findWithFilters(candidaturaId, tipoId, estadoId, nombre, apellido1, pageable);
    }

    // Métodos de lógica de negocio

    public Long countByCandidaturaAndTipo(Long candidaturaId, Long tipoId) {
        return candidatoRepository.countByCandidaturaIdAndTipoId(candidaturaId, tipoId);
    }

    public CandidatoEntity validarCandidato(Long id, String usuarioValidacion, String comentario) {
        CandidatoEntity candidato = findById(id)
            .orElseThrow(() -> new RuntimeException("Candidato no encontrado"));

        String estadoAnterior = candidato.getEstado() != null ? candidato.getEstado().getValor() : "SIN_ESTADO";

        EstadoCandidatoEntity estadoValidado = estadoCandidatoService.getValidado()
            .orElseThrow(() -> new RuntimeException("Estado VALIDADO no encontrado"));

        candidato.setEstado(estadoValidado);
        candidato.setUsuarioValidacion(usuarioValidacion);
        candidato.setFechaValidacion(LocalDate.now());
        candidato.setComentarioValidacion(comentario);

        CandidatoEntity saved = candidatoRepository.save(candidato);

        // Auditoría
        auditoriaService.registrarValidacion("Candidato", id, usuarioValidacion, "VALIDADO", comentario);
        auditoriaService.registrarCambioEstado("Candidato", id, usuarioValidacion,
            estadoAnterior, "VALIDADO", "Validación manual");

        return saved;
    }

}
