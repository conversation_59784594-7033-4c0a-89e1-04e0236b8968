import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { environment } from "../../environments/environment";

export interface Convocatoria {
  id: number;
  fecha: Date | null;
  fechaElecciones: Date | null;
  fechaInicioPresentacion: Date | null;
  fechaFinPresentacion: Date | null;
  fechaInicioPublicacionBoja: Date | null;
  fechaIrregularidades: Date | null;
  fechaInicioSubsanacion: Date | null;
  fechaFinSubsanacion: Date | null;
  fechaProclamacion: Date | null;
  fechaPublicacionProclamada: Date | null;
  fechaInicioDeclaracion: Date | null;
  fechaFinDeclaracion: Date | null;
  fechaInicioPublicacionInternet: Date | null;
  fechaFinPublicacionInternet: Date | null;
  fechaCancelacion: Date | null;
  logotipo: string;
}

@Injectable({
    providedIn: 'root'
})
export class ConvocatoriaService {
    private readonly apiUrl = environment.apiUrl + '/convocatoria';

    constructor(private http: HttpClient) {}

    getAll(): Observable<Convocatoria[]> {
        return this.http.get<Convocatoria[]>(this.apiUrl);
    }

    getById(id: number): Observable<Convocatoria> {
        return this.http.get<Convocatoria>(`${this.apiUrl}/${id}`);
    }

    create(convocatoria: Convocatoria): Observable<Convocatoria> {
        return this.http.post<Convocatoria>(this.apiUrl, convocatoria);
    }

    update(id: number, convocatoria: Convocatoria): Observable<Convocatoria> {
        return this.http.put<Convocatoria>(`${this.apiUrl}/${id}`, convocatoria);
    }

    delete(id: number): Observable<void> {
        return this.http.delete<void>(`${this.apiUrl}/${id}`);
    }

    obtenerEstadoFases(id: number): Observable<Map<string, boolean>> {
        return this.http.get<Map<string, boolean>>(`${this.apiUrl}/${id}/estado-fases`);
    }

}