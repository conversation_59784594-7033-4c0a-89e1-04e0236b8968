import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { HeaderComponent } from '../header/header.component';
import { FooterComponent } from '../footer/footer.component';

import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { CheckboxModule } from 'primeng/checkbox';
import { DropdownModule } from 'primeng/dropdown';
import { InputTextModule } from 'primeng/inputtext';
import { TagModule } from 'primeng/tag';
import { ModalCambiarRolComponent } from '../modal-cambiar-rol/modal-cambiar-rol.component';
import { CalendarModule } from 'primeng/calendar';
import { HttpClientModule } from '@angular/common/http';
import { MenuModule } from 'primeng/menu';
import { DialogModule } from 'primeng/dialog';
import { MultiSelectModule } from 'primeng/multiselect';
import { InputIconModule } from 'primeng/inputicon';
import { IconFieldModule } from 'primeng/iconfield';
import { SelectModule } from 'primeng/select';
import { SliderModule } from 'primeng/slider';
import { PaginatorModule } from 'primeng/paginator';

import { UsuarioService, Usuario } from '../../services/usuario.service';

interface User {
  id: string;
  name: string;
  status: 'Activo' | 'Inactivo';
  roles: string[];
  rolesIds: number[];
  constituency: string;
  email: string;
  registrationDate: string;
  lastAccess: string;
}

@Component({
  selector: 'app-usuarios',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    HeaderComponent,
    FooterComponent,
    TableModule,
    ButtonModule,
    CheckboxModule,
    DropdownModule,
    InputTextModule,
    TagModule,
    ModalCambiarRolComponent,
    CalendarModule,
    HttpClientModule,
    MenuModule,
    DialogModule,
    MultiSelectModule,
    InputIconModule,
    IconFieldModule,
    SliderModule,
    SelectModule,
    PaginatorModule
  ],
  templateUrl: './usuarios.component.html',
  styleUrls: ['./usuarios.component.scss']
})
export class UsuariosComponent implements OnInit {
  @ViewChild('dt') dt: any;

  users: User[] = [];
  selectedUsers: User[] = [];
  globalFilterValue = '';
  showRoleModal = false;
  selectedUserId: string | null = null;
  currentRoles: number[] = [];
  openedDropdownId: string | null = null;
  showMassiveActions = false;
  
  roles = [
    { label: 'Administrador del sistema', value: 'Administrador del sistema' },
    { label: 'Administrador de JEA', value: 'Administrador de JEA' },
    { label: 'Miembro JEA', value: 'Miembro JEA' },
    { label: 'Representante', value: 'Representante' },
    { label: 'Candidato', value: 'Candidato' }
  ];

  estados = [
    { label: 'Activo', value: 'Activo' },
    { label: 'Inactivo', value: 'Inactivo' },
  ];

  constructor(private usuarioService: UsuarioService) { }
  
  // Carga los usuarios al iniciar el componente
  ngOnInit(): void {
    this.usuarioService.getUsuarios().subscribe((usuarios: Usuario[]) => {
      this.users = usuarios.map(u => ({
        id: u.keycloakId,
        name: u.nombreCompleto || `${u.nombre} ${u.apellido1 ?? ''} ${u.apellido2 ?? ''}`,
        status: u.estado === 'activo' ? 'Activo' : 'Inactivo',
        roles: u.roles.map(r => r.valor), // array de strings
        constituency: '',
        email: u.email,
        registrationDate: this.formatFecha(u.fechaCreacion),
        lastAccess: this.formatFecha(u.ultimoAcceso),
        rolesIds: u.roles.map(r => r.id)
      }));
      //console.log('Usuarios cargados:', this.users); 
    });
  }

  // Formatea una fecha en formato (fecha + hora) para mostrarla en la tabla
  private formatFecha(fecha: string): string {
    if (!fecha) return '';
    const d = new Date(fecha);
    return `${d.toLocaleDateString()} ${d.toLocaleTimeString()}`;
  }

  // Limpia todos los filtros activos en la tabla PrimeNG
  clearFilters(): void {
    this.globalFilterValue = '';
    this.dt.filterGlobal('', 'contains');
    this.dt.reset();
  }

  // Devuelve la clase correspondiente al estado del usuario
  getStatusClass(status: string): string {
    return status === 'Activo' ? 'status-active' : 'status-inactive';
  }

  // Devuelve la clase para cada rol
  getRoleClass(role: string): string {
    switch (role) {
      case 'Administrador': return 'role-admin';
      case 'Candidato': return 'role-candidate';
      case 'Representante': return 'role-representative';
      case 'Junta electoral': return 'role-electoral-board';
      default: return '';
    }
  }

  // Abre el modal para cambiar roles
  openModalChangeRol(type: 'acciones' | 'rol', userId: string | null): void {
    this.selectedUserId = userId;
    const user = userId !== null ? this.users.find(u => u.id === userId) : null;
    this.currentRoles = user?.rolesIds ?? [];
    this.showRoleModal = true;
  }

  // Llama al servicio para actualizar los roles de un usuario
  handleRoleChange(event: { userId: string | null, newRoles: number[] }) {
    if (event.userId === null) {
      this.selectedUsers.forEach(user => {
        this.usuarioService.actualizarRolesUsuario(user.id, event.newRoles).subscribe(() => {
          console.log(`Roles actualizados para ${user.name}`);
        });
      });
    } else {
      const user = this.users.find(u => u.id === event.userId);
      if (user) {
        this.usuarioService.actualizarRolesUsuario(user.id, event.newRoles).subscribe(() => {
          console.log(`Roles actualizados para ${user.name}`);
        });
      }
    }
    this.showRoleModal = false;
  }

  // Activa a un usuario
  activateThisUser(userId: string): void {
    const user = this.users.find(u => u.id === userId);
    if (!user) return;

    user.status = 'Activo';
    this.usuarioService.activarUsuario(user.id).subscribe({
      next: () => console.log(`Usuario ${user.name} activado con éxito.`),
      error: err => console.error('Error al activar usuario:', err)
    });
  }

  // Desactiva a un usuario
  deactivateThisUser(userId: string): void {
    const user = this.users.find(u => u.id === userId);
    if (!user) return;

    user.status = 'Inactivo';
    this.usuarioService.desactivarUsuario(user.id).subscribe({
      next: () => console.log(`Usuario ${user.name} desactivado con éxito.`),
      error: err => console.error('Error al desactivar usuario:', err)
    });
  }

  // Abre o cierra el menu de acciones por fila de usuario
  toggleDropdown(userId: string): void {
    this.openedDropdownId = this.openedDropdownId === userId ? null : userId;
  }

  // Devuelve la clase para la fila
  getRowStyle(user: User, index: number): string {
    const isSelected = this.selectedUsers.some(u => u.id === user.id);
    return isSelected ? 'p-highlight' : (index % 2 === 0 ? 'row-par' : 'row-impar');
  }

  // Activa todos los usuarios seleccionados
  activateAll(): void {
    this.selectedUsers.forEach(user => {
      user.status = 'Activo';
      this.updateUserInDatabase(user);
    });
  }

  // Desactiva todos los usuarios seleccionados
  deactivateAll(): void {
    this.selectedUsers.forEach(user => {
      user.status = 'Inactivo';
      this.updateUserInDatabase(user);
    });
  }

  // Mensaje de confirmación para activar o desactivar usuarios seleccionados
  updateUserInDatabase(user: User): void {
    console.log('Actualizando usuario en base de datos:', user);
  }
}
