package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.EstadoCandidatoEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface EstadoCandidatoRepository extends JpaRepository<EstadoCandidatoEntity, Long> {

    /**
     * Busca un estado de candidato por su valor
     * @param valor El valor del estado (ej: "IMPORTADO", "MANUAL", "VALIDADO")
     * @return Optional con el estado encontrado
     */
    Optional<EstadoCandidatoEntity> findByValor(String valor);

    /**
     * Verifica si existe un estado de candidato con el valor especificado
     * @param valor El valor a verificar
     * @return true si existe, false en caso contrario
     */
    boolean existsByValor(String valor);

    /**
     * Busca estados que contengan el texto especificado en el valor
     * @param texto Texto a buscar
     * @return Lista de estados que coinciden
     */
    @Query("SELECT e FROM EstadoCandidatoEntity e WHERE " +
           "LOWER(e.valor) LIKE LOWER(CONCAT('%', :texto, '%'))")
    java.util.List<EstadoCandidatoEntity> findByTextoContaining(@Param("texto") String texto);
}
