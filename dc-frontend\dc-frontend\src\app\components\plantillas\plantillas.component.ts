import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { HeaderComponent } from '../header/header.component';
import { FooterComponent } from '../footer/footer.component';
import { ModalEditarPlantillasComponent } from '../modal-editar-plantillas/modal-editar-plantillas.component';

import { TableModule } from 'primeng/table';
import { DropdownModule } from 'primeng/dropdown';
import { InputTextModule } from 'primeng/inputtext';
import { CheckboxModule } from 'primeng/checkbox';

interface Plantilla {
  id: number;
  nombre: string;
  tipo: string;
  formato: string;
  estado: 'Activo' | 'Inactivo';
}

@Component({
  selector: 'app-plantillas',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    HeaderComponent,
    FooterComponent,
    TableModule,
    DropdownModule,
    InputTextModule,
    CheckboxModule
  ],
  templateUrl: './plantillas.component.html',
  styleUrls: ['../usuarios/usuarios.component.scss'],
})

export class PlantillasComponent {
  plantillas: Plantilla[] = [
    { id: 1, nombre: 'Plantilla', tipo: 'BOJA', formato: 'DOCX', estado: 'Activo' },
    { id: 2, nombre: 'Plantilla', tipo: 'BOPA', formato: 'DOCX', estado: 'Activo' },
    { id: 3, nombre: 'Plantilla', tipo: 'publicacion-web', formato: 'HTML', estado: 'Activo' },
    { id: 4, nombre: 'Plantilla', tipo: 'declaracion-individual', formato: 'DOCX', estado: 'Activo' },
    { id: 5, nombre: 'Plantilla', tipo: 'BOJA', formato: 'DOCX', estado: 'Inactivo' },
    { id: 6, nombre: 'Plantilla', tipo: 'publicacion-web', formato: 'HTML', estado: 'Inactivo' },
    { id: 7, nombre: 'Plantilla', tipo: 'declaracion-individual', formato: 'DOCX', estado: 'Inactivo' },
    { id: 8, nombre: 'Plantilla', tipo: 'BOJA', formato: 'DOCX', estado: 'Inactivo' },
    { id: 9, nombre: 'Plantilla', tipo: 'publicacion-web', formato: 'HTML', estado: 'Inactivo' },
    { id: 10, nombre: 'Plantilla', tipo: 'BOPA', formato: 'DOCX', estado: 'Inactivo' },
  ];

  tipos = [
    { label: 'BOJA', value: 'BOJA' },
    { label: 'BOPA', value: 'BOPA' },
    { label: 'Publicación web', value: 'publicacion-web' },
    { label: 'Declaración individual', value: 'declaracion-individual' },
  ];

  estados = [
    { label: 'Activo', value: 'Activo' },
    { label: 'Inactivo', value: 'Inactivo' },
  ];

  selectedRows: Plantilla[] = [];
  showMassiveActions = false;
  openedDropdownId: number | null = null;

  toggleDropdown(id: number) {
    this.openedDropdownId = this.openedDropdownId === id ? null : id;
  }

  clearFilters() {
    const table: any = document.querySelector('p-table');
    if (table && table?.filters) {
      table.clear();
    }
  }

  activateTemplates() {
    this.selectedRows.forEach(p => p.estado = 'Activo');
  }

  deactivateTemplates() {
    this.selectedRows.forEach(p => p.estado = 'Inactivo');
  }

  downloadSelected() {
    const nombres = this.selectedRows.map(p => p.nombre).join(', ');
    alert(`Descargando: ${nombres}`);
  }

  rowAction(id: number, action: string) {
    const plantilla = this.plantillas.find(p => p.id === id);
    if (!plantilla) return;

    switch (action) {
      case 'ver':
        alert(`Visualizando plantilla: ${plantilla.nombre}`);
        break;
      case 'editar':
        alert(`Editando plantilla: ${plantilla.nombre}`);
        break;
      case 'descargar':
        alert(`Descargando: ${plantilla.nombre}`);
        break;
      case 'desactivar':
        plantilla.estado = 'Inactivo';
        break;
    }
  }

  addNew() {
    const nueva: Plantilla = {
      id: this.plantillas.length + 1,
      nombre: 'Nueva Plantilla',
      tipo: 'BOJA',
      formato: 'DOCX',
      estado: 'Activo',
    };
    this.plantillas.unshift(nueva);
  }

  getRowStyle(_p: any, index: number) {
    return index % 2 === 0 ? 'row-par' : 'row-impar';
  }

  getStatusClass(estado: string): string {
    return estado === 'Activo' ? 'status-active' : 'status-inactive';
  }

  getTipoClass(tipo: string): string {
    return 'tipo-' + tipo.toLowerCase().replace(/\s+/g, '-');
  }
}
