import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

import { environment } from '../../environments/environment';



export interface PoliticalFormationBackend {
  id?: number; // Optional as it will be generated by the backend for new entities
  nombre: string;
  siglas: string; // Nuevo campo
  codigoInterno: string; // Nuevo campo
}

@Injectable({
  providedIn: 'root'
})
export class FormacionPoliticaService {
  private readonly apiUrl = environment.apiUrl + '/formaciones-politicas';

  constructor(private http: HttpClient) { }

  getAll(): Observable<PoliticalFormationBackend[]> {
    return this.http.get<PoliticalFormationBackend[]>(this.apiUrl);
  }

  getById(id: number): Observable<PoliticalFormationBackend> {
    return this.http.get<PoliticalFormationBackend>(`${this.apiUrl}/${id}`);
  }

  create(formation: PoliticalFormationBackend): Observable<PoliticalFormationBackend> {
    return this.http.post<PoliticalFormationBackend>(this.apiUrl, formation);
  }

  update(id: number, formation: PoliticalFormationBackend): Observable<PoliticalFormationBackend> {
    return this.http.put<PoliticalFormationBackend>(`${this.apiUrl}/${id}`, formation);
  }

  delete(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }
}