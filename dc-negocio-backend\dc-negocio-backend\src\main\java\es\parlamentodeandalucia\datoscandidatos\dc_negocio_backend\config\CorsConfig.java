package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;
import java.util.List;

@Configuration
public class CorsConfig {

    // Inyecta la propiedad 'spring.web.cors.allowed-origins'
    // Spring convierte automáticamente la cadena separada por comas en un array de Strings
    @Value("${spring.web.cors.allowed-origins}")
    private String[] allowedOriginsArray; // Usamos un array para capturar múltiples orígenes

    @Value("${spring.web.cors.allowed-methods}")
    private String[] allowedMethodsArray;

    @Value("${spring.web.cors.allowed-headers}")
    private String[] allowedHeadersArray;

    @Value("${spring.web.cors.allow-credentials}")
    private boolean allowCredentials;


    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();

        // **AQUÍ SE USAN LAS PROPIEDADES INYECTADAS**
        // Convertimos el array a una lista, ya que CorsConfiguration espera una List<String>
        configuration.setAllowedOriginPatterns(Arrays.asList(allowedOriginsArray));
        
        // Métodos HTTP permitidos
        configuration.setAllowedMethods(Arrays.asList(allowedMethodsArray));
        
        // Headers permitidos
        configuration.setAllowedHeaders(Arrays.asList(allowedHeadersArray)); // Cambiado a Arrays.asList
        
        // Permitir credenciales
        configuration.setAllowCredentials(allowCredentials);
        
        // Headers expuestos (pueden ser fijos o inyectados también si lo necesitas)
        configuration.setExposedHeaders(Arrays.asList(
            "Authorization", "Content-Type", "X-Total-Count" // Si esto es fijo, déjalo así. Si no, inyéctalo.
        ));
        
        // Tiempo de cache para preflight requests
        configuration.setMaxAge(3600L); // 1 hora
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        // Aplica esta configuración CORS a todas las rutas de tu API
        source.registerCorsConfiguration("/**", configuration);
        
        return source;
    }
}