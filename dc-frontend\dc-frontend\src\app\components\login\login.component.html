<div class="login-container">
    <div class="login-card">
        <img src="assets/logo-junta.png" alt="Logo Junta" class="logo" />

        <h2 class="title">Sistema de autenticación<br>centralizado</h2>

        <form>
            <label for="username">Usuario</label>
            <input id="username" type="text" [(ngModel)]="username" name="username" />
            <label for="password">Contraseña</label>
            <div class="password-wrapper">
                <input id="password" [type]="showPassword ? 'text' : 'password'" [(ngModel)]="password"
                    name="password" />
                <button type="button" class="toggle-password" (click)="togglePasswordVisibility()">
                    <img [src]="showPassword ? 'assets/eye-off.svg' : 'assets/eye.svg'" alt="Mostrar contraseña"
                        width="20" height="20" />
                </button>
            </div>
            <button class="login-button" type="button">Iniciar sesión</button>
        </form>

        <div class="divider">O accede con</div>

        <div class="auth-external">
            <button class="external-button firma">Firma</button>
            <button class="external-button clave">Clave</button>
        </div>
    </div>
</div>