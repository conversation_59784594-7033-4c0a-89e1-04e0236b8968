package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity;

import jakarta.persistence.*;

@Entity
@Table(name = "dac_t_representante_candidato")
public class RepresentanteCandidatoEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "dac_id_repre_cand")
    private Long id;

    @Column(name = "dac_fk_usuario")
    private String idUsuario;

    @Column(name = "dac_fk_candidato")
    private Long idCandidato;

    @Column(name = "representante_id", nullable = false)
    private Long representanteId;


    // Constructor 
    public RepresentanteCandidatoEntity() {}

    public RepresentanteCandidatoEntity(Long id, String idUsuario, Long idCandidato) {
        this.id = id;
        this.idUsuario = idUsuario;
        this.idCandidato = idCandidato;
    }

    // Getters y Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getIdUsuario() {
        return idUsuario;
    }

    public void setIdUsuario(String idUsuario) {
        this.idUsuario = idUsuario;
    }

    public Long getIdCandidato() {
        return idCandidato;
    }

    public void setIdCandidato(Long idCandidato) {
        this.idCandidato = idCandidato;
    }

    public Object id(int i) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'id'");
    }
}
