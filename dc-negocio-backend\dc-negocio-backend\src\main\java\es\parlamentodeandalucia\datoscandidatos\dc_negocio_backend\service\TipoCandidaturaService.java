package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.TipoCandidaturaEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository.TipoCandidaturaRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class TipoCandidaturaService {

    @Autowired
    private TipoCandidaturaRepository tipoCandidaturaRepository;

    /**
     * Obtiene todos los tipos de candidatura
     */
    public List<TipoCandidaturaEntity> findAll() {
        return tipoCandidaturaRepository.findAll();
    }

    /**
     * Busca un tipo de candidatura por ID
     */
    public Optional<TipoCandidaturaEntity> findById(Long id) {
        return tipoCandidaturaRepository.findById(id);
    }

    /**
     * Busca un tipo de candidatura por valor
     */
    public Optional<TipoCandidaturaEntity> findByValor(String valor) {
        return tipoCandidaturaRepository.findByValor(valor);
    }

    /**
     * Obtiene el tipo IMPORTADA (según Script 2)
     */
    public Optional<TipoCandidaturaEntity> getImportada() {
        return findByValor("IMPORTADA");
    }

    /**
     * Obtiene el tipo MANUAL (según Script 2)
     */
    public Optional<TipoCandidaturaEntity> getManual() {
        return findByValor("MANUAL");
    }



    /**
     * Busca tipos que contengan el texto especificado
     */
    public List<TipoCandidaturaEntity> findByTextoContaining(String texto) {
        return tipoCandidaturaRepository.findByTextoContaining(texto);
    }

    /**
     * Verifica si existe un tipo con el valor especificado
     */
    public boolean existsByValor(String valor) {
        return tipoCandidaturaRepository.existsByValor(valor);
    }

    /**
     * Guarda un tipo de candidatura
     */
    public TipoCandidaturaEntity save(TipoCandidaturaEntity tipoCandidatura) {
        return tipoCandidaturaRepository.save(tipoCandidatura);
    }

    /**
     * Elimina un tipo de candidatura por ID
     */
    public void deleteById(Long id) {
        tipoCandidaturaRepository.deleteById(id);
    }

    /**
     * Valida que el tipo de candidatura sea válido
     */
    public boolean isValidTipo(String valor) {
        return "IMPORTADA".equals(valor) || "MANUAL".equals(valor);
    }
}
