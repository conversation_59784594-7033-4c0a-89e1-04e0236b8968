.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f6fbf7;
}

.main-content {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-bottom: 100px;
}

.inicio-box {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  width: 360px;
  max-width: 90%;
  padding: 36px 28px;
  margin-top: 80px;
  text-align: left;
}

.logo {
  display: block;
  margin: 0 auto 20px auto;
  width: 54px;
  height: auto;
}

h1 {
  font-size: 17px;
  font-weight: 400;
  color: #444;
  margin-bottom: 12px;
}

.description {
  font-size: 13px;
  color: #444;
  line-height: 1.4;
  margin-bottom: 24px;
}

.access-button {
  display: block;
  margin: 0 auto;
  background-color: #d9f021;
  color: #000;
  font-weight: 500;
  border: none;
  padding: 8px 18px;
  font-size: 14px;
  border-radius: 6px;
  cursor: pointer;
}

@media (max-width: 480px) {
  .inicio-box {
    width: auto;
    max-width: 90%;
    padding: 24px 20px;
    margin: 0 16px;
    text-align: left;
  }

  h1 {
    font-size: 18px;
    text-align: left;
  }

  .description {
    font-size: 13px;
    line-height: 1.4;
    text-align: left;
  }

  .access-button {
    margin: 16px auto 0 auto;
    font-size: 14px;
    padding: 8px 16px;
  }

  .logo {
    width: 50px;
    margin: 0 auto 12px auto;
  }
  
  footer {
    flex-direction: column;
    align-items: center;
    text-align: center;
    font-size: 13px;
    gap: 4px;
    padding: 12px 20px;
  }

  .right {
    display: none;
  }

  .movil {
    display: block;
    font-size: 13px;
  }

  .left {
    display: block;
    font-weight: 600;
    margin-bottom: 4px;
  }
}

@media (min-width: 600px) and (max-width: 1024px) {
  .inicio-box {
    width: 460px;
    padding: 40px 32px;
  }

  h1 {
    font-size: 24px;
  }

  .description {
    font-size: 16px;
    line-height: 1.6;
  }

  .access-button {
    font-size: 16px;
    padding: 10px 22px;
  }

  .logo {
    width: 60px;
    margin-bottom: 18px;
  }

  footer {
    font-size: 14px;
  }

  .right a {
    margin-left: 28px;
  }
}

footer {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #666;
  padding: 12px 32px;
  background-color: #f6fbf7;
  box-sizing: border-box;
  border-top: 1px solid #ddd;
  position: absolute;
  bottom: 0;
  left: 0;
}

.left {
  font-weight: 500;
}

.right {
  display: flex;
  align-items: center;
}

.right a {
  margin-left: 24px;
  color: #666;
  text-decoration: none;
}

.right a:hover {
  text-decoration: underline;
}

.movil {
  display: none;
  text-align: center;
  line-height: 1.4;
  font-size: 13px;
}
