.modal-backdrop {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.45);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.modal-container {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
  padding: 30px;
  width: 440px;
  max-width: 90%;
  position: relative;
  text-align: left;
  animation: fadeIn 0.2s ease-in-out;
}

.close-btn {
  position: absolute;
  top: 16px;
  right: 18px;
  background: none;
  border: none;
  font-size: 20px;
  color: #999;
  cursor: pointer;
}
.close-btn:hover {
  color: #111;
}

.modal-title {
  font-size: 18px;
  font-weight: 700;
  color: #111;
  margin-bottom: 12px;
}

.modal-description {
  font-size: 14px;
  color: #444;
  margin-bottom: 24px;
  line-height: 1.5;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  flex-wrap: wrap;
}

.btn {
  padding: 10px 18px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  border: none;
  transition: background-color 0.2s ease;
}

.btn.secondary {
  background-color: #f3f4f6;
  color: #333;
}
.btn.secondary:hover {
  background-color: #e5e7eb;
}

.btn.primary {
  background-color: #C2E038;
  color: #111;
}
.btn.primary:hover {
  background-color: #A8CB5B;
}

.btn.danger {
  background-color: #e11d48;
  color: white;
}
.btn.danger:hover {
  background-color: #c20e38;
}
