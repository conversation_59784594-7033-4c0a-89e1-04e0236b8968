<div class="layout-container">
    <app-header class="header-fixed"></app-header>

    <div class="page-wrapper scrollable-content">
        <div class="breadcrumbs">
            <!-- Icono de casa para el inicio del breadcrumbs -->
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="16px" height="16px"
                style="vertical-align: middle; margin-right: 3px;">
                <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z" />
            </svg>
            <span class="separator">&gt;</span> <!-- Separador después del icono de casa -->
            <span>Administración</span>
            <span class="separator">&gt;</span>
            <strong>Circunscripciones</strong>
        </div>
        <div class="title-section">
            <a routerLink="/home" class="back-button"> <!-- <PERSON><PERSON><PERSON> la clase back-button para estilizar la flecha -->
                <!-- <PERSON>con<PERSON> de flecha hacia atrás (SVG simple para visualización) -->
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="24px"
                    height="24px">
                    <path d="M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z" transform="rotate(180 12 12)" />
                </svg>
            </a>
            <h1 class="page-main-title">Circunscripciones</h1>
        </div>
        <div class="circunscripciones-page-container">

            <!-- Barra de filtros y acciones masivas -->
            <div class="filter-actions-bar">
                <button class="btn-text" (click)="clearFilters()">
                    <!-- Icono de limpiar filtros (SVG simple) -->
                    <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24" width="16px"
                        height="16px">
                        <path
                            d="M6 13c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm14-10v2h-3.21l-3.42 8H18v2h-7.15c-.78 1.76-2.58 3-4.85 3-2.21 0-4-1.79-4-4s1.79-4 4-4c.78 0 1.5.22 2.15.62L12.58 4H20V2h2v2h-2z" />
                    </svg>
                    Limpiar filtros
                </button>
            </div>

            <!-- Tabla de Circunscripciones -->
            <div class="table-container">
                <p-table #dt [value]="circunscripciones" stripedRows [tableStyle]="{'min-width': '50rem'}" [rows]="10"
                    [sortMode]="'multiple'" [paginator]="true"
                    [globalFilterFields]="['codigoProvincia', 'nombre', 'candidaturaActiva']" [rowHover]="true">

                    <ng-template pTemplate="header">
                        <tr>
                            <th style="width: 20px;">
                                <input type="checkbox"
                                    [checked]="selectedRows.length === circunscripciones.length && circunscripciones.length > 0"
                                    [indeterminate]="selectedRows.length > 0 && selectedRows.length < circunscripciones.length"
                                    (change)="toggleSelectAll($event)">
                            </th>

                            <th style="min-width:10rem" pSortableColumn="codigoProvincia">
                                <div class="header-content-group-sortable"> <span>Código</span>
                                    <p-sortIcon field="codigoProvincia" />
                                </div>
                                <p-columnFilter type="text" field="codigoProvincia" display="menu"
                                    class="column-filter-align" />
                            </th>

                            <th style="min-width:15rem" pSortableColumn="nombre">
                                <div class="header-content-group-sortable">
                                    <span>Nombre</span>
                                    <p-sortIcon field="nombre" />
                                </div>
                                <p-columnFilter type="text" field="nombre" display="menu" class="column-filter-align" />
                            </th>

                            <th style="min-width:15rem" pSortableColumn="estado">
                                <div class="header-content-group-sortable">
                                    <span>Estado</span>
                                    <p-sortIcon field="estado" />
                                </div>
                                <p-columnFilter type="boolean" field="estado" display="menu"
                                    class="column-filter-align" />
                            </th>

                            <th style="min-width:15rem" pSortableColumn="candidaturaActiva">
                                <div class="header-content-group-sortable">
                                    <span>Candidaturas Activas</span>
                                    <p-sortIcon field="candidaturaActiva" />
                                </div>
                                <p-columnFilter type="boolean" field="candidaturaActiva" display="menu"
                                    class="column-filter-align" />
                            </th>

                            <th style="width: 70px;" class="align-right-header">
                            </th>
                        </tr>
                    </ng-template>

                    <ng-template pTemplate="body" let-circunscripcion>
                        <tr>
                            <td>
                                <input type="checkbox" [checked]="isRowSelected(circunscripcion.id)"
                                    (change)="toggleSelectRow(circunscripcion.id)">
                            </td>
                            <td>{{ circunscripcion.codigoProvincia }}</td>
                            <td>{{ circunscripcion.nombre }}</td>
                            <td>
                                <span class="status-badge" [ngClass]="getStatusClass(circunscripcion.estado ?? false)">
                                    <svg *ngIf="circunscripcion.estado" width="11" height="12" viewBox="0 0 11 12"
                                        fill="none" xmlns="http://www.w3.org/2000/svg" class="icon-dot">
                                        <g clip-path="url(#clip0_2844_25298)">
                                            <path
                                                d="M5.75 11.25C8.6495 11.25 11 8.8995 11 6C11 3.10051 8.6495 0.75 5.75 0.75C2.85051 0.75 0.5 3.10051 0.5 6C0.5 8.8995 2.85051 11.25 5.75 11.25Z"
                                                fill="currentColor" />
                                        </g>
                                        <defs>
                                            <clipPath id="clip0_2844_25298">
                                                <rect x="0.5" y="0.75" width="10.5" height="10.5" fill="white" />
                                            </clipPath>
                                        </defs>
                                    </svg>
                                    <svg *ngIf="!circunscripcion.estado" width="11" height="12" viewBox="0 0 11 12"
                                        fill="none" xmlns="http://www.w3.org/2000/svg" class="icon-dot">
                                        <g clip-path="url(#clip0_2844_25298)">
                                            <path
                                                d="M5.75 11.25C8.6495 11.25 11 8.8995 11 6C11 3.10051 8.6495 0.75 5.75 0.75C2.85051 0.75 0.5 3.10051 0.5 6C0.5 8.8995 2.85051 11.25 5.75 11.25Z"
                                                fill="currentColor" />
                                        </g>
                                        <defs>
                                            <clipPath id="clip0_2844_25298">
                                                <rect x="0.5" y="0.75" width="10.5" height="10.5" fill="white" />
                                            </clipPath>
                                        </defs>
                                    </svg>
                                    {{ circunscripcion.estado ? 'Activo' : 'Inactivo' }}
                                </span>
                            </td>
                            <td>
                                <span class="status-badge status-with-icon"
                                    [ngClass]="getStatusClass(circunscripcion.candidaturaActiva ?? false)">
                                    <svg *ngIf="circunscripcion.candidaturaActiva" width="12" height="12"
                                        viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg"
                                        class="icon-tick">
                                        <g clip-path="url(#clip0_2844_25271)">
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M11.1182 2.3818C11.2939 2.55754 11.2939 2.84246 11.1182 3.0182L4.5182 9.6182C4.34246 9.79393 4.05754 9.79393 3.8818 9.6182L0.881802 6.6182C0.706066 6.44246 0.706066 6.15754 0.881802 5.9818C1.05754 5.80607 1.34246 5.80607 1.5182 5.9818L4.2 8.6636L10.4818 2.3818C10.6575 2.20607 10.9425 2.20607 11.1182 2.3818Z"
                                                fill="currentColor" />
                                        </g>
                                        <defs>
                                            <clipPath id="clip0_2844_25271">
                                                <rect x="0.75" y="0.75" width="10.5" height="10.5" fill="white" />
                                            </clipPath>
                                        </defs>
                                    </svg>
                                    <svg *ngIf="!circunscripcion.candidaturaActiva" xmlns="http://www.w3.org/2000/svg"
                                        viewBox="0 0 24 24" fill="currentColor" width="18px" height="18px"
                                        class="icon-cross">
                                        <path
                                            d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" />
                                    </svg>
                                    {{ circunscripcion.candidaturaActiva ? 'Sí' : 'No' }}
                                </span>
                            </td>
                        </tr>
                    </ng-template>

                </p-table>
            </div>

        </div>
    </div>
    <app-footer class="footer-fixed"></app-footer>
</div>