{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-inputicon.mjs"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, inject, HostBinding, Input, ChangeDetectionStrategy, ViewEncapsulation, Component, NgModule } from '@angular/core';\nimport { SharedModule } from 'primeng/api';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { BaseStyle } from 'primeng/base';\nconst _c0 = [\"*\"];\nconst classes = {\n  root: 'p-inputicon'\n};\nclass InputIconStyle extends BaseStyle {\n  name = 'inputicon';\n  classes = classes;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵInputIconStyle_BaseFactory;\n    return function InputIconStyle_Factory(__ngFactoryType__) {\n      return (ɵInputIconStyle_BaseFactory || (ɵInputIconStyle_BaseFactory = i0.ɵɵgetInheritedFactory(InputIconStyle)))(__ngFactoryType__ || InputIconStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: InputIconStyle,\n    factory: InputIconStyle.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputIconStyle, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * InputIcon displays an icon.\n * @group Components\n */\nclass InputIcon extends BaseComponent {\n  /**\n   * Style class of the element.\n   * @group Props\n   */\n  styleClass;\n  get hostClasses() {\n    return this.styleClass;\n  }\n  _componentStyle = inject(InputIconStyle);\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵInputIcon_BaseFactory;\n    return function InputIcon_Factory(__ngFactoryType__) {\n      return (ɵInputIcon_BaseFactory || (ɵInputIcon_BaseFactory = i0.ɵɵgetInheritedFactory(InputIcon)))(__ngFactoryType__ || InputIcon);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: InputIcon,\n    selectors: [[\"p-inputicon\"], [\"p-inputIcon\"]],\n    hostVars: 4,\n    hostBindings: function InputIcon_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.hostClasses);\n        i0.ɵɵclassProp(\"p-inputicon\", true);\n      }\n    },\n    inputs: {\n      styleClass: \"styleClass\"\n    },\n    features: [i0.ɵɵProvidersFeature([InputIconStyle]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function InputIcon_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    dependencies: [CommonModule, SharedModule],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputIcon, [{\n    type: Component,\n    args: [{\n      selector: 'p-inputicon, p-inputIcon',\n      standalone: true,\n      imports: [CommonModule, SharedModule],\n      template: `<ng-content></ng-content>`,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [InputIconStyle],\n      host: {\n        '[class]': 'styleClass',\n        '[class.p-inputicon]': 'true'\n      }\n    }]\n  }], null, {\n    styleClass: [{\n      type: Input\n    }],\n    hostClasses: [{\n      type: HostBinding,\n      args: ['class']\n    }]\n  });\n})();\nclass InputIconModule {\n  static ɵfac = function InputIconModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || InputIconModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: InputIconModule,\n    imports: [InputIcon, SharedModule],\n    exports: [InputIcon, SharedModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [InputIcon, SharedModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputIconModule, [{\n    type: NgModule,\n    args: [{\n      imports: [InputIcon, SharedModule],\n      exports: [InputIcon, SharedModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { InputIcon, InputIconModule, InputIconStyle };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,UAAU;AAAA,EACd,MAAM;AACR;AACA,IAAM,iBAAN,MAAM,wBAAuB,UAAU;AAAA,EACrC,OAAO;AAAA,EACP,UAAU;AAAA,EACV,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,uBAAuB,mBAAmB;AACxD,cAAQ,gCAAgC,8BAAiC,sBAAsB,eAAc,IAAI,qBAAqB,eAAc;AAAA,IACtJ;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,gBAAe;AAAA,EAC1B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,YAAN,MAAM,mBAAkB,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpC;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,kBAAkB,OAAO,cAAc;AAAA,EACvC,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,kBAAkB,mBAAmB;AACnD,cAAQ,2BAA2B,yBAA4B,sBAAsB,UAAS,IAAI,qBAAqB,UAAS;AAAA,IAClI;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,aAAa,GAAG,CAAC,aAAa,CAAC;AAAA,IAC5C,UAAU;AAAA,IACV,cAAc,SAAS,uBAAuB,IAAI,KAAK;AACrD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,IAAI,WAAW;AAC7B,QAAG,YAAY,eAAe,IAAI;AAAA,MACpC;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,YAAY;AAAA,IACd;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC,cAAc,CAAC,GAAM,0BAA0B;AAAA,IACjF,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,mBAAmB,IAAI,KAAK;AAC7C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,aAAa,CAAC;AAAA,MACnB;AAAA,IACF;AAAA,IACA,cAAc,CAAC,cAAc,YAAY;AAAA,IACzC,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,cAAc,YAAY;AAAA,MACpC,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,WAAW,CAAC,cAAc;AAAA,MAC1B,MAAM;AAAA,QACJ,WAAW;AAAA,QACX,uBAAuB;AAAA,MACzB;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,WAAW,YAAY;AAAA,IACjC,SAAS,CAAC,WAAW,YAAY;AAAA,EACnC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,WAAW,cAAc,YAAY;AAAA,EACjD,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,WAAW,YAAY;AAAA,MACjC,SAAS,CAAC,WAAW,YAAY;AAAA,IACnC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}