package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity;

import java.sql.Date;
import java.util.ArrayList;
import java.util.List;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import net.minidev.json.annotate.JsonIgnore;

@Entity
@Table(name = "dac_t_convocatoria")
public class ConvocatoriaEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "dac_id_convocatoria")
    private Long id;

    @Column(name = "dac_fh_convocatoria")
    private Date fecha;

    @Column(name = "dac_fh_elecciones")
    private Date fechaElecciones; 

    @Column(name = "dac_fh_inicio_presentacion")
    private Date fechaInicioPresentacion;

    @Column(name = "dac_fh_fin_presentacion")
    private Date fechaFinPresentacion;
 
    @Column(name = "dac_fh_inicio_publicacion_boja")
    private Date fechaInicioPublicacionBoja;

    @Column(name = "dac_fh_irregularidades")   
    private Date fechaIrregularidades;

    @Column(name = "dac_fh_inicio_subsanacion")
    private Date fechaInicioSubsanacion;

    @Column(name = "dac_fh_fin_subsanacion")
    private Date fechaFinSubsanacion;

    @Column(name = "dac_fh_proclamacion")
    private Date fechaProclamacion;

    @Column(name = "dac_fh_publicacion_proclamada")
    private Date fechaPublicacionProclamada;

    @Column(name = "dac_fh_inicio_declaracion")
    private Date fechaInicioDeclaracion;

    @Column(name = "dac_fh_fin_declaracion")
    private Date fechaFinDeclaracion;

    @Column(name = "dac_fh_inicio_publicacion_internet")
    private Date fechaInicioPublicacionInternet;

    @Column(name = "dac_fh_fin_publicacion_internet")
    private Date fechaFinPublicacionInternet;

    @Column(name = "dac_fh_cancelacion")
    private Date fechaCancelacion;

    @Column(name = "dac_bl_logotipo")
    private byte[] logotipo;

    @OneToMany(mappedBy = "convocatoria", cascade = CascadeType.ALL, orphanRemoval = true)
    @JsonIgnore
    private List<AuditaConvocatoriaEntity> auditorias = new ArrayList<>();

    // Constructor por defecto
    public ConvocatoriaEntity() {
    }

    // Constructor con parámetros principales
    public ConvocatoriaEntity(Long id, Date fecha, Date fechaInicioPresentacion, Date fechaFinPresentacion,
            Date fechaInicioPublicacionBoja, Date fechaIrregularidades, Date fechaInicioSubsanacion,
            Date fechaFinSubsanacion, Date fechaProclamacion, Date fechaPublicacionProclamada,
            Date fechaInicioDeclaracion, Date fechaFinDeclaracion, Date fechaInicioPublicacionInternet,
            Date fechaFinPublicacionInternet, Date fechaCancelacion, byte[] logotipo) {
        this.id = id;
        this.fecha = fecha;
        this.fechaInicioPresentacion = fechaInicioPresentacion;
        this.fechaFinPresentacion = fechaFinPresentacion;
        this.fechaInicioPublicacionBoja = fechaInicioPublicacionBoja;
        this.fechaIrregularidades = fechaIrregularidades;
        this.fechaInicioSubsanacion = fechaInicioSubsanacion;
        this.fechaFinSubsanacion = fechaFinSubsanacion;
        this.fechaProclamacion = fechaProclamacion;
        this.fechaPublicacionProclamada = fechaPublicacionProclamada;
        this.fechaInicioDeclaracion = fechaInicioDeclaracion;
        this.fechaFinDeclaracion = fechaFinDeclaracion;
        this.fechaInicioPublicacionInternet = fechaInicioPublicacionInternet;
        this.fechaFinPublicacionInternet = fechaFinPublicacionInternet;
        this.fechaCancelacion = fechaCancelacion;
        this.logotipo = logotipo;
    }

    // Getters y Setters

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getFecha() {
        return fecha;
    }

    public void setFecha(Date fecha) {
        this.fecha = fecha;
    }

    public Date getFechaInicioPresentacion() {
        return fechaInicioPresentacion;
    }

    public void setFechaInicioPresentacion(Date fechaInicioPresentacion) {
        this.fechaInicioPresentacion = fechaInicioPresentacion;
    }

    public Date getFechaFinPresentacion() {
        return fechaFinPresentacion;
    }

    public void setFechaFinPresentacion(Date fechaFinPresentacion) {
        this.fechaFinPresentacion = fechaFinPresentacion;
    }

    

    public Date getFechaElecciones() {
        return fechaElecciones;
    }

    public void setFechaElecciones(Date fechaElecciones) {
        this.fechaElecciones = fechaElecciones;
    }

    public Date getFechaInicioPublicacionBoja() {
        return fechaInicioPublicacionBoja;
    }

    public void setFechaInicioPublicacionBoja(Date fechaInicioPublicacionBoja) {
        this.fechaInicioPublicacionBoja = fechaInicioPublicacionBoja;
    }

    public Date getFechaIrregularidades() {
        return fechaIrregularidades;
    }

    public void setFechaIrregularidades(Date fechaIrregularidades) {
        this.fechaIrregularidades = fechaIrregularidades;
    }

    public Date getFechaInicioSubsanacion() {
        return fechaInicioSubsanacion;
    }

    public void setFechaInicioSubsanacion(Date fechaInicioSubsanacion) {
        this.fechaInicioSubsanacion = fechaInicioSubsanacion;
    }

    public Date getFechaFinSubsanacion() {
        return fechaFinSubsanacion;
    }

    public void setFechaFinSubsanacion(Date fechaFinSubsanacion) {
        this.fechaFinSubsanacion = fechaFinSubsanacion;
    }

    public Date getFechaProclamacion() {
        return fechaProclamacion;
    }

    public void setFechaProclamacion(Date fechaProclamacion) {
        this.fechaProclamacion = fechaProclamacion;
    }

    public Date getFechaPublicacionProclamada() {
        return fechaPublicacionProclamada;
    }

    public void setFechaPublicacionProclamada(Date fechaPublicacionProclamada) {
        this.fechaPublicacionProclamada = fechaPublicacionProclamada;
    }

    public Date getFechaInicioDeclaracion() {
        return fechaInicioDeclaracion;
    }

    public void setFechaInicioDeclaracion(Date fechaInicioDeclaracion) {
        this.fechaInicioDeclaracion = fechaInicioDeclaracion;
    }

    public Date getFechaFinDeclaracion() {
        return fechaFinDeclaracion;
    }

    public void setFechaFinDeclaracion(Date fechaFinDeclaracion) {
        this.fechaFinDeclaracion = fechaFinDeclaracion;
    }

    public Date getFechaInicioPublicacionInternet() {
        return fechaInicioPublicacionInternet;
    }

    public void setFechaInicioPublicacionInternet(Date fechaInicioPublicacionInternet) {
        this.fechaInicioPublicacionInternet = fechaInicioPublicacionInternet;
    }

    public Date getFechaFinPublicacionInternet() {
        return fechaFinPublicacionInternet;
    }

    public void setFechaFinPublicacionInternet(Date fechaFinPublicacionInternet) {
        this.fechaFinPublicacionInternet = fechaFinPublicacionInternet;
    }

    public Date getFechaCancelacion() {
        return fechaCancelacion;
    }

    public void setFechaCancelacion(Date fechaCancelacion) {
        this.fechaCancelacion = fechaCancelacion;
    }

    public byte[] getLogotipo() {
        return logotipo;
    }

    public void setLogotipo(byte[] logotipo) {
        this.logotipo = logotipo;
    }

}
