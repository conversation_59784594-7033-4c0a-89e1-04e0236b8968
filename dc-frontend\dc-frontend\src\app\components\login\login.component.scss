.login-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #f7fcf7;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    box-sizing: border-box;
}

.login-card {
    background: white;
    padding: 40px 32px;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    width: 340px;
    text-align: center;
}

.logo {
    width: 60px;
    height: auto;
    margin-bottom: 8px;
}

.title {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 28px;
    color: #333;
}

form {
    display: flex;
    flex-direction: column;
    align-items: stretch;
}

label {
    text-align: left;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 6px;
    margin-top: 12px;
    color: #222;
}

input {
    height: 38px;
    padding: 0 10px;
    font-size: 14px;
    border: 1px solid #ccc;
    border-radius: 6px;
    outline: none;
    width: 100%;
    box-sizing: border-box;
}

.password-wrapper {
    position: relative;

    input {
        width: 100%;
        padding-right: 38px;
    }

    .toggle-password {
        position: absolute;
        top: 8px;
        right: 8px;
        border: none;
        background: transparent;
        font-size: 18px;
        cursor: pointer;

        img {
            width: 20px;
            height: 20px;
        }
    }
}

.login-button {
    margin-top: 20px;
    background-color: #cce62f;
    border: none;
    padding: 10px;
    font-size: 16px;
    font-weight: bold;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.3s;

    &:hover {
        background-color: #b7d42b;
    }
}

.divider {
    margin: 24px 0 12px;
    font-size: 13px;
    color: #555;
}

.auth-external {
    display: flex;
    flex-direction: column;
    gap: 10px;

    .external-button {
        padding: 10px;
        font-size: 15px;
        border-radius: 6px;
        border: 1px solid #ccc;
        background-color: white;
        cursor: pointer;
        font-weight: bold;

        &.firma {
            color: #d80027;
        }

        &.clave {
            color: #f28c00;
            border-color: #f28c00;
        }

        &:hover {
            background-color: #f5f5f5;
        }
    }
}

@media (max-width: 1024px) {
    .login-card {
        width: 320px;
        padding: 32px 24px;
    }

    .title {
        font-size: 16px;
    }

    .login-button,
    .external-button {
        font-size: 14px;
        padding: 9px;
    }

    input {
        font-size: 13px;
        height: 36px;
    }

    .toggle-password img {
        width: 18px;
        height: 18px;
    }
}

@media (max-width: 480px) {
    .login-card {
        width: 100%;
        max-width: 300px;
        padding: 28px 20px;
    }

    .title {
        font-size: 15px;
    }

    input {
        height: 34px;
        font-size: 13px;
    }

    .login-button {
        font-size: 14px;
        padding: 9px;
    }

    .external-button {
        font-size: 14px;
        padding: 9px;
    }

    .toggle-password img {
        width: 17px;
        height: 17px;
    }
}
