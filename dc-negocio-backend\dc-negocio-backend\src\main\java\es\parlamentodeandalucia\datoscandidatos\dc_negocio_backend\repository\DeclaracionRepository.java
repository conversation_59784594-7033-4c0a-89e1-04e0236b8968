package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.DeclaracionEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface DeclaracionRepository extends JpaRepository<DeclaracionEntity, Long> {
    
    // Métodos básicos heredados de JpaRepository
    // findAll(), findById(), save(), delete(), etc.
}
