<div class="layout-container">
  <app-header class="header-fixed"></app-header>
  <p-toast></p-toast>

  <div class="page-wrapper scrollable-content">
    <div class="breadcrumbs">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="16" height="16" style="margin-right:3px"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/></svg>
      <span class="separator">&gt;</span>
      <span>Administración</span>
      <span class="separator">&gt;</span>
      <strong>Candidaturas</strong>
    </div>

    <div class="title-section">
      <div class="title-with-back">
        <button class="btn-back" (click)="goBack()">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
            <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
          </svg>
        </button>
        <h1>Gestión de Candidaturas</h1>
      </div>
      <div class="actions">
        <button pButton type="button" label="Añadir nuevo" icon="pi pi-plus" (click)="abrirModalCrear()"></button>
      </div>
    </div>

    <div class="usuarios-page-container">
      <div class="filter-actions-bar">
        <button class="btn-text" (click)="limpiarFiltros()">
          <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24" width="16px" height="16px">
            <path d="M6 13c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm14-10v2h-3.21l-3.42 8H18v2h-7.15c-.78 1.76-2.58 3-4.85 3-2.21 0-4-1.79-4-4s1.79-4 4-4c.78 0 1.5.22 2.15.62L12.58 4H20V2h2v2h-2z"/>
          </svg>
          Limpiar filtros
        </button>
        <div class="dropdown-masivo">
          <p-menu #massMenu [model]="massItems" [popup]="true" [appendTo]="'body'" [baseZIndex]="11000" (onShow)="isMassMenuOpen=true" (onHide)="isMassMenuOpen=false"></p-menu>
          <button class="btn-massive" [disabled]="selected.length===0"
                  [ngClass]="{'is-disabled': selected.length===0, 'is-open': isMassMenuOpen}"
                  (click)="massMenu.toggle($event)">
            Acciones masivas
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
              <path d="M7 10l5 5 5-5H7z"/>
            </svg>
          </button>
        </div>
      </div>

      <p-table #dt [value]="candidaturas" [loading]="loading" [paginator]="true" [rows]="10" [rowsPerPageOptions]="[5,10,20]" [rowHover]="true" [(selection)]="selected" dataKey="id"
        paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown">
        <ng-template pTemplate="header">
          <tr>
            <th style="width:3rem"><p-tableHeaderCheckbox></p-tableHeaderCheckbox></th>
            <th pSortableColumn="formacionPolitica.nombre">Formación política <p-sortIcon field="formacionPolitica.nombre"></p-sortIcon>
              <p-columnFilter field="formacionPolitica.id" matchMode="equals" display="menu" [showMatchModes]="false" [showOperator]="false" [showAddButton]="false">
                <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                  <p-select [options]="formacionFilterOptions" [ngModel]="value" (onChange)="filter($event.value)" optionLabel="label" optionValue="value" placeholder="Seleccionar" class="w-full"></p-select>
                </ng-template>
              </p-columnFilter>
            </th>
            <th pSortableColumn="circunscripcion.nombre">Circunscripción <p-sortIcon field="circunscripcion.nombre"></p-sortIcon>
              <p-columnFilter field="circunscripcion.id" matchMode="equals" display="menu" [showMatchModes]="false" [showOperator]="false" [showAddButton]="false">
                <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                  <p-select [options]="circunscripcionFilterOptions" [ngModel]="value" (onChange)="filter($event.value)" optionLabel="label" optionValue="value" placeholder="Seleccionar" class="w-full"></p-select>
                </ng-template>
              </p-columnFilter>
            </th>
            <th pSortableColumn="orden">Orden <p-sortIcon field="orden"></p-sortIcon>
              <p-columnFilter field="orden" matchMode="equals" display="menu" type="numeric"></p-columnFilter>
            </th>
            <th>Tipo
              <p-columnFilter field="tipoCandidatura.valor" matchMode="equals" display="menu" [showMatchModes]="false" [showOperator]="false" [showAddButton]="false">
                <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                  <p-select [options]="tipoOptions" [ngModel]="value" (onChange)="filter($event.value)" optionLabel="label" optionValue="value" placeholder="Seleccionar" class="w-full"></p-select>
                </ng-template>
              </p-columnFilter>
            </th>
            <th>Estado
              <p-columnFilter field="estadoCandidatura.valor" matchMode="equals" display="menu" [showMatchModes]="false" [showOperator]="false" [showAddButton]="false">
                <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                  <p-select [options]="estadoOptions" [ngModel]="value" (onChange)="filter($event.value)" optionLabel="label" optionValue="value" placeholder="Seleccionar" class="w-full"></p-select>
                </ng-template>
              </p-columnFilter>
            </th>
            <th style="width:140px">Acciones</th>
          </tr>
        </ng-template>

        <ng-template pTemplate="body" let-row>
          <tr>
            <td><p-tableCheckbox [value]="row"></p-tableCheckbox></td>
            <td>{{ row.formacionPolitica?.nombre || row.formacionPolitica?.siglas || row.formacionPolitica?.id }}</td>
            <td>{{ row.circunscripcion?.nombre || row.circunscripcion?.id }}</td>
            <td>{{ row.orden }}</td>
            <td><p-tag [severity]="getTipoSeverity(row.tipoCandidatura)" [value]="row.tipoCandidatura?.valor || row.tipoCandidatura || 'Manual'"></p-tag></td>
            <td><p-tag [severity]="getEstadoSeverity(row.estadoCandidatura)" [value]="row.estadoCandidatura?.valor || row.estadoCandidatura || 'Proclamada'"></p-tag></td>
            <td class="acciones-cell">
              <p-menu #rowMenu [model]="rowItems" [popup]="true" [appendTo]="'body'" [baseZIndex]="11000" (onShow)="openRowId=row.id" (onHide)="openRowId=null"></p-menu>
              <button class="btn-text" [ngClass]="{'is-open': openRowId===row.id}" (click)="setCurrentRow(row, rowMenu, $event)">Acciones
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
              </button>
            </td>
          </tr>
        </ng-template>

        <ng-template pTemplate="emptymessage">
          <tr><td colspan="7" class="p-text-center">No hay candidaturas.</td></tr>
        </ng-template>
      </p-table>
    </div>

    <!-- Modal Crear/Editar -->
    <p-dialog [(visible)]="showSidebar" [modal]="true" [draggable]="false" [style]="{width:'520px'}" [breakpoints]="{'960px':'95vw'}" [appendTo]="'body'" [autoZIndex]="true" [baseZIndex]="11000">
      <ng-template pTemplate="header">{{ isEditing ? 'Editar candidatura' : 'Añadir candidatura' }}</ng-template>
      <ng-template pTemplate="content">
        <div class="field"><label>Formación política</label><p-select [options]="formacionOptions" optionLabel="nombre" optionValue="id" [(ngModel)]="nuevaCandidatura.formacionPolitica.id" placeholder="Seleccionar" [appendTo]="'body'" [autoZIndex]="true" [baseZIndex]="12000"></p-select></div>
        <div class="field"><label>Circunscripción</label><p-select [options]="circunscripcionOptions" optionLabel="nombre" optionValue="id" [(ngModel)]="nuevaCandidatura.circunscripcion.id" placeholder="Seleccionar" [appendTo]="'body'" [autoZIndex]="true" [baseZIndex]="12000"></p-select></div>
        <div class="field"><label>Orden</label><p-inputNumber [(ngModel)]="nuevaCandidatura.orden" [min]="1"></p-inputNumber></div>
      </ng-template>
      <ng-template pTemplate="footer">
        <div class="candidatura-footer">
          <button pButton type="button" label="Cancelar" class="p-button-text" (click)="showSidebar=false"></button>
          <button pButton type="button" label="Guardar" (click)="guardarCandidatura()"></button>
        </div>
      </ng-template>
    </p-dialog>

    <!-- Diálogo Exportar -->
    <p-dialog [(visible)]="showExport" [modal]="true" [draggable]="false" [style]="{width:'540px'}" [breakpoints]="{'960px':'95vw'}">
      <ng-template pTemplate="header">Exportar selección</ng-template>
      <ng-template pTemplate="content">
        <div class="field"><label>Formato del archivo</label><p-select [options]="formatos" optionLabel="label" optionValue="value" [(ngModel)]="exportForm.formato" placeholder="Seleccionar"></p-select></div>
        <div class="field"><label>Calidad</label><p-select [options]="calidades" optionLabel="label" optionValue="value" [(ngModel)]="exportForm.calidad" placeholder="Seleccionar"></p-select></div>
        <div class="field"><label>Label</label><input pInputText [(ngModel)]="exportForm.label1" /></div>
        <div class="field"><label>Label</label><input pInputText [(ngModel)]="exportForm.label2" /></div>
      </ng-template>
      <ng-template pTemplate="footer">
        <button pButton type="button" label="Cancelar" class="p-button-text" (click)="showExport=false"></button>
        <button pButton type="button" label="Exportar" (click)="exportar()"></button>
      </ng-template>
    </p-dialog>

    <!-- Diálogo de confirmación -->
    <p-confirmDialog></p-confirmDialog>

    <!-- Toast para mensajes -->
    <p-toast></p-toast>

    <app-footer class="footer-fixed"></app-footer>
  </div>
</div>
