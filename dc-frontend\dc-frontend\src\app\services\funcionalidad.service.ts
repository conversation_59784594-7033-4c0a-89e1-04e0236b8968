import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

import { environment } from '../../environments/environment';

export interface Funcionalidad {
  id: number;
  nombre: string;
  activo: boolean;
}

@Injectable({
  providedIn: 'root'
})

export class FuncionalidadService {

  private readonly apiUrl = environment.apiUrl + '/funcionalidades';

  constructor(private http: HttpClient) {}

  getAll(): Observable<Funcionalidad[]> {
    return this.http.get<Funcionalidad[]>(`${this.apiUrl}`);
  }

  getById(id: number): Observable<Funcionalidad> {
    return this.http.get<Funcionalidad>(`${this.apiUrl}/${id}`);
  }

  create(funcionalidad: Funcionalidad): Observable<Funcionalidad> {
    return this.http.post<Funcionalidad>(this.apiUrl, funcionalidad);
  }

  update(id: number, funcionalidad: Funcionalidad): Observable<Funcionalidad> {
    return this.http.put<Funcionalidad>(`${this.apiUrl}/${id}`, funcionalidad);
  }

  delete(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }

  toggleActivo(id: number, activo: boolean): Observable<any> {
    const url = `${this.apiUrl}/${id}/activo?activo=${activo}`;
    console.log(url);
    return this.http.put<any>(url, null);
  }

}
