package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity;

import jakarta.persistence.*;
import java.time.LocalDate;
import java.util.List;

@Entity
@Table(name = "dac_t_importacion")
public class ImportacionEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "dac_id_importacion")
    private Long id;

    @Column(name = "dac_tx_usuario_importacion")
    private String usuarioImportacion;

    @Column(name = "dac_fh_importacion")
    private LocalDate fechaImportacion;

    @Column(name = "dac_fk_nombre_fichero")
    private String nombreFichero;

    @Column(name = "dac_tx_formato_fichero")
    private String formatoFichero;

    @Column(name = "dac_bf_contenido", columnDefinition = "bytea")
    private byte[] contenido;

    @Column(name = "dac_in_filas_correctas")
    private Long filasCorrectas;

    @Column(name = "dac_in_filas_incorrectas")
    private Long filasIncorrectas;

    @Column(name = "dac_bf_resultado", columnDefinition = "bytea")
    private byte[] resultado;

    @OneToMany(mappedBy = "importacion", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<ErrorImportEntity> errores;

    // Constructor por defecto
    public ImportacionEntity() {
    }

    // Constructor con parámetros principales
    public ImportacionEntity(String usuarioImportacion, LocalDate fechaImportacion,
                           String nombreFichero, String formatoFichero, byte[] contenido) {
        this.usuarioImportacion = usuarioImportacion;
        this.fechaImportacion = fechaImportacion;
        this.nombreFichero = nombreFichero;
        this.formatoFichero = formatoFichero;
        this.contenido = contenido;
        this.filasCorrectas = 0L;
        this.filasIncorrectas = 0L;
    }

    // Getters y Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUsuarioImportacion() {
        return usuarioImportacion;
    }

    public void setUsuarioImportacion(String usuarioImportacion) {
        this.usuarioImportacion = usuarioImportacion;
    }

    public LocalDate getFechaImportacion() {
        return fechaImportacion;
    }

    public void setFechaImportacion(LocalDate fechaImportacion) {
        this.fechaImportacion = fechaImportacion;
    }

    public String getNombreFichero() {
        return nombreFichero;
    }

    public void setNombreFichero(String nombreFichero) {
        this.nombreFichero = nombreFichero;
    }

    public String getFormatoFichero() {
        return formatoFichero;
    }

    public void setFormatoFichero(String formatoFichero) {
        this.formatoFichero = formatoFichero;
    }

    public byte[] getContenido() {
        return contenido;
    }

    public void setContenido(byte[] contenido) {
        this.contenido = contenido;
    }

    public Long getFilasCorrectas() {
        return filasCorrectas;
    }

    public void setFilasCorrectas(Long filasCorrectas) {
        this.filasCorrectas = filasCorrectas;
    }

    public Long getFilasIncorrectas() {
        return filasIncorrectas;
    }

    public void setFilasIncorrectas(Long filasIncorrectas) {
        this.filasIncorrectas = filasIncorrectas;
    }

    public byte[] getResultado() {
        return resultado;
    }

    public void setResultado(byte[] resultado) {
        this.resultado = resultado;
    }

    public List<ErrorImportEntity> getErrores() {
        return errores;
    }

    public void setErrores(List<ErrorImportEntity> errores) {
        this.errores = errores;
    }

    @Override
    public String toString() {
        return "ImportacionEntity{" +
                "id=" + id +
                ", usuarioImportacion='" + usuarioImportacion + '\'' +
                ", fechaImportacion=" + fechaImportacion +
                ", nombreFichero='" + nombreFichero + '\'' +
                ", formatoFichero='" + formatoFichero + '\'' +
                ", filasCorrectas=" + filasCorrectas +
                ", filasIncorrectas=" + filasIncorrectas +
                '}';
    }
}
