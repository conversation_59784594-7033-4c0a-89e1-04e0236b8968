import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { KeycloakService } from 'keycloak-angular';
import { initializeKeycloak } from '../../services/keycloak-init';
import { Router } from '@angular/router';

@Component({
  selector: 'app-inicio',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './inicio.component.html',
  styleUrls: ['./inicio.component.scss']
})
export class InicioComponent {
  constructor(
    private keycloakService: KeycloakService,
    private router: Router
  ) {}
  
  async ngOnInit() {
    // Si ya está logueado, redirige a /home directamente
    const loggedIn = await this.keycloakService.isLoggedIn();
    if (loggedIn) {
      this.router.navigate(['/home']);
    }
  }

  async login() {
    this.keycloakService.login({
      redirectUri: window.location.origin + '/home'
    });
}
}
