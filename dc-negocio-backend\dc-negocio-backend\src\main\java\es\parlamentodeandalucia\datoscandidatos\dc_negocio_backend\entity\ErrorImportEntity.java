package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;

@Entity
@Table(name = "dac_t_error_import")
public class ErrorImportEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "dac_id_error_import")
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnore
    @JoinColumn(name = "dac_fk_importacion")
    private ImportacionEntity importacion;

    @Column(name = "dac_in_linea")
    private Long linea;

    @Column(name = "dac_tx_campo")
    private String campo;

    @Column(name = "dac_tx_error", length = 1024)
    private String error;

    // Constructor por defecto
    public ErrorImportEntity() {
    }

    // Constructor con parámetros
    public ErrorImportEntity(ImportacionEntity importacion, Long linea, String campo, String error) {
        this.importacion = importacion;
        this.linea = linea;
        this.campo = campo;
        this.error = error;
    }

    // Getters y Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public ImportacionEntity getImportacion() {
        return importacion;
    }

    public void setImportacion(ImportacionEntity importacion) {
        this.importacion = importacion;
    }

    public Long getLinea() {
        return linea;
    }

    public void setLinea(Long linea) {
        this.linea = linea;
    }

    public String getCampo() {
        return campo;
    }

    public void setCampo(String campo) {
        this.campo = campo;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    @Override
    public String toString() {
        return "ErrorImportEntity{" +
                "id=" + id +
                ", linea=" + linea +
                ", campo='" + campo + '\'' +
                ", error='" + error + '\'' +
                '}';
    }
}
