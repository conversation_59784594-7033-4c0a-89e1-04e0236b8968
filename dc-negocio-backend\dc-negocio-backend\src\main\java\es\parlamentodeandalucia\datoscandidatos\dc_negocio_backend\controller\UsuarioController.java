package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.controller;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.UsuarioEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service.UsuarioService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/usuarios")
@CrossOrigin(origins = { "http://localhost:4200", "https://localhost:4200" })
public class UsuarioController {

    @Autowired
    private UsuarioService usuarioService;

    // Obtener todos los usuarios (para tabla principal)
    @GetMapping("/consultar")
    public ResponseEntity<List<UsuarioEntity>> getAllUsuarios() {
        return ResponseEntity.ok(usuarioService.buscarTodos());
    }

    // Crear un nuevo usuario
    @PostMapping
    public ResponseEntity<UsuarioEntity> crearUsuario(@RequestBody UsuarioEntity nuevoUsuario) {
        if (nuevoUsuario.getKeycloakId() == null || nuevoUsuario.getKeycloakId().isEmpty()) {
            return ResponseEntity.badRequest().build();
        }
        UsuarioEntity creado = usuarioService.crearUsuario(nuevoUsuario);
        return ResponseEntity.status(201).body(creado);
    }

    // Eliminar un usuario por su keycloakId
    @DeleteMapping("/{keycloakId}")
    public ResponseEntity<Void> eliminarUsuario(@PathVariable String keycloakId) {
        boolean eliminado = usuarioService.eliminarUsuario(keycloakId);
        return eliminado ? ResponseEntity.noContent().build() : ResponseEntity.notFound().build();
    }

    // Buscar un usuario por su keycloakId
    @GetMapping("/{keycloakId}")
    public ResponseEntity<UsuarioEntity> buscarUsuarioPorId(@PathVariable String keycloakId) {
        return usuarioService.buscarPorKeycloakId(keycloakId)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    // Modificar el estado de un usuario a activo
    @PutMapping("/activar/{keycloakId}")
    public ResponseEntity<UsuarioEntity> activarUsuario(@PathVariable String keycloakId) {
        UsuarioEntity usuario = new UsuarioEntity();
        usuario.setKeycloakId(keycloakId);
        usuario.setEstado("activo");
        return ResponseEntity.ok(usuarioService.actualizarEstado(usuario));
    }

    // Modificar el estado de un usuario a inactivo
    @PutMapping("/desactivar/{keycloakId}")
    public ResponseEntity<UsuarioEntity> desactivarUsuario(@PathVariable String keycloakId) {
        UsuarioEntity usuario = new UsuarioEntity();
        usuario.setKeycloakId(keycloakId);
        usuario.setEstado("inactivo");
        return ResponseEntity.ok(usuarioService.actualizarEstado(usuario));
    }

    // Buscar usuarios por rol y estado
    @GetMapping("/buscar")
    public ResponseEntity<List<UsuarioEntity>> buscarPorRolYEstado(
            @RequestParam(required = false) String rol,
            @RequestParam(required = false) String estado) {
        List<UsuarioEntity> resultado = usuarioService.buscarUsuariosPorRolYEstado(rol, estado);
        return ResponseEntity.ok(resultado);
    }

    // Asignar roles a un usuario por su ID de Keycloak
    @PutMapping("/{keycloakId}/roles")
    public ResponseEntity<UsuarioEntity> actualizarRoles(
            @PathVariable String keycloakId,
            @RequestBody List<Long> idsRoles) {
        return ResponseEntity.ok(usuarioService.asignarRoles(keycloakId, idsRoles));
    }

}
