package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.EstadoCandidatoEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository.EstadoCandidatoRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class EstadoCandidatoService {

    @Autowired
    private EstadoCandidatoRepository estadoCandidatoRepository;

    /**
     * Obtiene todos los estados de candidato
     */
    public List<EstadoCandidatoEntity> findAll() {
        return estadoCandidatoRepository.findAll();
    }

    /**
     * Busca un estado de candidato por ID
     */
    public Optional<EstadoCandidatoEntity> findById(Long id) {
        return estadoCandidatoRepository.findById(id);
    }

    /**
     * Busca un estado de candidato por valor
     */
    public Optional<EstadoCandidatoEntity> findByValor(String valor) {
        return estadoCandidatoRepository.findByValor(valor);
    }

    /**
     * Obtiene el estado IMPORTADO
     */
    public Optional<EstadoCandidatoEntity> getImportado() {
        return findByValor("IMPORTADO");
    }

    /**
     * Obtiene el estado MANUAL
     */
    public Optional<EstadoCandidatoEntity> getManual() {
        return findByValor("MANUAL");
    }

    /**
     * Obtiene el estado VALIDADO
     */
    public Optional<EstadoCandidatoEntity> getValidado() {
        return findByValor("VALIDADO");
    }

    /**
     * Busca estados que contengan el texto especificado
     */
    public List<EstadoCandidatoEntity> findByTextoContaining(String texto) {
        return estadoCandidatoRepository.findByTextoContaining(texto);
    }

    /**
     * Verifica si existe un estado con el valor especificado
     */
    public boolean existsByValor(String valor) {
        return estadoCandidatoRepository.existsByValor(valor);
    }

    /**
     * Guarda un estado de candidato
     */
    public EstadoCandidatoEntity save(EstadoCandidatoEntity estadoCandidato) {
        return estadoCandidatoRepository.save(estadoCandidato);
    }

    /**
     * Elimina un estado de candidato por ID
     */
    public void deleteById(Long id) {
        estadoCandidatoRepository.deleteById(id);
    }

    /**
     * Valida que el estado de candidato sea válido
     */
    public boolean isValidEstado(String valor) {
        return "IMPORTADO".equals(valor) || "MANUAL".equals(valor) || "VALIDADO".equals(valor);
    }
}
