{"info": {"_postman_id": "keycloak-auth-collection", "name": "Keycloak Authentication - <PERSON><PERSON>", "description": "Colección para obtener access tokens de Keycloak para la aplicación Datos Candidatos", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "1. Get Access Token (Password Grant)", "event": [{"listen": "test", "script": {"exec": ["// Guardar el access token en una variable de entorno", "if (pm.response.code === 200) {", "    const responseJson = pm.response.json();", "    pm.environment.set('access_token', responseJson.access_token);", "    pm.environment.set('refresh_token', responseJson.refresh_token);", "    pm.environment.set('token_type', responseJson.token_type);", "    ", "    console.log('✅ Access Token obtenido exitosamente');", "    console.log('🔑 Token:', responseJson.access_token);", "    console.log('⏰ Expira en:', responseJson.expires_in, 'segundos');", "    ", "    // Mostrar información del token decodificado", "    try {", "        const tokenParts = responseJson.access_token.split('.');", "        const payload = JSON.parse(atob(tokenParts[1]));", "        console.log('👤 Usuario:', payload.preferred_username);", "        console.log('📧 Email:', payload.email);", "        console.log('🏢 Roles:', payload.resource_access?.datosCandidatos?.roles);", "    } catch (e) {", "        console.log('⚠️ No se pudo decodificar el token');", "    }", "} else {", "    console.log('❌ Error obteniendo token:', pm.response.text());", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "grant_type", "value": "password", "description": "Tipo de grant para autenticación directa"}, {"key": "client_id", "value": "datosCandidatos", "description": "ID del cliente en Keycloak"}, {"key": "username", "value": "{{keycloak_username}}", "description": "Usuario de Keycloak (configurar en variables de entorno)"}, {"key": "password", "value": "{{keycloak_password}}", "description": "Contraseña del usuario (configurar en variables de entorno)"}, {"key": "scope", "value": "openid profile email", "description": "Scopes solicitados"}]}, "url": {"raw": "{{keycloak_url}}/realms/{{keycloak_realm}}/protocol/openid-connect/token", "host": ["{{keycloak_url}}"], "path": ["realms", "{{keycloak_realm}}", "protocol", "openid-connect", "token"]}, "description": "Obtiene un access token usando credenciales de usuario (Resource Owner Password Credentials Grant)"}, "response": []}]}