package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.CandidatoEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.CandidaturaEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.CircunscripcionEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.ConvocatoriaEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository.CandidaturaRepository;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository.CircunscripcionRepository;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class CircunscripcionService {

    @Autowired
    private CircunscripcionRepository circunscripcionRepository;

    public CircunscripcionService(CircunscripcionRepository circunscripcionRepository) {
        this.circunscripcionRepository = circunscripcionRepository;
    }

    @Transactional(readOnly = true)
    public List<CircunscripcionEntity> findAll() {
        List<CircunscripcionEntity> circunscripciones = circunscripcionRepository.findAllConCandidaturas();
        return circunscripciones;
    }

    // Nuevo método para buscar por el estado de 'candidaturaActiva'
    @Transactional(readOnly = true)
    public List<CircunscripcionEntity> findByCandidaturaActiva(boolean activa) {
        // 1. Cargamos TODAS las circunscripciones con sus candidaturas
        List<CircunscripcionEntity> allCircunscripciones = circunscripcionRepository.findAllConCandidaturas();

        // 2. Filtramos la lista en memoria usando el getter calculado
        return allCircunscripciones.stream()
                .filter(c -> c.getCandidaturaActiva() == activa)
                .collect(Collectors.toList());
    }

    public Optional<CircunscripcionEntity> findByNombre(String nombre) {
        return circunscripcionRepository.findByNombre(nombre);
    }

    @Transactional(readOnly = true)
    public Optional<CircunscripcionEntity> findById(Long id) {
        return circunscripcionRepository.findByIdConCandidaturas(id);
    }

    @Transactional
    public CircunscripcionEntity save(CircunscripcionEntity entity) {
        return circunscripcionRepository.save(entity);
    }

    @Transactional
    public CircunscripcionEntity update(Long id, CircunscripcionEntity updatedDetails) {
        CircunscripcionEntity existingCircunscripcion = circunscripcionRepository.findByIdConCandidaturas(id)
                .orElseThrow(() -> new IllegalArgumentException("Circunscripción con ID " + id + " no encontrada."));

        // Actualiza los campos que sí son persistentes en la base de datos
        existingCircunscripcion.setCodigoProvincia(updatedDetails.getCodigoProvincia());
        existingCircunscripcion.setNombre(updatedDetails.getNombre());
        existingCircunscripcion.setCandidaturaActiva(updatedDetails.getCandidaturaActiva());

        return circunscripcionRepository.save(existingCircunscripcion);
    }

    @Transactional
    public void deleteById(Long id) {
        // Usa findByIdConCandidaturas para cargar las colecciones necesarias para la
        // validación
        Optional<CircunscripcionEntity> optionalCircunscripcion = circunscripcionRepository
                .findByIdConCandidaturas(id);

        if (optionalCircunscripcion.isPresent()) {
            CircunscripcionEntity circunscripcion = optionalCircunscripcion.get();

            List<CandidaturaEntity> candidaturas = circunscripcion.getCandidaturas();
            if (candidaturas != null && !candidaturas.isEmpty()) {
                throw new IllegalStateException(
                        "No se puede eliminar la circunscripción porque está asociada a una o más candidaturas.");
            }

           /* List<ConvocatoriaEntity> convocatorias = circunscripcion.getConvocatorias();
            if (convocatorias != null && !convocatorias.isEmpty()) {
                throw new IllegalStateException(
                        "No se puede eliminar la circunscripción porque está asociada a una o más convocatorias.");
            }*/ 

            // Si no hay asociaciones, entonces se puede eliminar
            circunscripcionRepository.deleteById(id);
        } else {
            throw new IllegalArgumentException(
                    "La circunscripción con ID " + id + " no existe y no puede ser eliminada.");
        }
    }
}