package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.TipoCandidaturaEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface TipoCandidaturaRepository extends JpaRepository<TipoCandidaturaEntity, Long> {

    /**
     * Busca un tipo de candidatura por su valor
     * @param valor El valor del tipo (ej: "IMPORTADA", "MANUAL")
     * @return Optional con el tipo encontrado
     */
    Optional<TipoCandidaturaEntity> findByValor(String valor);

    /**
     * Verifica si existe un tipo de candidatura con el valor especificado
     * @param valor El valor a verificar
     * @return true si existe, false en caso contrario
     */
    boolean existsByValor(String valor);

    /**
     * Busca tipos que contengan el texto especificado en el valor
     * @param texto Texto a buscar
     * @return Lista de tipos que coinciden
     */
    @Query("SELECT t FROM TipoCandidaturaEntity t WHERE " +
           "LOWER(t.valor) LIKE LOWER(CONCAT('%', :texto, '%'))")
    java.util.List<TipoCandidaturaEntity> findByTextoContaining(@Param("texto") String texto);
}
