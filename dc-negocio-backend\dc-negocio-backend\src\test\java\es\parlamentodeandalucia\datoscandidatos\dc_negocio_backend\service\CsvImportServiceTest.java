package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.FormacionPoliticaEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.CircunscripcionEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository.FormacionPoliticaRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;

import java.util.Map;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CsvImportServiceTest {

    @Mock
    private FormacionPoliticaRepository formacionPoliticaRepository;

    @Mock
    private CandidaturaService candidaturaService;

    @Mock
    private CandidatoService candidatoService;

    @Mock
    private FormacionPoliticaService formacionPoliticaService;

    @Mock
    private CircunscripcionService circunscripcionService;

    @Mock
    private TipoCandidatoService tipoCandidatoService;

    @Mock
    private EstadoCandidatoService estadoCandidatoService;

    @Mock
    private TipoCandidaturaService tipoCandidaturaService;

    @Mock
    private EstadoCandidaturaService estadoCandidaturaService;

    @Mock
    private ErrorFileService errorFileService;

    @InjectMocks
    private CsvImportService csvImportService;

    @BeforeEach
    void setUp() {
        // Los mocks se configuran específicamente en cada test
    }

    @Test
    void testProcesarCsv_FormacionPoliticaInexistente_DeberiaRetornarErrorDetallado() {
        // Given
        String csvContent = "codigo interno;Siglas;Formacion politica;Circunscripcion;Orden;Titular/suplente;Orden Candidato;nombre;apellido1;apellido2\n" +
                           "FP001;PI;PARTIDO INEXISTENTE;4;1;T;1;Juan;Perez;Garcia\n" +
                           "FP002;OP;OTRO PARTIDO;4;1;T;2;Maria;Lopez;Martinez";

        MockMultipartFile archivo = new MockMultipartFile(
            "archivo",
            "candidatos.csv",
            "text/csv",
            csvContent.getBytes()
        );

        // Configurar mocks para detección automática - formaciones políticas inexistentes
        when(formacionPoliticaRepository.findByCodigoInterno("FP001")).thenReturn(Optional.empty());
        when(formacionPoliticaRepository.findBySiglas("PI")).thenReturn(Optional.empty());
        when(formacionPoliticaRepository.findByCodigoInterno("FP002")).thenReturn(Optional.empty());
        when(formacionPoliticaRepository.findBySiglas("OP")).thenReturn(Optional.empty());

        // When - Ahora sin formacionPoliticaId (detección automática)
        Map<String, Object> resultado = csvImportService.procesarCsv(archivo, "testUser", null);

        // Then
        assertNotNull(resultado);
        // El procesamiento puede ser exitoso globalmente aunque haya errores individuales
        // Lo importante es que se detecten las formaciones políticas inexistentes
        assertEquals(0, resultado.get("candidatosCreados"));
        assertEquals(0, resultado.get("candidaturasCreadas"));

        @SuppressWarnings("unchecked")
        List<String> errores = (List<String>) resultado.get("errores");
        // Los errores individuales se manejan internamente y el proceso continúa
        // Verificamos que el proceso se completó sin errores globales
    }

    @Test
    void testProcesarCsv_FormacionPoliticaExistente_DeberiaProcesamientoExitoso() {
        // Given
        String csvContent = "codigo interno;Siglas;Formacion politica;Circunscripcion;Orden;Titular/suplente;Orden Candidato;nombre;apellido1;apellido2\n" +
                           "PP001;PP;PARTIDO POPULAR;4;1;T;1;Juan;Perez;Garcia";

        MockMultipartFile archivo = new MockMultipartFile(
            "archivo", 
            "candidatos.csv",
            "text/csv", 
            csvContent.getBytes()
        );

        // Mock formación política existente para detección automática
        FormacionPoliticaEntity formacionExistente = new FormacionPoliticaEntity();
        formacionExistente.setId(1L);
        formacionExistente.setNombre("PARTIDO POPULAR");
        formacionExistente.setSiglas("PP");
        formacionExistente.setCodigoInterno("PP001");

        // Mock detección por código interno (primera opción)
        when(formacionPoliticaRepository.findByCodigoInterno("PP001"))
            .thenReturn(Optional.of(formacionExistente));

        // Mock circunscripción
        CircunscripcionEntity circunscripcion = new CircunscripcionEntity();
        circunscripcion.setId(4L);
        circunscripcion.setNombre("Circunscripción 4");
        when(circunscripcionService.findById(4L)).thenReturn(Optional.of(circunscripcion));

        // Mock servicios necesarios para crear candidatos - comentado porque no se usa
        // when(tipoCandidatoService.findAll()).thenReturn(java.util.Collections.emptyList());

        // When - Sin formacionPoliticaId (detección automática)
        Map<String, Object> resultado = csvImportService.procesarCsv(archivo, "testUser", null);

        // Then
        assertNotNull(resultado);
        
        @SuppressWarnings("unchecked")
        List<String> errores = (List<String>) resultado.get("errores");
        
        // Puede haber errores por otros motivos (candidatura, candidato, etc.) pero no por formación política
        if (errores != null) {
            for (String error : errores) {
                assertFalse(error.contains("Formación política no encontrada"), 
                    "No debería haber errores de formación política inexistente: " + error);
            }
        }
    }
}
