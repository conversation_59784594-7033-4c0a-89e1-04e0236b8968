package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service;

import java.sql.Date;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.ConvocatoriaEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository.ConvocatoriaRepository;
import jakarta.transaction.Transactional;

@Service
@Transactional
public class ConvocatoriaService {

    @Autowired
    private ConvocatoriaRepository convocatoriaRepository;
    
    private AuditaConvocatoriaService auditaConvocatoriaService;

    
    private void auditarFechaCampo(String nombreCampo, ConvocatoriaEntity original, ConvocatoriaEntity nueva,
                               java.sql.Date valorOriginal, java.sql.Date valorNuevo) {
    auditaConvocatoriaService.auditarCambioFecha(original, nueva, nombreCampo, valorOriginal, valorNuevo, "Cambio manual");
}

    public List<ConvocatoriaEntity> findAll() {
        return convocatoriaRepository.findAll();
    }

    public Optional<ConvocatoriaEntity> findById(Long id) {
        return convocatoriaRepository.findById(id);
    }

    public ConvocatoriaEntity save(ConvocatoriaEntity convocatoria) {
        return convocatoriaRepository.save(convocatoria);
    }

    public void deleteById(Long id) {
        convocatoriaRepository.deleteById(id);
    }

    /**
     * Verifica si hay una convocatoria activa (período de presentación de candidaturas)
     * Por ahora retorna true para permitir importaciones durante desarrollo
     * TODO: Implementar cuando ConvocatoriaEntity tenga los campos del Script 2
     */
    public boolean hayConvocatoriaActiva() {
        // Temporal: permitir importaciones durante desarrollo
        return true;

        // TODO: Implementar cuando ConvocatoriaEntity tenga los campos correctos:
        // LocalDate hoy = LocalDate.now();
        // List<ConvocatoriaEntity> convocatorias = findAll();
        //
        // return convocatorias.stream().anyMatch(convocatoria -> {
        //     LocalDate inicioPresent = convocatoria.getFechaInicioPresent();
        //     LocalDate finPresent = convocatoria.getFechaFinPresent();
        //
        //     return inicioPresent != null && finPresent != null &&
        //            !hoy.isBefore(inicioPresent) && !hoy.isAfter(finPresent);
        // });
    }

    public ConvocatoriaEntity crearConvocatoria(ConvocatoriaEntity convocatoria) {
        ajustarFechasAutomáticamente(convocatoria);
        return convocatoriaRepository.save(convocatoria);
    }

    public Optional<ConvocatoriaEntity> obtenerConvocatoriaPorId(Long id) {
        return convocatoriaRepository.findById(id);
    }

    public ConvocatoriaEntity actualizarConvocatoria(ConvocatoriaEntity convocatoriaNueva) {
        Optional<ConvocatoriaEntity> existenteOpt = convocatoriaRepository.findById(convocatoriaNueva.getId());
        if (existenteOpt.isEmpty())
            throw new IllegalArgumentException("Convocatoria no encontrada");

        ConvocatoriaEntity existente = existenteOpt.get();

        auditarFechaCampo("fecha", existente, convocatoriaNueva, existente.getFecha(), convocatoriaNueva.getFecha());
        auditarFechaCampo("fechaElecciones", existente, convocatoriaNueva, existente.getFechaElecciones(), convocatoriaNueva.getFechaElecciones());
        auditarFechaCampo("fechaInicioPresentacion", existente, convocatoriaNueva, existente.getFechaInicioPresentacion(), convocatoriaNueva.getFechaInicioPresentacion());
        auditarFechaCampo("fechaFinPresentacion", existente, convocatoriaNueva, existente.getFechaFinPresentacion(), convocatoriaNueva.getFechaFinPresentacion());
        auditarFechaCampo("fechaInicioPublicacionBoja", existente, convocatoriaNueva, existente.getFechaInicioPublicacionBoja(), convocatoriaNueva.getFechaInicioPublicacionBoja());
        auditarFechaCampo("fechaIrregularidades", existente, convocatoriaNueva, existente.getFechaIrregularidades(), convocatoriaNueva.getFechaIrregularidades());
        auditarFechaCampo("fechaInicioSubsanacion", existente, convocatoriaNueva, existente.getFechaInicioSubsanacion(), convocatoriaNueva.getFechaInicioSubsanacion());
        auditarFechaCampo("fechaFinSubsanacion", existente, convocatoriaNueva, existente.getFechaFinSubsanacion(), convocatoriaNueva.getFechaFinSubsanacion());
        auditarFechaCampo("fechaProclamacion", existente, convocatoriaNueva, existente.getFechaProclamacion(), convocatoriaNueva.getFechaProclamacion());
        auditarFechaCampo("fechaPublicacionProclamada", existente, convocatoriaNueva, existente.getFechaPublicacionProclamada(), convocatoriaNueva.getFechaPublicacionProclamada());
        auditarFechaCampo("fechaInicioDeclaracion", existente, convocatoriaNueva, existente.getFechaInicioDeclaracion(), convocatoriaNueva.getFechaInicioDeclaracion());
        auditarFechaCampo("fechaFinDeclaracion", existente, convocatoriaNueva, existente.getFechaFinDeclaracion(), convocatoriaNueva.getFechaFinDeclaracion());
        auditarFechaCampo("fechaInicioPublicacionInternet", existente, convocatoriaNueva, existente.getFechaInicioPublicacionInternet(), convocatoriaNueva.getFechaInicioPublicacionInternet());
        auditarFechaCampo("fechaFinPublicacionInternet", existente, convocatoriaNueva, existente.getFechaFinPublicacionInternet(), convocatoriaNueva.getFechaFinPublicacionInternet());
        auditarFechaCampo("fechaCancelacion", existente, convocatoriaNueva, existente.getFechaCancelacion(), convocatoriaNueva.getFechaCancelacion());

        validarFechasLogicas(convocatoriaNueva);

        return convocatoriaRepository.save(convocatoriaNueva);
    }

    private void ajustarFechasAutomáticamente(ConvocatoriaEntity convocatoria) {
        if (convocatoria.getFecha() == null)
            return;

        LocalDate base = convocatoria.getFecha().toLocalDate();

        convocatoria.setFechaInicioPresentacion(Date.valueOf(base.plusDays(15)));
        convocatoria.setFechaFinPresentacion(Date.valueOf(base.plusDays(20)));
        convocatoria.setFechaInicioPublicacionBoja(Date.valueOf(base.plusDays(22)));
        convocatoria.setFechaIrregularidades(Date.valueOf(base.plusDays(24)));
        convocatoria.setFechaInicioSubsanacion(Date.valueOf(base.plusDays(25)));
        convocatoria.setFechaFinSubsanacion(Date.valueOf(base.plusDays(26)));
        convocatoria.setFechaProclamacion(Date.valueOf(base.plusDays(27)));
        convocatoria.setFechaPublicacionProclamada(Date.valueOf(base.plusDays(28)));
        convocatoria.setFechaInicioDeclaracion(Date.valueOf(base.plusDays(28)));
        convocatoria.setFechaFinDeclaracion(Date.valueOf(base.plusDays(30)));
        convocatoria.setFechaInicioPublicacionInternet(Date.valueOf(base.plusDays(31)));
        convocatoria.setFechaFinPublicacionInternet(Date.valueOf(base.plusDays(47)));

        if (convocatoria.getFechaElecciones() != null) {
            LocalDate elecciones = convocatoria.getFechaElecciones().toLocalDate();
            convocatoria.setFechaCancelacion(Date.valueOf(elecciones.minusDays(25)));
        }
    }

    private void validarFechasLogicas(ConvocatoriaEntity c) {
        if (c.getFechaInicioPresentacion().after(c.getFechaFinPresentacion())) {
            throw new IllegalArgumentException(
                    "La fecha de inicio de presentación no puede ser posterior a la de fin.");
        }
        if (c.getFechaInicioSubsanacion().after(c.getFechaFinSubsanacion())) {
            throw new IllegalArgumentException("La fecha de inicio de subsanación no puede ser posterior a la de fin.");
        }
        if (c.getFechaInicioDeclaracion().after(c.getFechaFinDeclaracion())) {
            throw new IllegalArgumentException(
                    "La fecha de inicio de declaraciones no puede ser posterior a la de fin.");
        }
        if (c.getFechaInicioPublicacionInternet().after(c.getFechaFinPublicacionInternet())) {
            throw new IllegalArgumentException(
                    "La fecha de inicio de publicación en Internet no puede ser posterior a la de fin.");
        }
    }

}
