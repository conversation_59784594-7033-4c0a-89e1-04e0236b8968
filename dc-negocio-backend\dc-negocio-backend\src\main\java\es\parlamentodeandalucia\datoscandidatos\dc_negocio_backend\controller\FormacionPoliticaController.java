package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.controller;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service.FormacionPoliticaService;
import es.parlamentodeandalucia.datoscandidatos.dto.FormacionPolitica;
import es.parlamentodeandalucia.datoscandidatos.dto.FormacionPoliticaInput;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/formaciones-politicas")
@CrossOrigin
public class FormacionPoliticaController {

    private final FormacionPoliticaService formacionPoliticaService;

    FormacionPoliticaController(FormacionPoliticaService formacionPoliticaService) {
        this.formacionPoliticaService = formacionPoliticaService;
    }

    @GetMapping
    //@PreAuthorize("hasRole('ADMIN') or hasRole('REPRESENTANTE')")
    public ResponseEntity<List<FormacionPolitica>> getFormacionesPoliticas(@AuthenticationPrincipal Jwt jwt) {

        return formacionPoliticaService.listarTodas()
                .stream()
                .map(formacion -> new FormacionPolitica()
                        .id(formacion.getId())
                        .nombre(formacion.getNombre())
                        .siglas(formacion.getSiglas())
                        .codigoInterno(formacion.getCodigoInterno())
                        .activa(formacion.getActiva()))
                .collect(Collectors.collectingAndThen(Collectors.toList(), ResponseEntity::ok));
    }

    @GetMapping("/{id}")
    //@PreAuthorize("hasRole('ADMIN') or hasRole('REPRESENTANTE')")
    public ResponseEntity<FormacionPolitica> getFormacionPolitica(@PathVariable Long id, @AuthenticationPrincipal Jwt jwt) {

        return formacionPoliticaService.buscarPorId(id)
                .map(formacion -> new FormacionPolitica()
                        .id(formacion.getId())
                        .nombre(formacion.getNombre())
                        .siglas(formacion.getSiglas())
                        .codigoInterno(formacion.getCodigoInterno())
                        .activa(formacion.getActiva()))
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @PostMapping
    //@PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<FormacionPolitica> createFormacionPolitica(@RequestBody FormacionPoliticaInput input) {

        FormacionPolitica guardada = formacionPoliticaService.guardar(new FormacionPolitica()
                .nombre(input.getNombre())
                .siglas(input.getSiglas())
                .codigoInterno(input.getCodigoInterno())
                .activa(input.getActiva()));

        if (guardada == null) {
            return ResponseEntity.badRequest().build();
        }

        return ResponseEntity.ok(new FormacionPolitica()
                .id(guardada.getId())
                .nombre(guardada.getNombre())
                .siglas(guardada.getSiglas())
                .codigoInterno(guardada.getCodigoInterno())
                .activa(guardada.getActiva()));
    }


    @PutMapping("/{id}")
    //@PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<FormacionPolitica> actualizarFormacionPolitica(
            @PathVariable Long id,
            @RequestBody FormacionPoliticaInput input,
            @AuthenticationPrincipal Jwt jwt) {

        FormacionPolitica dto = new FormacionPolitica()
                .nombre(input.getNombre())
                .siglas(input.getSiglas())
                .codigoInterno(input.getCodigoInterno())
                .activa(input.getActiva()); 

        return formacionPoliticaService.actualizar(id, dto)
                .map(actualizada -> ResponseEntity.ok(actualizada))
                .orElseGet(() -> ResponseEntity.notFound().build());
    }

    @DeleteMapping("/{id}")
    //@PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> eliminarFormacionPolitica(
            @PathVariable Long id,
            @AuthenticationPrincipal Jwt jwt) {

        if (formacionPoliticaService.buscarPorId(id).isEmpty()) {
            return ResponseEntity.notFound().build();
        }

        formacionPoliticaService.eliminarPorId(id);
        return ResponseEntity.noContent().build();
    }
}