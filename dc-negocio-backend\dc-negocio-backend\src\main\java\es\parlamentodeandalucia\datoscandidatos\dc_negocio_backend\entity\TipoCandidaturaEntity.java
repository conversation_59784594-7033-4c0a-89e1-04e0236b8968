package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity;

import jakarta.persistence.*;

@Entity
@Table(name = "dac_t_tipo_candidatura")
public class TipoCandidaturaEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "dac_id_tipo_candidatura")
    private Long id;

    @Column(name = "dac_tx_valor")
    private String valor;

    // Constructor por defecto
    public TipoCandidaturaEntity() {
    }

    // Constructor con parámetros
    public TipoCandidaturaEntity(Long id, String valor) {
        this.id = id;
        this.valor = valor;
    }

    // Getters y Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getValor() {
        return valor;
    }

    public void setValor(String valor) {
        this.valor = valor;
    }

    @Override
    public String toString() {
        return "TipoCandidaturaEntity{" +
                "id=" + id +
                ", valor='" + valor + '\'' +
                '}';
    }
}
