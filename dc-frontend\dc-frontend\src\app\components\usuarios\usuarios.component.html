<div class="layout-container">
    <app-header class="header-fixed"></app-header>

    <div class="page-wrapper scrollable-content">
        <div class="breadcrumbs">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="16px" height="16px">
                <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z" />
            </svg>
            <span class="separator">&gt;</span>
            <span>Administración</span>
            <span class="separator">&gt;</span>
            <strong>Usuarios</strong>
        </div>

        <div class="title-section">
            <a routerLink="/home" class="back-button">
                <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24" width="24px"
                    height="24px">
                    <path d="M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z" transform="rotate(180 12 12)" />
                </svg>
            </a>
            <h1 class="page-main-title">Usuarios</h1>
        </div>

        <div class="usuarios-page-container">
            <div class="filter-actions-bar">
                <button class="btn-text" (click)="clearFilters()">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24" width="16px"
                        height="16px">
                        <path
                            d="M6 13c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm14-10v2h-3.21l-3.42 8H18v2h-7.15c-.78 1.76-2.58 3-4.85 3-2.21 0-4-1.79-4-4s1.79-4 4-4c.78 0 1.5.22 2.15.62L12.58 4H20V2h2v2h-2z" />
                    </svg>
                    Limpiar filtros
                </button>
                <div class="dropdown-masivo" (clickOutside)="showMassiveActions = false">
                    <button class="btn-massive" (click)="showMassiveActions = !showMassiveActions">
                        Acciones masivas
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                            viewBox="0 0 24 24">
                            <path d="M7 10l5 5 5-5H7z" />
                        </svg>
                    </button>
                    <ul class="dropdown-options" *ngIf="showMassiveActions">
                        <li (click)="activateAll()">Activar</li>
                        <li (click)="deactivateAll()">Desactivar</li>
                        <li (click)="openModalChangeRol('rol', null)">Asignar Rol</li>
                    </ul>
                </div>
            </div>

            <p-table #dt [value]="users" [paginator]="true" [rows]="10"
                [globalFilterFields]="['name', 'email', 'role', 'status']" [selection]="selectedUsers"
                (selectionChange)="selectedUsers = $event" [rowHover]="true" dataKey="id"
                paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown">
                <ng-template pTemplate="header">
                    <tr>
                        <th style="width: 3rem">
                            <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
                        </th>
                        <th pSortableColumn="name">
                            Nombre
                            <p-sortIcon field="name" />
                            <p-columnFilter field="name" matchMode="startsWith" display="menu"
                                type="text"></p-columnFilter>
                        </th>
                        <th pSortableColumn="status">
                            Estado
                            <p-sortIcon field="status" />
                            <p-columnFilter field="status" matchMode="equals" display="menu">
                                <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                    <p-dropdown [options]="estados" [ngModel]="value" (onChange)="filter($event.value)"
                                        optionLabel="label" optionValue="value" placeholder="Selecciona estado"
                                        class="w-full">
                                    </p-dropdown>
                                </ng-template>
                            </p-columnFilter>
                        </th>
                        <th>
                            Rol
                            <p-columnFilter type="multiselect" field="roles" display="menu" [showMenu]="false">
                                <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                    <p-multiSelect [options]="roles" [ngModel]="value" (onChange)="filter($event.value)"
                                        optionLabel="label" optionValue="value" placeholder="Filtrar por rol"
                                        class="w-full">
                                    </p-multiSelect>
                                </ng-template>
                            </p-columnFilter>
                        </th>
                        <th pSortableColumn="email">
                            Correo
                            <p-sortIcon field="email" />
                            <p-columnFilter field="email" matchMode="contains" display="menu"
                                type="text"></p-columnFilter>
                        </th>
                        <th pSortableColumn="registrationDate">
                            Fecha Alta
                            <p-sortIcon field="registrationDate" />
                            <p-columnFilter field="registrationDate" matchMode="between" display="menu" dataType="date">
                                <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                    <p-calendar [ngModel]="value" (ngModelChange)="filter($event)" selectionMode="range"
                                        dateFormat="dd/mm/yy" [showTime]="true" hourFormat="24" [showIcon]="true"
                                        placeholder="Filtrar por rango de fecha" class="w-full">
                                    </p-calendar>

                                </ng-template>
                            </p-columnFilter>
                        </th>
                        <th pSortableColumn="lastAccess">
                            Último Acceso
                            <p-sortIcon field="lastAccess" />
                            <p-columnFilter field="lastAccess" matchMode="between" display="menu" dataType="date">
                                <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                    <p-calendar [ngModel]="value" (ngModelChange)="filter($event)" selectionMode="range"
                                        dateFormat="dd/mm/yy" [showTime]="true" hourFormat="24" [showIcon]="true"
                                        placeholder="Filtrar por rango de fecha" class="w-full">
                                    </p-calendar>
                                </ng-template>
                            </p-columnFilter>
                        </th>
                        <th style="width: 3rem;"></th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-user let-rowIndex="rowIndex">
                    <tr [ngClass]="getRowStyle(user, rowIndex)">
                        <td><p-tableCheckbox [value]="user"></p-tableCheckbox></td>
                        <td>{{ user.name }}</td>
                        <td><span class="status-badge" [ngClass]="getStatusClass(user.status)">{{ user.status }}</span>
                        </td>
                        <td>
                            <span *ngFor="let rol of user.roles" class="role-badge" [ngClass]="getRoleClass(rol)">
                                {{ rol }}
                            </span>
                        </td>
                        <td class="wrap-email">{{ user.email }}</td>
                        <td>{{ user.registrationDate }}</td>
                        <td>{{ user.lastAccess }}</td>
                        <td class="acciones-cell">
                            <div class="dropdown">
                                <button class="btn-icon dropdown-toggle" (click)="toggleDropdown(user.id)">
                                    Acciones
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor"
                                        viewBox="0 0 24 24">
                                        <path
                                            d="M12 7a2 2 0 1 0 0-4 2 2 0 0 0 0 4zm0 2a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm0 6a2 2 0 1 0 0 4 2 2 0 0 0 0-4z" />
                                    </svg>
                                </button>
                                <div class="dropdown-menu" *ngIf="openedDropdownId === user.id">
                                    <a class="dropdown-item" (click)="openModalChangeRol('acciones', user.id)">
                                        Cambiar rol
                                    </a>
                                    <a class="dropdown-item" *ngIf="user.status === 'Activo'"
                                        (click)="deactivateThisUser(user.id)">
                                        Desactivar
                                    </a>
                                    <a class="dropdown-item" *ngIf="user.status === 'Inactivo'"
                                        (click)="activateThisUser(user.id)">
                                        Activar
                                    </a>
                                </div>
                            </div>
                        </td>
                    </tr>
                </ng-template>
            </p-table>
            
            <app-modal-cambiar-rol [(visible)]="showRoleModal" [selectedUserId]="selectedUserId"
                [currentRoles]="currentRoles" (saveRole)="handleRoleChange($event)">
            </app-modal-cambiar-rol>

        </div>
    </div>

    <app-footer class="footer-fixed"></app-footer>
    
</div>