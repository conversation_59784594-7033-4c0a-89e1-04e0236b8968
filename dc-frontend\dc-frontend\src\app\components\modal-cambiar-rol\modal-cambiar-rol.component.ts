import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { FormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { MultiSelectModule } from 'primeng/multiselect';

@Component({
  selector: 'app-modal-cambiar-rol',
  standalone: true,
  imports: [CommonModule, DialogModule, DropdownModule, FormsModule, ButtonModule, MultiSelectModule],
  templateUrl: './modal-cambiar-rol.component.html',
  styleUrls: ['./modal-cambiar-rol.component.scss']
})
export class ModalCambiarRolComponent {
  @Input() visible: boolean = false;
  @Input() selectedUserId: string | null = null;
  @Input() currentRoles: number[] = [];

  @Output() visibleChange = new EventEmitter<boolean>();
  @Output() saveRole = new EventEmitter<{ userId: string | null, newRoles: number[] }>();

  // Lista completa de roles disponibles en el sistema
  roles = [
    { label: 'Administrador del sistema', value: 1 },
    { label: 'Administrador de JEA', value: 2 },
    { label: 'Miembro JEA', value: 3 },
    { label: 'Representante', value: 4 },
    { label: 'Candidato', value: 5 }
  ];

  // Función para el boton "Guardar" los roles seleccionados
  confirmChange() {
    if (this.currentRoles.length > 0) {
      this.saveRole.emit({ userId: this.selectedUserId, newRoles: this.currentRoles });
      this.close();
    }
  } 

  // Función para cerrar modal
  close() {
    this.visible = false;
    this.visibleChange.emit(false);
  }
}
