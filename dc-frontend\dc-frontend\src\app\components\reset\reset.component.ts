import { Component, OnInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import { HeaderComponent } from '../header/header.component';
import { FooterComponent } from '../footer/footer.component';
import { ModalResetComponent } from '../modal-reset/modal-reset.component';
import { ConvocatoriaService } from '../../services/fecha-cancelacionService';

@Component({
  selector: 'app-reset',
  standalone: true,
  imports: [
    RouterModule,
    HeaderComponent,
    FooterComponent,
    ModalResetComponent
  ],
  templateUrl: './reset.component.html',
  styleUrls: ['./reset.component.scss']
})
export class ResetComponent implements OnInit {

  showModal = false;
  mostrarBoton = false;

  constructor(private convocatoriaService: ConvocatoriaService) {}

  ngOnInit(): void {
    this.convocatoriaService.getFechaCancelacion().subscribe(fecha => {
      if (fecha) {
        const fechaCancelacion = new Date(fecha);
        const hoy = new Date();
        this.mostrarBoton = hoy >= fechaCancelacion;
      }
    });
  }

  openModal(): void {
    this.showModal = true;
  }

  closeModal(): void {
    this.showModal = false;
  }

  confirmDelete(): void {
    console.log('Base de datos reseteada');
    this.closeModal(); 
  }
}
