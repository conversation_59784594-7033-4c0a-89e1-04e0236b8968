import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { HttpClientModule, HttpClient, HttpHeaders } from '@angular/common/http';

import { HeaderComponent } from '../header/header.component';
import { FooterComponent } from '../footer/footer.component';

import { ButtonModule } from 'primeng/button';
import { ToastModule } from 'primeng/toast';
import { ProgressBarModule } from 'primeng/progressbar';
import { CardModule } from 'primeng/card';
import { FileUploadModule } from 'primeng/fileupload';
import { TableModule } from 'primeng/table';
import { DialogModule } from 'primeng/dialog';
import { TooltipModule } from 'primeng/tooltip';
import { MessageService } from 'primeng/api';

import { environment } from '../../../environments/environment';
import { KeycloakService } from 'keycloak-angular';

// Interfaces
interface ImportacionDto {
  id: number;
  nombreFichero: string;
  usuario: string;
  fechaImportacion: string;
  filasCorrectas: number;
  filasIncorrectas: number;
  formatoFichero: string;
}

@Component({
  selector: 'app-importacion-candidaturas',
  standalone: true,
  imports: [
    CommonModule, RouterModule, FormsModule, HttpClientModule,
    HeaderComponent, FooterComponent,
    ButtonModule, ToastModule, ProgressBarModule, CardModule, FileUploadModule,
    TableModule, DialogModule, TooltipModule
  ],
  providers: [MessageService],
  templateUrl: './importacion-candidaturas.component.html',
  styleUrls: ['./importacion-candidaturas.component.scss']
})
export class ImportacionCandidaturasComponent implements OnInit {
  @ViewChild('fileInput') fileInput!: ElementRef<HTMLInputElement>;

  // Datos de la tabla
  importaciones: ImportacionDto[] = [];
  loading = false;

  // Modal de importación
  showImportModal = false;
  selectedFile: File | null = null;
  isUploading = false;
  uploadProgress = 0;
  logContent = '';
  showLog = false;

  constructor(
    private router: Router,
    private http: HttpClient,
    private messageService: MessageService,
    private keycloak: KeycloakService
  ) {}

  ngOnInit(): void {
    this.cargarImportaciones();
  }

  cargarImportaciones(): void {
    this.loading = true;

    // Obtener token y hacer petición al backend
    this.keycloak.getToken().then(token => {
      const headers = new HttpHeaders({
        'Authorization': `Bearer ${token}`
      });

      this.http.get<ImportacionDto[]>(`${environment.apiUrl}/importaciones`, { headers })
        .subscribe({
          next: (data) => {
            this.importaciones = data;
            this.loading = false;
          },
          error: (error) => {
            console.error('Error al cargar importaciones:', error);
            this.messageService.add({
              severity: 'error',
              summary: 'Error',
              detail: 'No se pudieron cargar las importaciones'
            });
            this.loading = false;
          }
        });
    }).catch(error => {
      console.error('Error al obtener token:', error);
      this.loading = false;
    });
  }

  goBack(): void {
    this.router.navigate(['/candidaturas']);
  }

  abrirModalImportar(): void {
    this.showImportModal = true;
    this.limpiarSeleccion();
  }

  cerrarModal(): void {
    if (!this.isUploading) {
      this.showImportModal = false;
      this.limpiarSeleccion();
    }
  }

  nuevaImportacion(): void {
    this.limpiarSeleccion();
    this.showLog = false;
  }

  onFileSelect(): void {
    this.fileInput.nativeElement.click();
  }

  onFileChange(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Validar que sea un archivo CSV
      if (!file.name.toLowerCase().endsWith('.csv')) {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Solo se permiten archivos CSV (.csv)'
        });
        return;
      }

      this.selectedFile = file;
      this.logContent = '';
      this.showLog = false;
      
      this.messageService.add({
        severity: 'success',
        summary: 'Archivo seleccionado',
        detail: `Archivo "${file.name}" listo para importar`
      });
    }
  }

  async procesarImportacion(): Promise<void> {
    if (!this.selectedFile) {
      this.messageService.add({
        severity: 'warn',
        summary: 'Advertencia',
        detail: 'Debe seleccionar un archivo CSV primero'
      });
      return;
    }

    this.isUploading = true;
    this.uploadProgress = 0;
    this.logContent = '';
    this.showLog = true;

    try {
      // Obtener token de autenticación
      const token = await this.keycloak.getToken();
      
      // Preparar FormData
      const formData = new FormData();
      formData.append('archivo', this.selectedFile);

      // Configurar headers
      const headers = new HttpHeaders({
        'Authorization': `Bearer ${token}`
      });

      // Simular progreso más realista
      this.simularProgreso();

      // Realizar la petición
      const response = await this.http.post<any>(
        `${environment.apiUrl}/importaciones/procesar-csv`,
        formData,
        { headers }
      ).toPromise();

      this.uploadProgress = 100;

      // Procesar respuesta
      this.procesarRespuesta(response);

      // Recargar la tabla de importaciones
      this.cargarImportaciones();

    } catch (error: any) {
      this.isUploading = false;
      this.uploadProgress = 0;
      
      console.error('Error en la importación:', error);
      
      let errorMessage = 'Error desconocido durante la importación';
      if (error.error?.error) {
        errorMessage = error.error.error;
      } else if (error.message) {
        errorMessage = error.message;
      }

      this.logContent = `ERROR: ${errorMessage}\n\nDetalles técnicos:\n${JSON.stringify(error, null, 2)}`;
      
      this.messageService.add({
        severity: 'error',
        summary: 'Error en la importación',
        detail: errorMessage
      });
    }
  }

  private procesarRespuesta(response: any): void {
    this.isUploading = false;
    
    if (response) {
      // Construir log de resultado
      let log = '=== RESULTADO DE LA IMPORTACIÓN ===\n\n';
      log += `Archivo procesado: ${response.nombreFichero || this.selectedFile?.name}\n`;
      log += `ID de importación: ${response.importacionId || 'N/A'}\n`;
      log += `Formato: ${response.formatoFichero || 'CSV'}\n`;
      log += `Usuario: Usuario por defecto (sistema en desarrollo)\n\n`;
      
      log += '=== ESTADÍSTICAS ===\n';
      log += `Filas correctas: ${response.filasCorrectas || 0}\n`;
      log += `Filas incorrectas: ${response.filasIncorrectas || 0}\n`;
      log += `Candidatos válidos: ${response.candidatosValidos || 0}\n`;
      log += `Candidaturas válidas: ${response.candidaturasValidas || 0}\n\n`;

      // Mostrar errores si los hay
      if (response.errores && response.errores.length > 0) {
        log += '=== ERRORES ENCONTRADOS ===\n';
        response.errores.forEach((error: any, index: number) => {
          log += `${index + 1}. ${error.descripcion || error}\n`;
        });
        log += '\n';
      }

      // Si no hay errores, mostrar mensaje de éxito
      if (!response.errores || response.errores.length === 0) {
        log += '✅ IMPORTACIÓN COMPLETADA SIN ERRORES\n';
        log += 'Todos los registros se procesaron correctamente.\n\n';
        
        this.messageService.add({
          severity: 'success',
          summary: 'Importación exitosa',
          detail: `Se procesaron ${response.candidatosValidos || 0} candidatos correctamente`
        });
      } else {
        this.messageService.add({
          severity: 'warn',
          summary: 'Importación con errores',
          detail: `Se encontraron ${response.errores.length} errores. Revise el log para más detalles.`
        });
      }

      log += '=== FIN DEL LOG ===';
      this.logContent = log;

    } else {
      this.logContent = 'ERROR: No se recibió respuesta del servidor';
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'No se recibió respuesta del servidor'
      });
    }
  }

  limpiarSeleccion(): void {
    this.selectedFile = null;
    this.logContent = '';
    this.showLog = false;
    this.uploadProgress = 0;
    this.isUploading = false;

    // Limpiar el input file
    if (this.fileInput) {
      this.fileInput.nativeElement.value = '';
    }
  }

  descargarLog(importacion: ImportacionDto): void {
    this.keycloak.getToken().then(token => {
      const headers = new HttpHeaders({
        'Authorization': `Bearer ${token}`
      });

      // Llamar al endpoint de descarga
      this.http.get(`${environment.apiUrl}/importaciones/${importacion.id}/descargar`, {
        headers,
        responseType: 'blob'
      }).subscribe({
        next: (blob) => {
          // Crear un enlace temporal para descargar el log
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;

          // Generar nombre del archivo de log
          const nombreBase = importacion.nombreFichero.replace(/\.[^/.]+$/, "");
          link.download = `${nombreBase}_log.txt`;

          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);

          this.messageService.add({
            severity: 'success',
            summary: 'Descarga exitosa',
            detail: `Log de ${importacion.nombreFichero} descargado correctamente`
          });
        },
        error: (error) => {
          console.error('Error al descargar archivo:', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Error en la descarga',
            detail: 'No se pudo descargar el archivo'
          });
        }
      });
    }).catch(error => {
      console.error('Error al obtener token:', error);
      this.messageService.add({
        severity: 'error',
        summary: 'Error de autenticación',
        detail: 'No se pudo obtener el token de acceso'
      });
    });
  }

  private simularProgreso(): void {
    // Progreso inicial rápido
    setTimeout(() => this.uploadProgress = 15, 100);
    setTimeout(() => this.uploadProgress = 25, 300);
    setTimeout(() => this.uploadProgress = 40, 600);

    // Progreso medio más lento (simulando procesamiento)
    setTimeout(() => this.uploadProgress = 55, 1000);
    setTimeout(() => this.uploadProgress = 65, 1500);
    setTimeout(() => this.uploadProgress = 75, 2200);

    // Progreso final muy lento (esperando respuesta)
    setTimeout(() => this.uploadProgress = 85, 3000);
    setTimeout(() => this.uploadProgress = 92, 4000);

    // El 100% se establece cuando llega la respuesta real
  }
}
