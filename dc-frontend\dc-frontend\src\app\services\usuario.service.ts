import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

import { environment } from '../../environments/environment';

export interface Usuario {
  keycloakId: string;
  username: string;
  nombre: string;
  apellido1: string;
  apellido2: string;
  nombreCompleto: string;
  email: string;
  roles: Rol[];
  estado: string;
  activo: boolean;
  fechaCreacion: string;
  ultimoAcceso: string;
}

export interface Rol {
  id: number;
  valor: string;
}

@Injectable({
  providedIn: 'root'
})
export class UsuarioService {

  private readonly apiUrl = environment.apiUrl + '/usuarios'

  constructor(private http: HttpClient) { }

  // Lista todos los usuarios
  getUsuarios(): Observable<Usuario[]> {
    return this.http.get<Usuario[]>(`${this.apiUrl}/consultar`);
  }

  // Activar un usuario por su ID de Keycloak
  activarUsuario(keycloakId: string): Observable<void> {
    const url = `${this.apiUrl}/activar/${keycloakId}`;
    return this.http.put<void>(url, null);
  }

  // Desactivar un usuario por su ID de Keycloak
  desactivarUsuario(keycloakId: string): Observable<void> {
    const url = `${this.apiUrl}/desactivar/${keycloakId}`;
    return this.http.put<void>(url, null);
  }

  // Actualizar los roles de un usuario por su ID de Keycloak
  actualizarRolesUsuario(keycloakId: string, nuevosRolesIds: number[]): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/${keycloakId}/roles`, nuevosRolesIds, {
      headers: { 'Content-Type': 'application/json' }
    });
  }

}
