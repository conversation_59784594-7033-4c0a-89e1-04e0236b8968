package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository;

import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.RolEntity;

@Repository
public interface RolRepository extends JpaRepository<RolEntity, Long> {
    
    Optional<RolEntity> findByValorIgnoreCase(String valor);

}
