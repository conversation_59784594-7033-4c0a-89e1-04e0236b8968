openapi: 3.0.0
info:
  title: API Representantes - Datos Candida<PERSON>
  description: API para la gestión de representantes, candidatos, usuarios y autenticación del sistema de Datos Candidatos
  version: 1.0.0
  contact:
    name: <PERSON><PERSON><PERSON> de Desarrollo
    email: <EMAIL>

servers:
  - url: http://localhost:8080/api
    description: Servidor de desarrollo local
  - url: https://***************:8463/api/v1/myservice
    description: Servidor de producción

security:
  - BearerAuth: []
  - ClaveAuth: []
  - CertificateAuth: []

paths:
  # Autenticación y gestión de sesiones
  /auth/login:
    post:
      tags:
        - Autenticación
      summary: Inicio de sesión de usuarios
      description: Permite el inicio de sesión mediante Cl@ve, certificado digital o usuario/contraseña
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              oneOf:
                - $ref: '#/components/schemas/LoginCredentials'
                - $ref: '#/components/schemas/ClaveLogin'
                - $ref: '#/components/schemas/CertificateLogin'
      responses:
        '200':
          description: Login exitoso
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '401':
          description: Credenciales inválidas
        '400':
          description: Datos de entrada inválidos

  /auth/logout:
    post:
      tags:
        - Autenticación
      summary: Cierre de sesión
      description: Cierra la sesión del usuario autenticado
      responses:
        '200':
          description: Sesión cerrada correctamente
        '401':
          description: No autorizado

  /auth/me:
    get:
      tags:
        - Autenticación
      summary: Información del usuario autenticado
      description: Recupera la información del usuario actualmente autenticado
      responses:
        '200':
          description: Información del usuario
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserInfo'
        '401':
          description: No autorizado

  # Gestión de usuarios y roles
  /usuarios:
    get:
      tags:
        - Usuarios
      summary: Listado de usuarios
      description: Obtiene la lista de usuarios con filtros opcionales
      parameters:
        - name: rol
          in: query
          description: Filtrar por rol
          schema:
            type: string
        - name: estado
          in: query
          description: Filtrar por estado
          schema:
            type: string
            enum: [activo, inactivo, bloqueado]
        - name: page
          in: query
          description: Número de página
          schema:
            type: integer
            default: 0
        - name: size
          in: query
          description: Tamaño de página
          schema:
            type: integer
            default: 20
      responses:
        '200':
          description: Lista de usuarios
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PagedUsers'
        '403':
          description: Sin permisos para acceder

    post:
      tags:
        - Usuarios
      summary: Alta de usuario
      description: Crea un nuevo usuario (solo administradores)
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUser'
      responses:
        '201':
          description: Usuario creado correctamente
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '400':
          description: Datos inválidos
        '403':
          description: Sin permisos para crear usuarios
        '409':
          description: El usuario ya existe

  /usuarios/{id}:
    get:
      tags:
        - Usuarios
      summary: Consulta de usuario
      description: Obtiene la información de un usuario específico
      parameters:
        - name: id
          in: path
          required: true
          description: ID del usuario
          schema:
            type: integer
      responses:
        '200':
          description: Usuario encontrado
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '404':
          description: Usuario no encontrado
        '403':
          description: Sin permisos para acceder

    put:
      tags:
        - Usuarios
      summary: Modificación de usuario
      description: Actualiza la información de un usuario
      parameters:
        - name: id
          in: path
          required: true
          description: ID del usuario
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUser'
      responses:
        '200':
          description: Usuario actualizado correctamente
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '400':
          description: Datos inválidos
        '404':
          description: Usuario no encontrado
        '403':
          description: Sin permisos para modificar

    delete:
      tags:
        - Usuarios
      summary: Baja de usuario
      description: Elimina un usuario del sistema
      parameters:
        - name: id
          in: path
          required: true
          description: ID del usuario
          schema:
            type: integer
      responses:
        '204':
          description: Usuario eliminado correctamente
        '404':
          description: Usuario no encontrado
        '403':
          description: Sin permisos para eliminar

  /roles:
    get:
      tags:
        - Usuarios
      summary: Listado de roles
      description: Obtiene la lista de roles disponibles en el sistema
      responses:
        '200':
          description: Lista de roles
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Role'

  # Representantes y relaciones
  /representantes:
    get:
      tags:
        - Representantes
      summary: Obtener lista de representantes
      description: Obtiene la lista de representantes con filtros opcionales
      parameters:
        - name: circunscripcion
          in: query
          description: Filtrar por circunscripción
          schema:
            type: string
        - name: page
          in: query
          description: Número de página
          schema:
            type: integer
            default: 0
        - name: size
          in: query
          description: Tamaño de página
          schema:
            type: integer
            default: 20
      responses:
        '200':
          description: Lista de representantes
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PagedRepresentantes'

    post:
      tags:
        - Representantes
      summary: Crear nuevo representante
      description: Crea un nuevo representante en el sistema
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateRepresentante'
      responses:
        '201':
          description: Representante creado correctamente
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Representante'
        '400':
          description: Datos inválidos
        '403':
          description: Sin permisos para crear representantes

  /representantes/{id}:
    get:
      tags:
        - Representantes
      summary: Obtener representante por ID
      description: Obtiene la información detallada de un representante
      parameters:
        - name: id
          in: path
          required: true
          description: ID del representante
          schema:
            type: integer
      responses:
        '200':
          description: Representante encontrado correctamente
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Representante'
        '404':
          description: Representante no encontrado

    put:
      tags:
        - Representantes
      summary: Actualizar representante por ID
      description: Actualiza la información de un representante
      parameters:
        - name: id
          in: path
          required: true
          description: ID del representante
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateRepresentante'
      responses:
        '200':
          description: Representante actualizado correctamente
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Representante'
        '400':
          description: Datos inválidos
        '404':
          description: Representante no encontrado
        '403':
          description: Sin permisos para modificar

    delete:
      tags:
        - Representantes
      summary: Eliminar representante por id
      description: Elimina un representante del sistema
      parameters:
        - name: id
          in: path
          required: true
          description: ID del representante
          schema:
            type: integer
      responses:
        '204':
          description: Representante eliminado correctamente
        '404':
          description: Representante no encontrado
        '403':
          description: Sin permisos para eliminar

  /representantes/{id}/candidatos:
    get:
      tags:
        - Representantes
      summary: Candidatos asociados a un representante
      description: Obtiene la lista de candidatos asociados a un representante específico
      parameters:
        - name: id
          in: path
          required: true
          description: ID del representante
          schema:
            type: integer
        - name: page
          in: query
          description: Número de página
          schema:
            type: integer
            default: 0
        - name: size
          in: query
          description: Tamaño de página
          schema:
            type: integer
            default: 20
      responses:
        '200':
          description: Lista de candidatos del representante
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PagedCandidatos'
        '404':
          description: Representante no encontrado

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
    ClaveAuth:
      type: oauth2
      flows:
        authorizationCode:
          authorizationUrl: https://clave.gob.es/oauth/authorize
          tokenUrl: https://clave.gob.es/oauth/token
          scopes:
            openid: Identificación del usuario
    CertificateAuth:
      type: http
      scheme: bearer
      description: Autenticación mediante certificado digital

  schemas:
    # Esquemas de autenticación
    LoginCredentials:
      type: object
      required:
        - username
        - password
      properties:
        username:
          type: string
          description: Nombre de usuario
        password:
          type: string
          format: password
          description: Contraseña del usuario
        tipoLogin:
          type: string
          enum: [credentials]
          default: credentials

    ClaveLogin:
      type: object
      required:
        - tipoLogin
      properties:
        tipoLogin:
          type: string
          enum: [clave]
        redirectUrl:
          type: string
          format: uri
          description: URL de redirección tras autenticación

    CertificateLogin:
      type: object
      required:
        - tipoLogin
        - certificate
      properties:
        tipoLogin:
          type: string
          enum: [certificate]
        certificate:
          type: string
          description: Certificado digital en formato base64

    AuthResponse:
      type: object
      properties:
        token:
          type: string
          description: Token JWT de autenticación
        refreshToken:
          type: string
          description: Token para renovar la sesión
        expiresIn:
          type: integer
          description: Tiempo de expiración en segundos
        user:
          $ref: '#/components/schemas/UserInfo'

    UserInfo:
      type: object
      properties:
        id:
          type: integer
        username:
          type: string
        email:
          type: string
          format: email
        nombre:
          type: string
        apellido1:
          type: string
          description: Primer apellido del usuario
        apellido2:
          type: string
          description: Segundo apellido del usuario
        nombre_completo:
          type: string
          description: Campo computado que combina nombre + apellido1 + apellido2
          readOnly: true
        nif:
          type: string
          description: Número de identificación fiscal
        roles:
          type: array
          items:
            $ref: '#/components/schemas/Role'
        estado:
          type: string
          enum: [activo, inactivo, bloqueado]
        ultimoAcceso:
          type: string
          format: date-time

    # Esquemas de usuarios
    User:
      type: object
      properties:
        id:
          type: integer
        username:
          type: string
        email:
          type: string
          format: email
        email_verified:
          type: boolean
          description: Indica si el email ha sido verificado en Keycloak
        nombre:
          type: string
        apellido1:
          type: string
          description: Primer apellido del usuario
        apellido2:
          type: string
          description: Segundo apellido del usuario
        nombre_completo:
          type: string
          description: Campo computado que combina nombre + apellido1 + apellido2
          readOnly: true
        telefono:
          type: string
        roles:
          type: array
          items:
            $ref: '#/components/schemas/Role'
        estado:
          type: string
          enum: [activo, inactivo, bloqueado]
        fechaCreacion:
          type: string
          format: date-time
        ultimoAcceso:
          type: string
          format: date-time

    CreateUser:
      type: object
      required:
        - username
        - email
        - nombre
        - apellido1
        - password
      properties:
        username:
          type: string
          minLength: 3
          maxLength: 50
        email:
          type: string
          format: email
        nombre:
          type: string
          minLength: 1
          maxLength: 100
        apellido1:
          type: string
          minLength: 1
          maxLength: 100
          description: Primer apellido del usuario
        apellido2:
          type: string
          maxLength: 100
          description: Segundo apellido del usuario (opcional)
        telefono:
          type: string
        password:
          type: string
          format: password
          minLength: 8
        roles:
          type: array
          items:
            type: integer
          description: IDs de los roles a asignar

    UpdateUser:
      type: object
      properties:
        email:
          type: string
          format: email
        nombre:
          type: string
          minLength: 1
          maxLength: 100
        apellido1:
          type: string
          minLength: 1
          maxLength: 100
          description: Primer apellido del usuario
        apellido2:
          type: string
          maxLength: 100
          description: Segundo apellido del usuario
        telefono:
          type: string
        roles:
          type: array
          items:
            type: integer
        estado:
          type: string
          enum: [activo, inactivo, bloqueado]

    Role:
      type: object
      properties:
        id:
          type: integer
        nombre:
          type: string
        descripcion:
          type: string
        permisos:
          type: array
          items:
            type: string

    PagedUsers:
      type: object
      properties:
        content:
          type: array
          items:
            $ref: '#/components/schemas/User'
        totalElements:
          type: integer
        totalPages:
          type: integer
        size:
          type: integer
        number:
          type: integer
        first:
          type: boolean
        last:
          type: boolean

    # Esquemas de representantes
    Representante:
      type: object
      properties:
        id:
          type: integer
        nombre:
          type: string
        apellidos:
          type: string
        email:
          type: string
          format: email
        telefono:
          type: string
        circunscripcion:
          type: string
        partido:
          type: string
        cargo:
          type: string
        fechaAlta:
          type: string
          format: date-time
        estado:
          type: string
          enum: [activo, inactivo]

    CreateRepresentante:
      type: object
      required:
        - nombre
        - apellidos
        - email
        - circunscripcion
      properties:
        nombre:
          type: string
          minLength: 1
          maxLength: 100
        apellidos:
          type: string
          minLength: 1
          maxLength: 100
        email:
          type: string
          format: email
        telefono:
          type: string
        circunscripcion:
          type: string
          minLength: 1
          maxLength: 100
        partido:
          type: string
        cargo:
          type: string

    UpdateRepresentante:
      type: object
      properties:
        nombre:
          type: string
          minLength: 1
          maxLength: 100
        apellidos:
          type: string
          minLength: 1
          maxLength: 100
        email:
          type: string
          format: email
        telefono:
          type: string
        circunscripcion:
          type: string
          minLength: 1
          maxLength: 100
        partido:
          type: string
        cargo:
          type: string
        estado:
          type: string
          enum: [activo, inactivo]

    PagedRepresentantes:
      type: object
      properties:
        content:
          type: array
          items:
            $ref: '#/components/schemas/Representante'
        totalElements:
          type: integer
        totalPages:
          type: integer
        size:
          type: integer
        number:
          type: integer
        first:
          type: boolean
        last:
          type: boolean

    # Esquemas de candidatos
    Candidato:
      type: object
      properties:
        id:
          type: integer
        nombre:
          type: string
        apellidos:
          type: string
        email:
          type: string
          format: email
        telefono:
          type: string
        dni:
          type: string
        fechaNacimiento:
          type: string
          format: date
        representanteId:
          type: integer
        estado:
          type: string
          enum: [activo, inactivo, pendiente]
        fechaAlta:
          type: string
          format: date-time

    PagedCandidatos:
      type: object
      properties:
        content:
          type: array
          items:
            $ref: '#/components/schemas/Candidato'
        totalElements:
          type: integer
        totalPages:
          type: integer
        size:
          type: integer
        number:
          type: integer
        first:
          type: boolean
        last:
          type: boolean

    # Esquemas de error
    Error:
      type: object
      properties:
        codigo:
          type: string
        mensaje:
          type: string
        detalles:
          type: string
        timestamp:
          type: string
          format: date-time
