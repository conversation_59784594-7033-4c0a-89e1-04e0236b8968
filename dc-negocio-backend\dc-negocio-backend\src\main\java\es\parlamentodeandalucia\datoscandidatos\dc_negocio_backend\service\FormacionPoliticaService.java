package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.FormacionPoliticaEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository.FormacionPoliticaRepository;
import es.parlamentodeandalucia.datoscandidatos.dto.FormacionPolitica;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.Objects;

@Service
@Transactional
public class FormacionPoliticaService {

    @Autowired
    private FormacionPoliticaRepository repository;

    public List<FormacionPolitica> listarTodas() {
        return repository.findAll().stream()
                .map(this::convertirADto)
                .collect(Collectors.toList());
    }

    public Optional<FormacionPolitica> buscarPorId(Long id) {
        return repository.findById(id).map(this::convertirADto);
    }

    public Optional<FormacionPoliticaEntity> findById(Long id) {
        return repository.findById(id);
    }

    public Optional<FormacionPolitica> buscarPorNombre(String nombre) {
        return repository.findByNombre(nombre).map(this::convertirADto);
    }

    public FormacionPolitica guardar(FormacionPolitica dto) {
        // Validación para asegurar que el valor de dac_bl_activa no es nulo
        Boolean dacBlActiva = dto.getActiva() != null ? dto.getActiva() : false;

        // Generar codigoInterno si no se proporciona
        if (dto.getCodigoInterno() == null || dto.getCodigoInterno().trim().isEmpty()) {
            int ultimoCodigo = repository.findAll().stream()
                .map(FormacionPoliticaEntity::getCodigoInterno)
                .filter(Objects::nonNull)
                .mapToInt(code -> {
                    try {
                        return Integer.parseInt(code);
                    } catch (NumberFormatException e) {
                        return 0; // Si el código no es un número, se ignora
                    }
                })
                .max()
                .orElse(0); // Valor inicial si no hay códigos numéricos

            int siguienteCodigo = ultimoCodigo + 1;

            String nuevoCodigo = String.valueOf(siguienteCodigo);
            dto.setCodigoInterno(nuevoCodigo);
        }

        FormacionPoliticaEntity entity = new FormacionPoliticaEntity(
                dto.getId(),
                dto.getNombre(),
                dto.getSiglas(),
                dto.getCodigoInterno(),
                dacBlActiva
        );

        FormacionPoliticaEntity guardada = repository.save(entity);
        return convertirADto(guardada);
    }

    public Optional<FormacionPolitica> actualizar(Long id, FormacionPolitica dto) {
        return repository.findById(id).map(entity -> {
            entity.setNombre(dto.getNombre());
            entity.setSiglas(dto.getSiglas());
            entity.setCodigoInterno(dto.getCodigoInterno());
            entity.setActiva(dto.getActiva());
            FormacionPoliticaEntity actualizada = repository.save(entity);
            return convertirADto(actualizada);
        });
    }

    public void eliminarPorId(Long id) {
        repository.deleteById(id);
    }

    private FormacionPolitica convertirADto(FormacionPoliticaEntity entity) {
        FormacionPolitica dto = new FormacionPolitica();
        dto.setId(entity.getId());
        dto.setNombre(entity.getNombre());
        dto.setSiglas(entity.getSiglas());
        dto.setCodigoInterno(entity.getCodigoInterno());
        dto.activa(entity.getActiva());
        return dto;
    }
}