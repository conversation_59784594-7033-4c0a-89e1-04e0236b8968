spring.application.name=dc-negocio-backend

# Configuración del servidor
server.port=8080
server.servlet.context-path=/

# Configuración de SpringDoc OpenAPI
# springdoc.api-docs.path=/v3/api-docs
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method
springdoc.swagger-ui.tagsSorter=alpha
springdoc.swagger-ui.tryItOutEnabled=true
springdoc.swagger-ui.filter=true
springdoc.swagger-ui.url=/swagger.yaml


# Configuración de CORS para desarrollo
spring.web.cors.allowed-origins=http://localhost:4200,http://localhost:3000,https://***************:8007
spring.web.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
spring.web.cors.allowed-headers=*
spring.web.cors.allow-credentials=true

spring.datasource.url=******************************************************
spring.datasource.username=postgres
spring.datasource.password=postgres
spring.datasource.driver-class-name=org.postgresql.Driver

# Opcional: Configuración para Hibernate/JPA
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.properties.hibernate.use_sql_comments=false

# Configuración de logging detallado
logging.level.es.parlamentodeandalucia.datoscandidatos=INFO
logging.level.org.hibernate.SQL=WARN
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=WARN

logging.level.org.springframework.web=WARN


# Configuración de Keycloak OAuth2 Resource Server (automática)
spring.security.oauth2.resourceserver.jwt.issuer-uri=http://***************:8453/realms/parlamento
spring.security.oauth2.resourceserver.jwt.jwk-set-uri=http://***************:8453/realms/parlamento/protocol/openid-connect/certs

# Configuración adicional de JWT
spring.security.oauth2.resourceserver.jwt.jws-algorithm=RS256

# Cache para JWT
spring.security.oauth2.resourceserver.jwt.cache-duration=PT5M

# Configuración de Keycloak Client (opcional para client credentials)
keycloak.realm=parlamento
keycloak.auth-server-url=https://***************:8453
keycloak.resource=datosCandidatos
keycloak.public-client=true

# Deshabilitar métricas OTLP para desarrollo
management.otlp.metrics.export.enabled=false

# Configuración de logs de importación CSV
app.csv.logs.directory=${user.home}/Documents/ImportacionesCSV
app.csv.logs.enabled=true
app.csv.logs.max-files=100
# Logging para debugging de JWT
logging.level.org.springframework.security=WARN
logging.level.org.springframework.security.oauth2=WARN
logging.level.org.springframework.web.cors=WARN