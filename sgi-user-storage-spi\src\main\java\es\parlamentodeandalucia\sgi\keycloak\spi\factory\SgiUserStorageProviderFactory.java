package es.parlamentodeandalucia.sgi.keycloak.spi.factory;

import es.parlamentodeandalucia.sgi.keycloak.spi.provider.SgiUserStorageProvider;
import jakarta.persistence.EntityManager;
import org.keycloak.component.ComponentModel;
import org.keycloak.component.ComponentValidationException;
import org.keycloak.connections.jpa.JpaConnectionProvider;
import org.keycloak.models.KeycloakSession;
import org.keycloak.models.RealmModel;
import org.keycloak.provider.ProviderConfigProperty;
import org.keycloak.provider.ProviderConfigurationBuilder;
import org.keycloak.storage.UserStorageProviderFactory;

import java.util.List;

/**
 * Factory para el SGI User Storage Provider
 * Adaptado del proyecto base keycloak-user-spi
 */
public class SgiUserStorageProviderFactory implements UserStorageProviderFactory<SgiUserStorageProvider> {

    public static final String PROVIDER_ID = "sgi-user-storage";

    protected static final List<ProviderConfigProperty> configProperties;

    public static final String PERSISTENCE_UNIT_PROPERTY = "persistence-unit-name";

    protected static final String PERSISTENCE_UNIT_LABEL = "Persistence Unit Name";

    protected static final String PERSISTENCE_UNIT_PROPERTY_HELP_TEXT =
        "The persistence unit name is used to specify the configuration details for acquiring an entity manager. " +
        "This will be needed to get an entity manager / session per Keycloak transaction in your user provider implementation. " +
        "This name corresponds to the <persistence-unit> element in the persistence.xml file, which should be located " +
        "in the META-INF directory of your provider.";

    static {
        configProperties = ProviderConfigurationBuilder.create()
                .property()
                .name(PERSISTENCE_UNIT_PROPERTY)
                .type(ProviderConfigProperty.STRING_TYPE)
                .label(PERSISTENCE_UNIT_LABEL)
                .helpText(PERSISTENCE_UNIT_PROPERTY_HELP_TEXT)
                .defaultValue("sgi-user-store")
                .required(true)
                .add()
                .build();
    }

    @Override
    public SgiUserStorageProvider create(KeycloakSession keycloakSession, ComponentModel componentModel) {
        return new SgiUserStorageProvider(
                keycloakSession,
                componentModel,
                getEntityManager(keycloakSession, componentModel)
        );
    }

    @Override
    public List<ProviderConfigProperty> getConfigProperties() {
        return configProperties;
    }

    /**
     * Called before a component is created or updated.  Allows you to validate the configuration
     *
     * @throws ComponentValidationException
     */
    @Override
    public void validateConfiguration(KeycloakSession session, RealmModel realm, ComponentModel config)
            throws ComponentValidationException {
        validatePersistenceUnitName(session, config);
    }

    @Override
    public String getId() {
        return PROVIDER_ID;
    }

    protected void validatePersistenceUnitName(KeycloakSession session, ComponentModel config) {
        if (!session.listProviderIds(JpaConnectionProvider.class).contains(config.get(PERSISTENCE_UNIT_PROPERTY))) {
            throw new ComponentValidationException("Failed to find JpaConnectionProvider for " +
                    "persistence unit: " + config.get(PERSISTENCE_UNIT_PROPERTY));
        }
    }

    protected EntityManager getEntityManager(KeycloakSession keycloakSession, ComponentModel config) {
        return keycloakSession
                .getProvider(JpaConnectionProvider.class, config.get(PERSISTENCE_UNIT_PROPERTY))
                .getEntityManager();
    }

}
