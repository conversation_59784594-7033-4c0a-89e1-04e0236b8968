# Multi-stage build optimizado para Angular 19 con SSR
# Etapa 1: Build de la aplicación Angular 19
FROM node:18-alpine AS builder

# Establecer directorio de trabajo
WORKDIR /app

# Copiar archivos de configuración de dependencias
COPY package*.json ./

# Instalar todas las dependencias (incluyendo devDependencies para el build)
RUN npm ci --silent

# Copiar código fuente
COPY . .

# Build de la aplicación Angular 19 para producción
# Angular 19 genera tanto browser como server builds
RUN npm run build --configuration=production

# Etapa 2: Servidor de producción con Nginx para Angular 19
FROM nginx:alpine AS production

# Instalar curl para health checks
# Instalar openssl para generar certificados auto-firmados
RUN apk add --no-cache curl openssl

# Crear directorio para logs
RUN mkdir -p /var/log/nginx

# Crear el directorio para los certificados de Nginx
RUN mkdir -p /etc/nginx/certs/

# Copiar el script de generación de certificados a una ubicación estándar
COPY generate_certs.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/generate_certs.sh

# --- ¡LÍNEA TEMPORAL PARA DEPURACIÓN! ---
RUN ls -l /usr/local/bin/
# --- FIN DE LÍNEA TEMPORAL ---

# Copiar configuración personalizada de Nginx optimizada para Angular 19
# Esta configuración ahora incluirá la parte de HTTPS
COPY nginx.conf /etc/nginx/nginx.conf

# Copiar los archivos construidos desde la etapa anterior
# Angular 19 genera la carpeta browser dentro de dist
COPY --from=builder /app/dist/dc-frontend/browser /usr/share/nginx/html

# Exponer el puerto 80 (HTTP) y el puerto 443 (HTTPS)
# Aunque en docker-compose solo mapearemos uno hacia el 8007
EXPOSE 80 443

# Health check optimizado para Portainer
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:80/health || exit 1

# Comando para iniciar Nginx
CMD ["/bin/sh", "-c", "/usr/local/bin/generate_certs.sh && nginx -g 'daemon off;'"]