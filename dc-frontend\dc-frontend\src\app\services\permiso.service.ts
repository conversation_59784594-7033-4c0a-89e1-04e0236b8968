import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';
import { Funcionalidad } from './funcionalidad.service';

export interface Permiso {
  id: number;
  nombre: string;
  activo: boolean;
  funcionalidad: Funcionalidad;
}


@Injectable({
  providedIn: 'root'
})

export class PermisoService {

  private readonly apiUrl = environment.apiUrl + '/permisos';

  constructor(private http: HttpClient) {}

  getAll(): Observable<Permiso[]> {
    return this.http.get<Permiso[]>(this.apiUrl);
  }

  getById(id: number): Observable<Permiso> {
    return this.http.get<Permiso>(`${this.apiUrl}/${id}`);
  }

  create(permiso: Permiso): Observable<Permiso> {
    return this.http.post<Permiso>(this.apiUrl, permiso);
  }

  update(id: number, permiso: Permiso): Observable<Permiso> {
    return this.http.put<Permiso>(`${this.apiUrl}/${id}`, permiso);
  }

  delete(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }

  toggleActivo(id: number, activo: boolean): Observable<any> {
    const url = `${this.apiUrl}/${id}/activo?activo=${activo}`;
    console.log(url);
    return this.http.put<any>(url, null);
  }
}
