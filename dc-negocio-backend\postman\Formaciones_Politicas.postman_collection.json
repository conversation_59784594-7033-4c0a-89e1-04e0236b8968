{"info": {"_postman_id": "fp-test-collection-001", "name": "Formaciones Políticas - API Tests", "description": "Colección para probar todos los endpoints de Formaciones Políticas", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "1. GET - Listar todas las formaciones políticas", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response is an array\", function () {", "    pm.expect(pm.response.json()).to.be.an('array');", "});", "", "pm.test(\"Response time is less than 2000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/formaciones-politicas", "host": ["{{base_url}}"], "path": ["formaciones-politicas"]}}, "response": []}, {"name": "2. POST - Crear nueva formación política", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"Response has id field\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('id');", "    pm.expect(jsonData.id).to.be.a('number');", "    ", "    // Guardar el ID para usar en otros tests", "    pm.environment.set('created_formacion_id', jsonData.id);", "});", "", "pm.test(\"Response has correct nombre\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.nombre).to.eql('Partido de Prueba');", "    pm.expect(jsonData.siglas).to.eql('PP');", "});", "", "pm.test(\"Response time is less than 3000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(3000);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"nombre\": \"Partido de Prueba\",\n    \"siglas\": \"PP\",\n    \"codigoInterno\": \"PRUEBA001\"\n}"}, "url": {"raw": "{{base_url}}/formaciones-politicas", "host": ["{{base_url}}"], "path": ["formaciones-politicas"]}}, "response": []}, {"name": "3. GET - Obtener formación política por ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has correct structure\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('id');", "    pm.expect(jsonData).to.have.property('nombre');", "    pm.expect(jsonData).to.have.property('siglas');", "    pm.expect(jsonData).to.have.property('codigoInterno');", "});", "", "pm.test(\"Response time is less than 2000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/formaciones-politicas/{{created_formacion_id}}", "host": ["{{base_url}}"], "path": ["formaciones-politicas", "{{created_formacion_id}}"]}}, "response": []}, {"name": "4. PUT - Actualizar formación política", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has updated nombre\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.nombre).to.eql('Partido de Prueba Actualizado');", "});", "", "pm.test(\"Response time is less than 3000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(3000);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"nombre\": \"Partido de Prueba Actualizado\",\n    \"siglas\": \"PPA\",\n    \"codigoInterno\": \"PRUEBA002\"\n}"}, "url": {"raw": "{{base_url}}/formaciones-politicas/{{created_formacion_id}}", "host": ["{{base_url}}"], "path": ["formaciones-politicas", "{{created_formacion_id}}"]}}, "response": []}, {"name": "5. DELETE - Eliminar formación política", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204 or 200\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 204]);", "});", "", "pm.test(\"Response time is less than 2000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/formaciones-politicas/{{created_formacion_id}}", "host": ["{{base_url}}"], "path": ["formaciones-politicas", "{{created_formacion_id}}"]}}, "response": []}, {"name": "6. GET - Verificar eliminación (debe devolver 404)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {", "    pm.response.to.have.status(404);", "});", "", "pm.test(\"Response time is less than 2000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/formaciones-politicas/{{created_formacion_id}}", "host": ["{{base_url}}"], "path": ["formaciones-politicas", "{{created_formacion_id}}"]}}, "response": []}, {"name": "7. POST - Error: <PERSON><PERSON> (sin nombre)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400\", function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test(\"Response time is less than 2000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"siglas\": \"SN\",\n    \"codigoInterno\": \"SIN_NOMBRE\"\n}"}, "url": {"raw": "{{base_url}}/formaciones-politicas", "host": ["{{base_url}}"], "path": ["formaciones-politicas"]}}, "response": []}, {"name": "8. GET - Error: ID inexistente", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {", "    pm.response.to.have.status(404);", "});", "", "pm.test(\"Response time is less than 2000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/formaciones-politicas/99999", "host": ["{{base_url}}"], "path": ["formaciones-politicas", "99999"]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Script que se ejecuta antes de cada request", "console.log('<PERSON><PERSON><PERSON><PERSON><PERSON> request: ' + pm.info.requestName);"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Script que se ejecuta después de cada request", "console.log('Request completado: ' + pm.info.requestName);"]}}], "variable": [{"key": "base_url", "value": "http://localhost:8080/api", "type": "string"}]}