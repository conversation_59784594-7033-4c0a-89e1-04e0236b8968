package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.controller;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.CandidatoEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service.CandidatoService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CandidatoControllerTest {

    @Mock
    private CandidatoService candidatoService;

    @InjectMocks
    private CandidatoController candidatoController;

    private CandidatoEntity candidato1;
    private CandidatoEntity candidato2;
    private List<CandidatoEntity> candidatos;

    @BeforeEach
    void setUp() {
        candidato1 = new CandidatoEntity();
        candidato1.setId(1L);
        candidato1.setNombre("Pedro");
        candidato1.setApellido1("Perez");
        candidato1.setApellido2("Garcia");

        candidato2 = new CandidatoEntity();
        candidato2.setId(2L);
        candidato2.setNombre("Ana");
        candidato2.setApellido1("Martinez");
        candidato2.setApellido2("Lopez");

        candidatos = Arrays.asList(candidato1, candidato2);
    }

    @Test
    void testGetAll_SinParametros_DeberiaRetornarTodosLosCandidatos() {
        // Given
        when(candidatoService.findAll()).thenReturn(candidatos);

        // When
        ResponseEntity<?> response = candidatoController.getAll(null, null, null);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getStatusCodeValue());
        
        @SuppressWarnings("unchecked")
        List<CandidatoEntity> resultado = (List<CandidatoEntity>) response.getBody();
        assertNotNull(resultado);
        assertEquals(2, resultado.size());
        assertEquals("Pedro", resultado.get(0).getNombre());
        assertEquals("Ana", resultado.get(1).getNombre());
    }

    @Test
    void testGetAll_ConParametrosPaginacion_DeberiaRetornarPaginado() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        Page<CandidatoEntity> candidatosPaginados = new PageImpl<>(candidatos, pageable, 2);
        when(candidatoService.findAll(any(Pageable.class))).thenReturn(candidatosPaginados);

        // When
        ResponseEntity<?> response = candidatoController.getAll(0, 10, null);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getStatusCodeValue());
        
        @SuppressWarnings("unchecked")
        Page<CandidatoEntity> resultado = (Page<CandidatoEntity>) response.getBody();
        assertNotNull(resultado);
        assertEquals(2, resultado.getContent().size());
        assertEquals(0, resultado.getNumber());
        assertEquals(10, resultado.getSize());
        assertEquals(2, resultado.getTotalElements());
    }

    @Test
    void testGetAll_SoloConPage_DeberiaUsarSizeDefault() {
        // Given
        Pageable pageable = PageRequest.of(1, 20); // size default = 20
        Page<CandidatoEntity> candidatosPaginados = new PageImpl<>(candidatos, pageable, 2);
        when(candidatoService.findAll(any(Pageable.class))).thenReturn(candidatosPaginados);

        // When
        ResponseEntity<?> response = candidatoController.getAll(1, null, null);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getStatusCodeValue());
        
        @SuppressWarnings("unchecked")
        Page<CandidatoEntity> resultado = (Page<CandidatoEntity>) response.getBody();
        assertNotNull(resultado);
        assertEquals(1, resultado.getNumber()); // página 1
        assertEquals(20, resultado.getSize()); // size default
    }

    @Test
    void testGetAll_SoloConSize_DeberiaUsarPageDefault() {
        // Given
        Pageable pageable = PageRequest.of(0, 5); // page default = 0
        Page<CandidatoEntity> candidatosPaginados = new PageImpl<>(candidatos, pageable, 2);
        when(candidatoService.findAll(any(Pageable.class))).thenReturn(candidatosPaginados);

        // When
        ResponseEntity<?> response = candidatoController.getAll(null, 5, null);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getStatusCodeValue());
        
        @SuppressWarnings("unchecked")
        Page<CandidatoEntity> resultado = (Page<CandidatoEntity>) response.getBody();
        assertNotNull(resultado);
        assertEquals(0, resultado.getNumber()); // page default
        assertEquals(5, resultado.getSize());
    }
}
