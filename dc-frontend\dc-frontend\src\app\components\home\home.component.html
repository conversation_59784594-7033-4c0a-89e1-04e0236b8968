<div class="home-layout">
    <app-header></app-header>

    <main class="main-content">
        <!-- ==== ESTADÍSTICAS ==== -->
        <section class="stats-cards">
            <div class="card-summary">
                <div class="card-icon"><i class="fas fa-user"></i></div>
                <div class="card-info">
                    <div class="card-title">Candidatos</div>
                    <div class="card-value">24</div>
                    <div class="card-growth">+2.5% último año</div>
                </div>
            </div>
            <div class="card-summary">
                <div class="card-icon"><i class="fas fa-user-tie"></i></div>
                <div class="card-info">
                    <div class="card-title">Representantes</div>
                    <div class="card-value">20</div>
                    <div class="card-growth negative">-10% último mes</div>
                </div>
            </div>
            <div class="card-summary">
                <div class="card-icon"><i class="fas fa-flag"></i></div>
                <div class="card-info">
                    <div class="card-title">Formaciones políticas</div>
                    <div class="card-value">6</div>
                    <div class="card-growth negative">-1% último año</div>
                </div>
            </div>
            <div class="card-summary">
                <div class="card-icon"><i class="fas fa-map-marked-alt"></i></div>
                <div class="card-info">
                    <div class="card-title">Circunscripciones</div>
                    <div class="card-value">89</div>
                    <div class="card-growth">+10% último mes</div>
                </div>
            </div>
            <div class="card-summary">
                <div class="card-icon"><i class="fas fa-users"></i></div>
                <div class="card-info">
                    <div class="card-title">Usuarios activos</div>
                    <div class="card-value">234</div>
                    <div class="card-growth">+4% último mes</div>
                </div>
            </div>
            <div class="card-summary">
                <div class="card-icon"><i class="fas fa-chart-pie"></i></div>
                <div class="card-info">
                    <div class="card-title">Introducido</div>
                    <div class="card-value">•••</div>
                    <div class="card-growth">+1% último mes</div>
                </div>
            </div>
        </section>

        <!-- ==== DASHBOARD PANELS ==== -->
        <section class="dashboard-panels">
            <!-- 1. Últimos registrados -->
            <div class="panel panel-ultimos">
                <div class="panel-header">
                    <h3>Últimos registrados</h3>
                    <button class="btn-panel">Ver todo</button>
                </div>
                <table class="table-simple">
                    <thead>
                        <tr>
                            <th>Nombre</th>
                            <th>Estado</th>
                            <th>Rol</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Juan Pérez</td>
                            <td><span class="status active">Activo</span></td>
                            <td><span class="tag">Candidato</span></td>
                        </tr>
                        <tr>
                            <td>Ana Gómez</td>
                            <td><span class="status inactive">Inactivo</span></td>
                            <td><span class="tag">Candidato</span></td>
                        </tr>
                        <!-- …más filas… -->
                    </tbody>
                </table>
            </div>

            <!-- 2. Parlamento -->
            <div class="panel panel-parlamento">
                <div class="panel-header">
                    <h3>Parlamento</h3>
                    <button class="btn-panel">Ver todo</button>
                </div>
                <p>Administra los correos electrónicos habilitados para el envío de las órdenes del día de los Consejos
                    y la Comisión.</p>
            </div>

            <!-- 3. Correos electrónicos -->
            <div class="panel panel-correos">
                <div class="panel-header">
                    <h3>Correos electrónicos</h3>
                    <button class="btn-panel">Ver todo</button>
                </div>
                <p>Gestiona los correos que pueden enviar las órdenes del día del Consejo de Gobierno y la Comisión
                    General de Viceconsejerías y Viceconsejeras.</p>
            </div>

            <!-- 4. Usuarios recientes -->
            <div class="panel panel-usuarios-recientes">
                <div class="panel-header">
                    <h3>Usuarios recientes</h3>
                    <button class="btn-panel">Ver todo</button>
                </div>
                <table class="table-simple">
                    <thead>
                        <tr>
                            <th>Nombre</th>
                            <th>Estado</th>
                            <th>Rol</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Lucía Díaz</td>
                            <td><span class="status active">Activo</span></td>
                            <td><span class="tag">Candidato</span></td>
                        </tr>
                        <tr>
                            <td>Carlos Ruiz</td>
                            <td><span class="status inactive">Inactivo</span></td>
                            <td><span class="tag">Candidato</span></td>
                        </tr>
                        <!-- …más filas… -->
                    </tbody>
                </table>
            </div>

            <!-- 5. Introducido (gráficas) -->
            <div class="panel panel-introducido">
                <div class="panel-header">
                    <h3>Introducido</h3>
                    <button class="btn-panel">Ver todo</button>
                </div>
                <div class="charts-grid">
                    <div class="chart-placeholder">[Gráfica 1]</div>
                    <div class="chart-placeholder">[Gráfica 2]</div>
                </div>
            </div>
        </section>
    </main>

    <app-footer></app-footer>
</div>