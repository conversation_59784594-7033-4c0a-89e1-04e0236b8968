package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.config;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service.AuditoriaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

@Configuration
public class SecurityAuditConfig {

    @Autowired
    private AuditoriaService auditoriaService;

    /**
     * Manejador para accesos denegados
     */
    @Bean
    public AccessDeniedHandler accessDeniedHandler() {
        return new AccessDeniedHandler() {
            @Override
            public void handle(HttpServletRequest request, HttpServletResponse response,
                             AccessDeniedException accessDeniedException) throws IOException, ServletException {
                
                Authentication auth = SecurityContextHolder.getContext().getAuthentication();
                String usuario = "ANONIMO";
                String ip = getClientIpAddress(request);
                String recurso = request.getRequestURI();
                String metodo = request.getMethod();
                
                if (auth != null && auth.getPrincipal() instanceof Jwt) {
                    Jwt jwt = (Jwt) auth.getPrincipal();
                    usuario = jwt.getClaimAsString("username");
                }
                
                // Registrar intento de acceso no autorizado
                auditoriaService.registrarAccesoNoAutorizado(usuario, metodo + " " + recurso, ip, 
                    "Acceso denegado: " + accessDeniedException.getMessage());
                
                response.setStatus(HttpServletResponse.SC_FORBIDDEN);
                response.setContentType("application/json");
                response.getWriter().write("{\"error\":\"Acceso denegado\",\"message\":\"No tiene permisos para acceder a este recurso\"}");
            }
        };
    }

    /**
     * Manejador para autenticación exitosa
     */
    @Bean
    public AuthenticationSuccessHandler authenticationSuccessHandler() {
        return new AuthenticationSuccessHandler() {
            @Override
            public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response,
                                              Authentication authentication) throws IOException, ServletException {
                
                if (authentication.getPrincipal() instanceof Jwt) {
                    Jwt jwt = (Jwt) authentication.getPrincipal();
                    String usuario = jwt.getClaimAsString("username");
                    String ip = getClientIpAddress(request);
                    String userAgent = request.getHeader("User-Agent");
                    
                    auditoriaService.registrarSesion("LOGIN", usuario, ip, userAgent);
                }
            }
        };
    }

    /**
     * Manejador para fallos de autenticación
     */
    @Bean
    public AuthenticationFailureHandler authenticationFailureHandler() {
        return new AuthenticationFailureHandler() {
            @Override
            public void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response,
                                              org.springframework.security.core.AuthenticationException exception) 
                                              throws IOException, ServletException {
                
                String ip = getClientIpAddress(request);
                String userAgent = request.getHeader("User-Agent");
                
                auditoriaService.registrarSesion("LOGIN_FAILED", "UNKNOWN", ip, 
                    userAgent + " - Error: " + exception.getMessage());
                
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                response.setContentType("application/json");
                response.getWriter().write("{\"error\":\"Autenticación fallida\",\"message\":\"Credenciales inválidas\"}");
            }
        };
    }

    /**
     * Obtiene la dirección IP del cliente
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedForHeader = request.getHeader("X-Forwarded-For");
        if (xForwardedForHeader == null) {
            return request.getRemoteAddr();
        } else {
            return xForwardedForHeader.split(",")[0];
        }
    }
}
