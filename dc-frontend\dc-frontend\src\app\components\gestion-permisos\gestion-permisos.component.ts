import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { HeaderComponent } from '../header/header.component';
import { FooterComponent } from '../footer/footer.component';

import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { TagModule } from 'primeng/tag';
import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { CheckboxModule } from 'primeng/checkbox';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ToastModule } from 'primeng/toast';
import { forkJoin } from 'rxjs';
import { ConfirmDialogModule } from 'primeng/confirmdialog';

import { PermisoService, Permiso } from '../../services/permiso.service';
import { FuncionalidadService, Funcionalidad } from '../../services/funcionalidad.service';

@Component({
  selector: 'app-gestion-permisos',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    HeaderComponent,
    FooterComponent,
    TableModule,
    ButtonModule,
    InputTextModule,
    TagModule,
    DialogModule,
    DropdownModule,
    CheckboxModule,
    ToastModule,
    ConfirmDialogModule,
  ],
  providers: [MessageService, ConfirmationService],
  templateUrl: './gestion-permisos.component.html',
  styleUrls: ['./gestion-permisos.component.scss']
})


export class GestionPermisosComponent implements OnInit {
  @ViewChild('dt') dt: any;

  permisos: Permiso[] = [];
  selectedPermiso: Permiso[] = [];
  selectedPermisoId: string | null = null;

  globalFilterValue = '';
  showModal = false;
  openedDropdownId: string | null = null;

  showMassiveActions = false;

  funcionalidades: Funcionalidad[] = [];

  estados = [
    { label: 'Activo', value: 'true' },
    { label: 'Inactivo', value: 'false' },
  ];

  permisoSeleccionado: Permiso | null = null;

  constructor(private permisoService: PermisoService, private funcionalidadService: FuncionalidadService,  private confirmationService: ConfirmationService, private messageService: MessageService) {}

  ngOnInit(): void {
    this.cargarPermisos();
    this.funcionalidadService.getAll().subscribe(data => {
    this.funcionalidades = data;
});

  }

  cargarPermisos(): void {
    this.permisoService.getAll().subscribe(data => {
      this.permisos = data;
    });
  }

  cambiarEstado(permiso: Permiso): void {
    const nuevoEstado = !permiso.activo;
    this.permisoService.toggleActivo(permiso.id, nuevoEstado).subscribe({
      next: (res) => {
        permiso.activo = res.activo;
      },
      error: (err) => {
        console.error('Error al cambiar el estado de la funcionalidad:', err);
      }
    });
  }

  eliminar(permiso: Permiso): void {
    this.confirmationService.confirm({
      message: `¿Estás seguro de que deseas eliminar el permiso "${permiso.nombre}"?`,
      header: 'Confirmar eliminación',
      icon: 'pi pi-exclamation-triangle',
      acceptLabel: 'Sí',
      rejectLabel: 'No',
      accept: () => {
        this.permisoService.delete(permiso.id).subscribe({
          next: () => {
            this.messageService.add({
              severity: 'success',
              summary: 'Eliminado',
              detail: 'Permiso eliminado correctamente'
            });
            this.cargarPermisos();
          },
          error: () => {
            this.messageService.add({
              severity: 'error',
              summary: 'Error',
              detail: 'No se pudo eliminar el permiso'
            });
          }
        });
      }
    });
  }


  abrirModalEdicion(permiso: Permiso): void {
    this.permisoSeleccionado = { ...permiso };
    this.showModal = true;
  }

abrirModalNueva(): void {
  this.permisoSeleccionado = {
    id: 0,
    nombre: '',
    activo: true,
    funcionalidad: {
      id: 0,
      nombre: '',
      activo: true
    }
  };
  this.showModal = true;
}


guardar(): void {
  if (!this.permisoSeleccionado) return;

  // Clonamos y le indicamos a TypeScript que ahora los campos son opcionales
  const permisoParaEnviar: Partial<Permiso> = { ...this.permisoSeleccionado };

  // Eliminar el id si es 0 o undefined (para creación)
  if (!permisoParaEnviar.id || permisoParaEnviar.id === 0) {
    delete permisoParaEnviar.id;
  }

  const esEdicion = !!this.permisoSeleccionado.id;

  const obs = esEdicion
    ? this.permisoService.update(this.permisoSeleccionado.id!, permisoParaEnviar as Permiso)
    : this.permisoService.create(permisoParaEnviar as Permiso);

    obs.subscribe({
      next: (respuesta) => {
        this.showModal = false;
        this.cargarPermisos();
        if (!esEdicion) {
          this.messageService.add({
            severity: 'success',
            summary: 'Éxito',
            detail: 'Permiso creado correctamente'
          });
        } else {
          this.messageService.add({
            severity: 'success',
            summary: 'Éxito',
            detail: 'Permiso actualizado correctamente'
          });
        }
      },
      error: (err) => {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'No se pudo guardar el permiso'
        });
      }
    });
  }

  cancelar(): void {
    this.permisoSeleccionado = null;
    this.showModal = false;
  }

  clearFilters(): void {
    this.globalFilterValue = '';
    this.dt.filterGlobal('', 'contains');
    this.dt.reset();
  }

  getStatusClass(activo: boolean): string {
    return activo ? 'status-active' : 'status-inactive';
  }

  toggleDropdown(selectedPermisoId: string): void {
    this.openedDropdownId = this.openedDropdownId === selectedPermisoId ? null : selectedPermisoId;
  }

  getRowStyle(permiso: Permiso, index: number): string {
    const isSelected = this.selectedPermiso.some(p => p.id === permiso.id);
    return isSelected ? 'p-highlight' : (index % 2 === 0 ? 'row-par' : 'row-impar');
  }

  activateAll(): void {
    const requests = this.selectedPermiso.map(permi => {
      permi.activo = true;
      return this.permisoService.toggleActivo(permi.id, true);
    });

    forkJoin(requests).subscribe({
      next: () => {
        this.messageService.add({
          severity: 'success',
          summary: 'Activación completa',
          detail: 'Todas los permisos seleccionados fueron activados correctamente.'
        });
        this.cargarPermisos(); // recarga si es necesario
      },
      error: () => {
        this.messageService.add({
          severity: 'error',
          summary: 'Error en activación',
          detail: 'Ocurrió un error al activar algunos permisos.'
        });
      }
  });
}

deactivateAll(): void {
  const requests = this.selectedPermiso.map(permi => {
    permi.activo = false;
    return this.permisoService.toggleActivo(permi.id, false);
  });

  forkJoin(requests).subscribe({
    next: () => {
        this.messageService.add({
          severity: 'success',
          summary: 'Desactivación completa',
          detail: 'Todas los permisos seleccionadaos fueron desactivados correctamente.'
        });
        this.cargarPermisos(); // recarga si es necesario
      },
      error: () => {
        this.messageService.add({
          severity: 'error',
          summary: 'Error en desactivación',
          detail: 'Ocurrió un error al desactivar algunos permisos.'
        });
      }
    });
  }

}