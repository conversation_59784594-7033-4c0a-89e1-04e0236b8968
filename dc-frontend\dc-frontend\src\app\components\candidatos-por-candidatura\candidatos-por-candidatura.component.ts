import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { HeaderComponent } from '../header/header.component';
import { FooterComponent } from '../footer/footer.component';
import { TableModule, Table } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { SelectModule } from 'primeng/select';
import { DialogModule } from 'primeng/dialog';
import { InputNumberModule } from 'primeng/inputnumber';
import { ToastModule } from 'primeng/toast';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { CheckboxModule } from 'primeng/checkbox';
import { InputTextModule } from 'primeng/inputtext';
import { MenuModule } from 'primeng/menu';
import { TagModule } from 'primeng/tag';
import { MessageService, ConfirmationService, MenuItem } from 'primeng/api';
import { CandidatosService, CandidatoEntity, CandidatoUpsert, TipoCandidato, EstadoCandidato } from '../../services/candidatos.service';
import { CandidaturasService, CandidaturaDto } from '../../services/candidaturas.service';

@Component({
  selector: 'app-candidatos-por-candidatura',
  standalone: true,
  imports: [
    CommonModule, RouterModule, FormsModule,
    HeaderComponent, FooterComponent,
    TableModule, ButtonModule, SelectModule, DialogModule, InputNumberModule, ToastModule,
    ConfirmDialogModule, CheckboxModule, InputTextModule, MenuModule, TagModule
  ],
  providers: [MessageService, ConfirmationService],
  templateUrl: './candidatos-por-candidatura.component.html',
  styleUrls: ['./candidatos-por-candidatura.component.scss']
})
export class CandidatosPorCandidaturaComponent implements OnInit {
  @ViewChild('dt') dt: Table | undefined;

  candidaturaId!: number;
  candidatura!: CandidaturaDto;

  candidatos: CandidatoEntity[] = [];
  loading = false;

  // selección y menús
  selected: CandidatoEntity[] = [];
  massItems: MenuItem[] = [];
  rowItems: MenuItem[] = [];
  currentRow: CandidatoEntity | null = null;
  // estado de menús para estilos
  isMassMenuOpen = false;
  openRowId: number | null = null;

  // modal crear/editar
  showDialog = false;
  isEditing = false;
  tipos: TipoCandidato[] = [];
  estados: EstadoCandidato[] = [];
  selectedTipoId: number | null = null;
  selectedEstadoId: number | null = null;

  nuevoCandidato: CandidatoUpsert = {
    nombre: '',
    apellido1: '',
    apellido2: '',
    orden: undefined,
    candidatura: { id: 0 }
  };

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private candidatosService: CandidatosService,
    private candidaturasService: CandidaturasService,
    private messageService: MessageService,
    private confirmationService: ConfirmationService
  ) {}

  ngOnInit(): void {
    this.candidaturaId = Number(this.route.snapshot.paramMap.get('id'));
    this.nuevoCandidato.candidatura.id = this.candidaturaId;
    if (this.candidaturaId) {
      this.loadHeader();
      this.loadDropdowns();
      this.loadData();
      this.massItems = [
        { label: 'Exportar CSV', icon: 'pi pi-upload', command: () => this.exportar('csv') },
        { label: 'Exportar PDF', icon: 'pi pi-file-pdf', command: () => this.exportar('pdf') }
      ];
    }
  }

  loadHeader(): void {
    // Usamos getAll() para garantizar DTO con nombres completos (evita problemas de lazy loading en backend)
    this.candidaturasService.getAll().subscribe({
      next: (lista) => {
        const found = lista.find(c => c.id === this.candidaturaId);
        if (found) this.candidatura = found;
      }
    });
  }

  loadDropdowns(): void {
    this.candidatosService.getTipos().subscribe({ next: d => {
      this.tipos = d;
      // Seleccionar por defecto TITULAR si existe
      const titular = d.find(x => (x.valor || '').toUpperCase() === 'TITULAR');
      this.selectedTipoId = titular ? titular.id : (d[0]?.id ?? null);
    }});
    this.candidatosService.getEstados().subscribe({ next: d => {
      this.estados = d;
      // Seleccionar por defecto IMPORTADO si existe
      const imp = d.find(x => (x.valor || '').toUpperCase() === 'IMPORTADO');
      this.selectedEstadoId = imp ? imp.id : (d[0]?.id ?? null);
    }});
  }

  loadData(): void {
    this.loading = true;
    this.candidatosService.getByCandidatura(this.candidaturaId).subscribe({
      next: d => { this.candidatos = d; this.loading = false; },
      error: () => { this.loading = false; this.messageService.add({severity:'error', summary:'Error', detail:'No se pudieron cargar los candidatos'}); }
    });
  }

  limpiarFiltros(): void { this.dt?.clear(); }
  goBack(): void { this.router.navigate(['/candidaturas']); }

  abrirModalCrear(): void {
    this.isEditing = false;
    this.nuevoCandidato = { nombre:'', apellido1:'', apellido2:'', orden: undefined, candidatura: { id: this.candidaturaId } };
    // asegurar selección por defecto
    if (!this.selectedTipoId && this.tipos.length) {
      const titular = this.tipos.find(x => (x.valor || '').toUpperCase() === 'TITULAR');
      this.selectedTipoId = titular ? titular.id : this.tipos[0].id;
    }
    if (!this.selectedEstadoId && this.estados.length) {
      const imp = this.estados.find(x => (x.valor || '').toUpperCase() === 'IMPORTADO');
      this.selectedEstadoId = imp ? imp.id : this.estados[0].id;
    }
    this.showDialog = true;
  }

  abrirModalEditar(row: CandidatoEntity): void {
    this.isEditing = true;
    this.currentRow = row;
    this.nuevoCandidato = {
      id: row.id,
      nombre: row.nombre,
      apellido1: row.apellido1,
      apellido2: row.apellido2,
      orden: row.orden,
      candidatura: { id: this.candidaturaId }
    };
    this.selectedTipoId = row.tipo?.id ?? this.selectedTipoId;
    this.selectedEstadoId = row.estado?.id ?? this.selectedEstadoId;
    this.showDialog = true;
  }

  guardar(): void {
    if (!this.nuevoCandidato.nombre || !this.nuevoCandidato.apellido1) {
      this.messageService.add({severity:'warn', summary:'Datos incompletos', detail:'Nombre y primer apellido son obligatorios'});
      return;
    }
    // Construir asociaciones opcionales segun selecciones
    const payload: CandidatoUpsert = {
      ...this.nuevoCandidato,
      tipo: this.selectedTipoId ? { id: this.selectedTipoId } : undefined,
      estado: this.selectedEstadoId ? { id: this.selectedEstadoId } : undefined,
    };

    const obs = this.isEditing && this.currentRow?.id
      ? this.candidatosService.update(this.currentRow.id, payload)
      : this.candidatosService.create(payload);

    obs.subscribe({
      next: () => { this.messageService.add({severity:'success', summary:'Guardado', detail:'Candidato guardado'}); this.showDialog = false; this.loadData(); },
      error: () => { this.messageService.add({severity:'error', summary:'Error', detail:'No se pudo guardar el candidato'}); }
    });
  }

  setCurrentRow(row: CandidatoEntity, menu?: any, event?: Event): void {
    this.currentRow = row;
    this.rowItems = [
      { label: 'Editar', icon: 'pi pi-pencil', command: () => this.abrirModalEditar(row) },
      { label: 'Eliminar', icon: 'pi pi-trash', command: () => this.eliminar(row) }
    ];
    if (menu && event) menu.toggle(event);
  }

  eliminar(row: CandidatoEntity): void {
    this.confirmationService.confirm({
      message: '¿Eliminar este candidato?', header: 'Confirmar', icon: 'pi pi-exclamation-triangle', acceptLabel: 'Eliminar', rejectLabel: 'Cancelar',
      acceptButtonStyleClass: 'p-button-danger',
      accept: () => {
        this.candidatosService.delete(row.id).subscribe({
          next: () => { this.messageService.add({severity:'success', summary:'Eliminado'}); this.loadData(); },
          error: () => this.messageService.add({severity:'error', summary:'Error al eliminar'})
        });
      }
    });
  }

  eliminarSeleccion(): void {
    if (!this.selected.length) { this.messageService.add({severity:'warn', summary:'Nada seleccionado'}); return; }
    this.confirmationService.confirm({
      message: '¿Eliminar los candidatos seleccionados?', header: 'Confirmar', icon: 'pi pi-exclamation-triangle', acceptLabel: 'Eliminar', rejectLabel: 'Cancelar',
      acceptButtonStyleClass: 'p-button-danger',
      accept: () => {
        const reqs = this.selected.map(r => this.candidatosService.delete(r.id));
        Promise.allSettled(reqs.map(o => o.toPromise())).then(() => { this.messageService.add({severity:'success', summary:'Proceso finalizado'}); this.selected = []; this.loadData(); });
      }
    });
  }



  exportar(formato: 'csv'|'pdf'): void {
    this.messageService.add({severity:'info', summary:`Exportar ${formato.toUpperCase()}`});
  }

  getEstadoSeverity(estado?: any): 'success' | 'warning' | 'info' {
    const v = (estado?.valor || estado || '').toString().toLowerCase();
    if (v.includes('valid')) return 'success';
    if (v.includes('import')) return 'warning';
    return 'info';
  }
}

