

::ng-deep {
  .p-column-filter-overlay {
    z-index: 1100 !important;
  }

  .p-progressbar {
    height: .5rem;
    background-color: #D8DADC;

    .p-progressbar-value {
      background-color: #607D8B;
    }
  }
}

.breadcrumbs {
  margin: 0 auto 20px auto;
  max-width: 1400px;
  width: calc(100% - 40px);
  font-size: 14px;
  color: #666;
  margin-top: 10px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.breadcrumbs .separator {
  margin: 0 5px;
  color: #999;
}

.breadcrumbs strong {
  color: #333;
  font-weight: 600;
}

.page-header {
  width: 100%;
  margin: 20px auto 0 auto;
  padding: 0 20px;
  box-sizing: border-box;
}

/* Filtros y acciones */
.filter-actions-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 10px 10px;
  flex-wrap: wrap;
  gap: 15px;
}

/* Dropdown */
.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-toggle {
  display: flex;
  align-items: center;
  gap: 5px;
}

.dropdown-menu {
  display: none;
  position: absolute;
  background-color: #fff;
  min-width: 160px;
  box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.2);
  z-index: 1;
  border-radius: 6px;
  overflow: auto;
  top: 100%;
  left: 0;
  margin-top: 5px;
}

.dropdown-item {
  color: #333;
  padding: 12px 16px;
  text-decoration: none;
  display: block;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.dropdown-item:hover {
  background-color: #f1f1f1;
}

.dropdown:hover .dropdown-menu {
  display: block;
}

/* --- ADAPTACIONES PARA LA TABLA DE PRIMENG --- */

/* Contenedor de la tabla */
.table-container {
  max-height: 500px;
  /* Mantener para el contenedor si la tabla es scrollable */
  margin-bottom: 20px;
}

// Sobreescribir estilos de PrimeNG para la tabla principal
// Sobreescribir estilos de PrimeNG para la tabla principal
:host ::ng-deep .p-datatable {
  width: 100%;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  overflow: auto;

  .p-datatable-wrapper {
    // ...
  }

  .p-datatable-table {
    table-layout: fixed;
  }

  /* Estilos para la cabecera (thead) */
  .p-datatable-thead>tr>th {
    background-color: #f5f5f5;
    padding: 12px 15px;
    /* Padding general por defecto */
    text-align: left;
    font-size: 14px;
    font-weight: 600;
    color: #555;
    white-space: nowrap;
    position: sticky;
    top: 0;
    z-index: 2;
    background-color: #f5f5f5;
    border-bottom: 1px solid #ddd;

    /* --- ESTE ES EL BLOQUE QUE DEBE ESTAR AQUÍ DENTRO DE ESTAS MISMAS LLAVES --- */
    position: relative;
    /* CRUCIAL: Necesario para que .column-filter-align se posicione respecto a este TH */
    padding-right: 40px;
    /* Deja espacio a la derecha del TH para el filtro. Ajusta este valor si es necesario. */
    padding-left: 15px;
    /* Para asegurar un padding uniforme a la izquierda si no está definido */


    &.align-right-header {
      /* Para la columna de acciones (última columna) */
      text-align: right;
      /* Alinea el contenido de la cabecera de acciones a la derecha */
      padding-right: 15px;
      /* Restablece el padding normal para la última columna */
      padding-left: 15px;
      /* Asegura padding uniforme */
    }

    .header-content-group-sortable {
      /* Para el div que contiene el texto y el icono de ordenar */
      display: inline-flex;
      /* Permite que ocupe solo el espacio de su contenido */
      align-items: center;
      /* Alinea verticalmente el texto y el icono */
      gap: 3px;
      /* Espacio entre el texto y el icono de ordenar. Ajusta a 0px para totalmente pegados. */
      cursor: pointer;
      /* Indica que es clicable para ordenar */
    }

    .column-filter-align {
      /* La clase que le pusiste a p-columnFilter en el HTML */
      position: relative;
      /* Saca el filtro del flujo normal */
      right: 0px;
      /* Distancia desde el borde derecho del TH. Ajusta para moverlo más/menos a la derecha. */
      top: 50%;
      /* Centra verticalmente */
      transform: translateY(-50%);
      /* Ajuste fino para el centrado vertical */
      z-index: 3;
      /* Asegura que esté por encima de otros elementos */
    }
  }

  // Estilo para el icono de ordenación de PrimeNG
  .p-sortable-column .p-sortable-column-icon {
    font-size: 10px;
    margin-left: 5px;
    color: #999;
  }

  /* Estilos para el cuerpo (tbody) */
  .p-datatable-tbody>tr {
    border-bottom: 1px solid #eee;
  }

  .p-datatable-tbody>tr:last-child {
    border-bottom: none;
  }

  .p-datatable-tbody>tr:hover {
    background-color: #f9f9f9;
  }

  /* Estilos para las celdas (td) */
  .p-datatable-tbody>tr>td {
    padding: 12px 15px;
    font-size: 14px;
    color: #444;
    white-space: nowrap;
    vertical-align: middle; // Asegura que el contenido esté centrado verticalmente
  }

  /* Estilos para las filas alternas (striped rows) */
  .p-datatable-tbody>tr.p-row-odd {
    background-color: #6b4646 !important;
    /* Un color ligeramente más oscuro para las filas impares */
  }

  .p-datatable-tbody>tr.p-row-even {
    background-color: #ffffff !important;
    /* Blanco para las filas pares */
  }

  /* Asegurarse de que el hover siga funcionando sobre los colores de las rayas */
  .p-datatable-tbody>tr.p-row-odd:hover,
  .p-datatable-tbody>tr.p-row-even:hover,
  .p-datatable-tbody>tr.p-highlight:hover {
    /* Añadido para el hover en filas seleccionadas */
    background-color: #f0f0f0 !important;
    /* Un color de hover que se vea bien sobre ambos */
  }


  /* Estilos para los Checkboxes de PrimeNG */
  .p-checkbox .p-checkbox-box {
    border: 1px solid #ccc;
    background-color: #fff;
    width: 18px;
    height: 18px;
    border-radius: 4px; // Bordes ligeramente redondeados para los checkboxes
  }

  .p-checkbox .p-checkbox-box.p-highlight {
    background-color: #007bff;
    border-color: #007bff;
  }

  .p-checkbox .p-checkbox-box .p-checkbox-icon {
    color: #fff;
    font-size: 12px;
  }

  /* Estilos para el filtro global (p-inputtext) */
  .p-inputtext {
    padding: 8px 12px;
    border: 1px solid #ccc;
    border-radius: 6px; // Un poco más redondeado
    font-size: 14px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    width: auto; // Asegura que no ocupe todo el ancho si no es necesario
  }

  .p-inputtext:focus {
    border-color: #007bff;
    outline: none;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  }

  /* Estilos para la paginación de PrimeNG (p-paginator) */
  .p-paginator {
    display: flex;
    justify-content: flex-end;
    /* Alinea a la derecha */
    align-items: center;
    gap: 5px;
    margin-top: 20px;
    padding: 10px 0; // Añade un poco de padding vertical
    background-color: transparent;
    border: none;
  }

  .p-paginator .p-paginator-pages .p-paginator-page {
    background-color: #f0f0f0;
    border: 1px solid #ddd;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    color: #555;
    transition: background-color 0.2s ease, color 0.2s ease;
  }

  .p-paginator .p-paginator-pages .p-paginator-page.p-highlight {
    background-color: #007bff;
    color: #fff;
    border-color: #007bff;
  }

  .p-paginator .p-paginator-pages .p-paginator-page:hover:not(.p-highlight) {
    background-color: #e0e0e0;
  }

  .p-paginator .p-paginator-first,
  .p-paginator .p-paginator-prev,
  .p-paginator .p-paginator-next,
  .p-paginator .p-paginator-last {
    background-color: #f0f0f0;
    border: 1px solid #ddd;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    color: #555;
    transition: background-color 0.2s ease, color 0.2s ease;
  }

  .p-paginator .p-paginator-first:hover,
  .p-paginator .p-paginator-prev:hover,
  .p-paginator .p-paginator-next:hover,
  .p-paginator .p-paginator-last:hover {
    background-color: #e0e0e0;
  }

  .p-paginator-icon {
    color: #555;
  }

  // Estilos para el "Rows per page dropdown" de PrimeNG
  .p-paginator .p-dropdown {
    min-width: 80px;

    .p-dropdown-label {
      font-size: 14px;
    }
  }

  .p-paginator .p-paginator-current {
    font-size: 14px;
    color: #555;
    margin-right: 10px; // Espacio entre el texto y el dropdown
  }
}


/* Resto de tus estilos que ya funcionan con tus clases personalizadas */
.circunscripciones-page-container {
  flex: 1;
  background-color: #fff;
  // border-radius: 8px; // Movido a .p-datatable
  // box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05); // Movido a .p-datatable
  padding: 16px;
  margin: 0 auto 20px auto;
  max-width: 1400px;
  display: flex;
  flex-direction: column;
}

/* Buttons */
.btn-primary,
.btn-text,
.btn-icon {
  padding: 10px 18px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.2s ease, box-shadow 0.2s ease;
}

.btn-primary {
  background-color: #C2E038;
  color: #282F19;
  border-width: 1px;
}


.btn-secondary {
  padding: 10px 18px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  transition: background-color 0.2s ease, box-shadow 0.2s ease;
}

.btn-secondary:hover {
  background-color: #d5d5d5;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn-text {
  background: none;
  color: #007bff;
  padding: 8px 12px;
}

.btn-text:hover {
  text-decoration: underline;
  background-color: rgba(0, 123, 255, 0.05);
}

.btn-icon {
  background: none;
  border: none;
  padding: 5px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
}

.btn-icon:hover {
  background-color: #f0f0f0;
}

.btn-icon svg {
  width: 20px;
  height: 20px;
}

.title-section {
  margin: 0 auto 20px auto;
  max-width: 1400px;
  width: calc(100% - 40px);
  display: flex;
  align-items: center;
  justify-content: left;
  flex-wrap: nowrap;
  width: 100%;
}

.title-section .back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  color: #333;
  text-decoration: none;
  padding: 0;
  flex-shrink: 0;
}

.title-section .back-button svg {
  width: 24px;
  height: 24px;
  color: #555;
}

.title-section .page-main-title {
  font-size: 28px;
  font-weight: 600;
  color: #333;
  margin-left: 5px;
  white-space: nowrap;
  flex-shrink: 0;
  text-align: left;
}

.title-section .header-actions {
  display: flex;
  gap: 10px;
  margin-left: auto;
  flex-wrap: nowrap;
  flex-shrink: 0;
  background-color: #C2E038;
  border-radius: 10px;
}

.breadcrumbs {
  margin: 0 auto 20px auto;
  max-width: 1400px;
  width: calc(100% - 40px);
  font-size: 14px;
  color: #666;
  margin-top: 10px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.breadcrumbs .separator {
  margin: 0 5px;
  color: #999;
}

.breadcrumbs strong {
  color: #333;
  font-weight: 600;
}

html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: 'Inter', sans-serif;
  overflow-y: auto;
  overflow-x: hidden;
}

.layout-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: auto;
}

.page-wrapper {
  margin-top: 60px;
  margin-bottom: 50px;
  overflow-y: auto;
  background-color: #f8f8f8;
  padding: 0;
}

.header-fixed {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 10;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  height: 60px;
}

.footer-fixed {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 10;
  background-color: #fff;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
  height: 50px;
}

/* Paginación */
// Estos estilos ya no se usan directamente, pero se han adaptado para .p-paginator
.pagination {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 5px;
  margin-top: 20px;
  padding-right: 10px;
}

.pagination-arrow,
.pagination-number {
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  color: #555;
  transition: background-color 0.2s ease, color 0.2s ease;
}

.pagination-number.active {
  background-color: #007bff;
  color: #fff;
  border-color: #007bff;
}

.pagination-number.active:hover {
  background-color: #0056b3;
}

.pagination-ellipsis {
  padding: 8px 0;
  color: #777;
}

/* Badges */
.status-badge {
    display: inline-flex; /* Usa flexbox para alinear elementos internos */
    align-items: center; /* Centra verticalmente el contenido */
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    text-transform: capitalize;
    gap: 4px; /* Espacio por defecto entre elementos, para el dot y el texto */
}

/* ESTILOS PARA EL ICONO SVG DEL PUNTO EN EL CAMPO 'ESTADO' */
.icon-dot {
    // Puedes ajustar el tamaño si lo necesitas, o dejar el width/height del SVG
    width: 10px; /* Un tamaño más pequeño para que parezca un punto */
    height: 10px; /* Un tamaño más pequeño para que parezca un punto */
    flex-shrink: 0; /* Evita que el SVG se encoja */
}

/* Colores de fondo y texto generales para los estados */
.status-active {
    color: #28a745; /* Color de texto verde */
}

.status-inactive {
    color: #000; /* Color de texto negro */
}

/* APLICA EL COLOR AL SVG DEL PUNTO BASADO EN EL ESTADO DEL PADRE */
.status-active .icon-dot {
    color: #28a745; /* Color verde para el punto activo */
}

.status-inactive .icon-dot {
    color: #df2f3d; /* Color rojo para el punto inactivo */
}

/* ESTILOS PARA LOS ICONOS SVG EN CANDIDATURA ACTIVA */
.icon-tick {
    color: #28a745; /* Color verde para el tick */
    // Puedes ajustar el tamaño si lo necesitas aquí, o en el HTML del SVG
    width: 12px;
    height: 12px;
}

.icon-cross {
    color: #df2f3d; /* Color rojo para la cruz */
    // Puedes ajustar el tamaño si lo necesitas aquí, o en el HTML del SVG
    width: 18px; /* Mantén tu tamaño original si prefieres */
    height: 18px;
}

.status-badge.status-with-icon.status-active {
    background-color: #DDF3C8; /* Fondo verde para Activo en Candidatura Activa */
}

.status-badge.status-with-icon.status-inactive {
    background-color: #FEE2E2; /* Fondo rojo claro para Inactivo en Candidatura Activa */
}

//* --- Sidebar (Modal-like) Styles --- */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  /* Semi-transparent black */
  z-index: 99;
  /* Below sidebar, above page content */
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.overlay.show {
  opacity: 1;
  visibility: visible;
}

.sidebar {
  position: fixed;
  top: 0;
  right: -400px;
  /* Start off-screen */
  width: 380px;
  /* Width of the sidebar */
  height: 100%;
  background-color: #fff;
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.2);
  z-index: 100;
  /* Above overlay */
  display: flex;
  flex-direction: column;
  transition: right 0.3s ease;
}

.sidebar.show {
  right: 0;
  /* Slide in */
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
  background-color: #f8f8f8;
}

.sidebar-header h2 {
  margin: 0;
  font-size: 20px;
  color: #333;
}

.sidebar-header .btn-icon {
  color: #666;
}

.sidebar-content {
  flex-grow: 1;
  padding: 20px;
  overflow-y: auto;
  /* Enable scrolling if content overflows */
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #555;
  font-size: 14px;
}

.form-group input[type="text"],
.form-group input[type="number"],
.form-group select {
  width: calc(100% - 20px);
  /* Adjust for padding */
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  color: #333;
  box-sizing: border-box;
  /* Include padding in width */
}

.form-group input[type="text"]::placeholder,
.form-group input[type="number"]::placeholder {
  color: #aaa;
}

.form-group input[type="number"] {
  -moz-appearance: textfield;
  /* Hide number input arrows in Firefox */
}

.form-group input[type="number"]::-webkit-outer-spin-button,
.form-group input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  /* Hide number input arrows in Chrome, Safari */
  margin: 0;
}


.form-group select {
  background-color: #fff;
  cursor: pointer;
}

.checkbox-group {
  display: flex;
  align-items: center;
  margin-top: 20px;
  /* Space from above inputs */
}

.checkbox-group input[type="checkbox"] {
  margin-right: 10px;
  width: 16px;
  /* Adjust size if needed */
  height: 16px;
  cursor: pointer;
}

.checkbox-group label {
  margin-bottom: 0;
  /* Reset margin for inline label */
}


.sidebar-footer {
  padding: 15px 20px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  background-color: #f8f8f8;
}

/* Reusing existing button styles */
.sidebar-footer .btn-primary,
.sidebar-footer .btn-secondary {
  min-width: 90px;
  justify-content: center;
}

