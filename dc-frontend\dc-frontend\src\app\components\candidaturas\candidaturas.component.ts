import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { HttpClientModule } from '@angular/common/http';
import { firstValueFrom } from 'rxjs';

import { HeaderComponent } from '../header/header.component';
import { FooterComponent } from '../footer/footer.component';

// PrimeNG
import { TableModule, Table } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { SelectModule } from 'primeng/select';
import { DialogModule } from 'primeng/dialog';
import { InputNumberModule } from 'primeng/inputnumber';
import { ToastModule } from 'primeng/toast';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { CheckboxModule } from 'primeng/checkbox';
import { InputTextModule } from 'primeng/inputtext';
import { MenuModule } from 'primeng/menu';
import { TagModule } from 'primeng/tag';
import { MessageService, ConfirmationService, MenuItem } from 'primeng/api';

// Services
import { CandidaturasService, CandidaturaDto, CandidaturaCreateRequest } from '../../services/candidaturas.service';
import { FormacionPoliticaService, PoliticalFormationBackend } from '../../services/formacion-politica.service';
import { CircunscripcionesService, Circunscripcion } from '../../services/circunscripcionService';

@Component({
  selector: 'app-candidaturas',
  standalone: true,
  imports: [
    CommonModule, RouterModule, FormsModule, HttpClientModule,
    HeaderComponent, FooterComponent,
    TableModule, ButtonModule, SelectModule, DialogModule, InputNumberModule, ToastModule,
    ConfirmDialogModule, CheckboxModule, InputTextModule, MenuModule, TagModule
  ],
  providers: [MessageService, ConfirmationService],
  templateUrl: './candidaturas.component.html',
  styleUrls: ['./candidaturas.component.scss']
})
export class CandidaturasComponent implements OnInit {
  @ViewChild('dt') dt: Table | undefined;

  candidaturas: CandidaturaDto[] = [];
  loading = false;

  // Selección y menús
  selected: CandidaturaDto[] = [];
  massItems: MenuItem[] = [];
  rowItems: MenuItem[] = [];
  currentRow: CandidaturaDto | null = null;
  // Estados visuales de menús
  isMassMenuOpen = false;
  openRowId: number | null = null;

  // Sidebar/Modal "Añadir/Editar"
  showSidebar = false;
  isEditing = false;
  formacionOptions: PoliticalFormationBackend[] = [];
  circunscripcionOptions: Circunscripcion[] = [];
  formacionFilterOptions: {label:string, value:number}[] = [];
  circunscripcionFilterOptions: {label:string, value:number}[] = [];
  tipoOptions = [ {label:'Manual', value:'Manual'}, {label:'Importada', value:'Importada'} ];
  estadoOptions = [ {label:'Proclamada', value:'Proclamada'}, {label:'Validada', value:'Validada'}, {label:'Rechazada', value:'Rechazada'}, {label:'Tag', value:'Tag'} ];

  // Diálogo Exportar
  showExport = false;
  exportForm = { formato: '', calidad: '', label1: '', label2: '' };
  formatos = [
    { label: 'CSV', value: 'csv' },
    { label: 'PDF', value: 'pdf' }
  ];
  calidades = [
    { label: 'Borrador', value: 'borrador' },
    { label: 'Alta', value: 'alta' }
  ];

  nuevaCandidatura: CandidaturaCreateRequest = {
    formacionPolitica: { id: null as any },
    circunscripcion: { id: null as any },
    orden: 1
  };

  constructor(
    private candidaturasService: CandidaturasService,
    private formacionesService: FormacionPoliticaService,
    private circService: CircunscripcionesService,
    private router: Router,
    private messageService: MessageService,
    private confirmationService: ConfirmationService
  ) {}

  ngOnInit(): void {
    this.loadData();
    this.loadDropdowns();
    this.massItems = [
      { label: 'Exportar', icon: 'pi pi-upload', command: () => this.abrirExport() },
      { label: 'Eliminar', icon: 'pi pi-trash', command: () => this.eliminarSeleccion() }
    ];
  }

  loadData(): void {
    this.loading = true;
    this.candidaturasService.getAll().subscribe({
      next: (data) => { this.candidaturas = data; this.loading = false; },
      error: () => { this.loading = false; this.messageService.add({severity:'error', summary:'Error', detail:'No se pudieron cargar las candidaturas'}); }
    });
  }

  loadDropdowns(): void {
    this.formacionesService.getAll().subscribe({ next: d => {
      this.formacionOptions = d;
      this.formacionFilterOptions = d.map(f => ({ label: f.nombre ?? f.siglas ?? String(f.id), value: f.id! }));
    }});
    this.circService.getAll().subscribe({ next: d => {
      this.circunscripcionOptions = d;
      this.circunscripcionFilterOptions = d.map(c => ({ label: c.nombre ?? String(c.id), value: c.id! }));
    }});
  }

  limpiarFiltros(): void { this.dt?.clear(); }

  goBack(): void {
    this.router.navigate(['/home']);
  }

  abrirModalCrear(): void { this.isEditing = false; this.resetForm(); this.showSidebar = true; }

  abrirModalEditar(row: CandidaturaDto): void {
    this.isEditing = true;
    this.nuevaCandidatura = {
      formacionPolitica: { id: row.formacionPolitica?.id || null as any },
      circunscripcion: { id: (row.circunscripcion?.id as number) || null as any },
      orden: row.orden || 1
    };
    this.showSidebar = true;
  }

  resetForm(): void {
    this.nuevaCandidatura = { formacionPolitica: { id: null as any }, circunscripcion: { id: null as any }, orden: 1 };
  }

  guardarCandidatura(): void {
    if (!this.nuevaCandidatura.formacionPolitica.id || !this.nuevaCandidatura.circunscripcion.id) {
      this.messageService.add({severity:'warn', summary:'Datos incompletos', detail:'Selecciona formación política y circunscripción'});
      return;
    }
    if (!this.nuevaCandidatura.orden || this.nuevaCandidatura.orden < 1) {
      this.messageService.add({severity:'warn', summary:'Datos incompletos', detail:'El orden debe ser mayor a 0'});
      return;
    }
    if (this.isEditing && this.currentRow?.id) {
      this.candidaturasService.update(this.currentRow.id, this.nuevaCandidatura).subscribe({
        next: () => { this.messageService.add({severity:'success', summary:'Candidatura actualizada', detail:'Los cambios se han guardado correctamente'}); this.showSidebar = false; this.loadData(); },
        error: (error) => {
          this.messageService.add({severity:'error', summary:'Error al actualizar', detail:'No se pudo actualizar la candidatura. Verifica los datos e inténtalo de nuevo.'});
        }
      });
    } else {
      this.candidaturasService.create(this.nuevaCandidatura).subscribe({
        next: () => { this.messageService.add({severity:'success', summary:'Candidatura creada', detail:'La nueva candidatura se ha registrado correctamente'}); this.showSidebar = false; this.loadData(); },
        error: (error) => {
          this.messageService.add({severity:'error', summary:'Error al crear', detail:'No se pudo crear la candidatura. Puede que ya exista una candidatura para esta formación política en esta circunscripción.'});
        }
      });
    }
  }

  setCurrentRow(row: CandidaturaDto, menu?: any, event?: Event): void {
    this.currentRow = row;
    this.rowItems = [
      { label: 'Ver candidatos', icon: 'pi pi-users', command: () => this.verCandidatos(row) },
      { label: 'Editar', icon: 'pi pi-pencil', command: () => this.abrirModalEditar(row) },
      { label: 'Eliminar', icon: 'pi pi-trash', command: () => this.eliminarFila(row) }
    ];
    if (menu && event) menu.toggle(event);
  }

  eliminarFila(row: CandidaturaDto): void {
    this.confirmationService.confirm({
      message: '¿Estás seguro de que deseas eliminar esta candidatura?',
      header: 'Confirmar eliminación',
      icon: 'pi pi-exclamation-triangle',
      acceptLabel: 'Sí, eliminar',
      rejectLabel: 'Cancelar',
      acceptButtonStyleClass: 'p-button-danger',
      accept: () => {
        this.candidaturasService.delete(row.id).subscribe({
          next: () => {
            this.messageService.add({severity:'success', summary:'Candidatura eliminada', detail:'La candidatura se ha eliminado correctamente'});
            this.loadData();
          },
          error: (error) => {
            this.messageService.add({severity:'error', summary:'Error al eliminar', detail:'No se pudo eliminar la candidatura. Puede que tenga candidatos asociados o esté validada.'});
          }
        });
      }
    });
  }

  eliminarSeleccion(): void {
    if (!this.selected.length) { this.messageService.add({severity:'warn', summary:'Nada seleccionado'}); return; }
    this.confirmationService.confirm({
      message: '¿Eliminar las candidaturas seleccionadas?', acceptLabel: 'Eliminar', rejectLabel: 'Cancelar',
      accept: () => {
        const ids = this.selected.map(s => s.id);
        // Ejecutar en paralelo y reportar resultado real
        const requests = ids.map(id => this.candidaturasService.delete(id));
        Promise.allSettled(requests.map(obs => firstValueFrom(obs))).then(results => {
          const ok = results.filter(r => r.status === 'fulfilled').length;
          const ko = results.length - ok;
          if (ok) this.messageService.add({severity:'success', summary:'Eliminadas', detail: `${ok} correctas`});
          if (ko) this.messageService.add({severity:'warn', summary:'No eliminadas', detail: `${ko} con error (revisa permisos o dependencias)`});
          this.selected = [];
          this.loadData();
        });
      }
    });
  }



  abrirExport(): void {
    if (!this.selected.length) { this.messageService.add({severity:'warn', summary:'Selecciona al menos una candidatura'}); return; }
    this.showExport = true;
  }

  exportar(): void {
    this.showExport = false;
    this.messageService.add({severity:'success', summary:'Exportación iniciada'});
  }

  verCandidatos(candidatura: CandidaturaDto): void {
    this.router.navigate(['/candidaturas', candidatura.id, 'candidatos']);
  }

  getTipoSeverity(tipo?: any): 'info' | 'warning' {
    const v = (tipo?.valor || tipo || '').toString().toLowerCase();
    return v === 'importada' ? 'warning' : 'info';
  }

  getEstadoSeverity(estado?: any): 'success' | 'danger' | 'info' {
    const v = (estado?.valor || estado || '').toString().toLowerCase();
    if (v.includes('rechaz')) return 'danger';
    if (v.includes('valid')) return 'success';
    return 'info';
  }
}

