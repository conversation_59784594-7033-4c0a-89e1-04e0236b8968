package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.controller;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.*;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service.CandidaturaService;
import es.parlamentodeandalucia.datoscandidatos.dto.Candidatura;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CandidaturaControllerTest {

    @Mock
    private CandidaturaService candidaturaService;

    @InjectMocks
    private CandidaturaController candidaturaController;

    private CandidaturaEntity candidaturaTest;
    private FormacionPoliticaEntity formacionTest;
    private CircunscripcionEntity circunscripcionTest;

    @BeforeEach
    void setUp() {
        formacionTest = new FormacionPoliticaEntity(1L, "Partido Popular", "PP", "PP001", true);
        
        circunscripcionTest = new CircunscripcionEntity();
        circunscripcionTest.setId(1L);
        circunscripcionTest.setNombre("Sevilla");
        circunscripcionTest.setCandidaturaActiva(true);

        candidaturaTest = new CandidaturaEntity(formacionTest, circunscripcionTest, 1, "testuser");
        candidaturaTest.setId(1L);
        candidaturaTest.setFechaCreacion(LocalDate.now());
    }

    @Test
    void testGetAllCandidaturas() {
        // Given
        List<CandidaturaEntity> candidaturas = Arrays.asList(candidaturaTest);
        when(candidaturaService.findAll()).thenReturn(candidaturas);

        // When
        ResponseEntity<List<Candidatura>> response = candidaturaController.getAllCandidaturas();

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(1, response.getBody().size());
        verify(candidaturaService).findAll();
    }

    @Test
    void testGetCandidaturaById_Encontrada() {
        // Given
        when(candidaturaService.findById(1L)).thenReturn(Optional.of(candidaturaTest));

        // When
        ResponseEntity<CandidaturaEntity> response = candidaturaController.getCandidaturaById(1L);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(1L, response.getBody().getId());
        verify(candidaturaService).findById(1L);
    }

    @Test
    void testGetCandidaturaById_NoEncontrada() {
        // Given
        when(candidaturaService.findById(1L)).thenReturn(Optional.empty());

        // When
        ResponseEntity<CandidaturaEntity> response = candidaturaController.getCandidaturaById(1L);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        verify(candidaturaService).findById(1L);
    }

    @Test
    void testUpdateCandidatura() {
        // Given
        when(candidaturaService.findById(1L)).thenReturn(Optional.of(candidaturaTest));
        when(candidaturaService.save(any(CandidaturaEntity.class))).thenReturn(candidaturaTest);

        // When
        ResponseEntity<CandidaturaEntity> response = candidaturaController.updateCandidatura(1L, candidaturaTest);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(1L, response.getBody().getId());
        verify(candidaturaService).findById(1L);
        verify(candidaturaService).save(any(CandidaturaEntity.class));
    }

    @Test
    void testDeleteCandidatura() {
        // Given
        when(candidaturaService.findById(1L)).thenReturn(Optional.of(candidaturaTest));
        doNothing().when(candidaturaService).deleteById(1L);

        // When
        ResponseEntity<Void> response = candidaturaController.deleteCandidatura(1L);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.NO_CONTENT, response.getStatusCode());
        verify(candidaturaService).findById(1L);
        verify(candidaturaService).deleteById(1L);
    }

    @Test
    void testGetByFormacionYCircunscripcion() {
        // Given
        when(candidaturaService.findByFormacionPoliticaAndCircunscripcion(1L, 1L))
            .thenReturn(Optional.of(candidaturaTest));

        // When
        ResponseEntity<CandidaturaEntity> response = candidaturaController.getByFormacionYCircunscripcion(1L, 1L);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(1L, response.getBody().getId());
        verify(candidaturaService).findByFormacionPoliticaAndCircunscripcion(1L, 1L);
    }

    @Test
    void testGetByFormacionYCircunscripcion_NoEncontrada() {
        // Given
        when(candidaturaService.findByFormacionPoliticaAndCircunscripcion(1L, 1L))
            .thenReturn(Optional.empty());

        // When
        ResponseEntity<CandidaturaEntity> response = candidaturaController.getByFormacionYCircunscripcion(1L, 1L);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        verify(candidaturaService).findByFormacionPoliticaAndCircunscripcion(1L, 1L);
    }
}
