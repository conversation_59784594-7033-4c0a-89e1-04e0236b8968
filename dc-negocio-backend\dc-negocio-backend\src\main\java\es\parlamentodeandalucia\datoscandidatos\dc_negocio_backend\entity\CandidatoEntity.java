package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import java.time.LocalDate;

@Entity
@Table(name = "dac_t_candidato")
public class CandidatoEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "dac_id_candidato")
    private Long id;

    @Column(name = "dac_tx_nombre")
    private String nombre;

    @Column(name = "dac_tx_apellido1")
    private String apellido1;

    @Column(name = "dac_tx_apellido2")
    private String apellido2;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "dac_fk_tipo_candidato")
    private TipoCandidatoEntity tipo;

    @Column(name = "dac_in_orden")
    private Long orden;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    @JoinColumn(name = "dac_fk_candidatura")
    private CandidaturaEntity candidatura;

    @Column(name = "dac_tx_usuario_creacion")
    private String usuarioCreacion;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "dac_fk_estado_candidato")
    private EstadoCandidatoEntity estado;

    @Column(name = "dac_tx_usuario_validacion")
    private String usuarioValidacion;

    @Column(name = "dac_fh_validacion")
    private LocalDate fechaValidacion;

    @Column(name = "dac_tx_comentario_validacion", length = 1024)
    private String comentarioValidacion;

    @Column(name = "dac_tx_observacion_rechazo", length = 1024)
    private String observacionRechazo;

    // Constructor por defecto
    public CandidatoEntity() {
    }

    // Constructor con parámetros principales
    public CandidatoEntity(String nombre, String apellido1, String apellido2,
                          TipoCandidatoEntity tipo, Long orden, CandidaturaEntity candidatura,
                          String usuarioCreacion) {
        this.nombre = nombre;
        this.apellido1 = apellido1;
        this.apellido2 = apellido2;
        this.tipo = tipo;
        this.orden = orden;
        this.candidatura = candidatura;
        this.usuarioCreacion = usuarioCreacion;
    }

    // Getters y Setters

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNombre() {
        return nombre;
    }

    public void setNombre(String nombre) {
        this.nombre = nombre;
    }

    public String getApellido1() {
        return apellido1;
    }

    public void setApellido1(String apellido1) {
        this.apellido1 = apellido1;
    }

    public String getApellido2() {
        return apellido2;
    }

    public void setApellido2(String apellido2) {
        this.apellido2 = apellido2;
    }



    public TipoCandidatoEntity getTipo() {
        return tipo;
    }

    public void setTipo(TipoCandidatoEntity tipo) {
        this.tipo = tipo;
    }

    public Long getOrden() {
        return orden;
    }

    public void setOrden(Long orden) {
        this.orden = orden;
    }

    public CandidaturaEntity getCandidatura() {
        return candidatura;
    }

    public void setCandidatura(CandidaturaEntity candidatura) {
        this.candidatura = candidatura;
    }

    public String getUsuarioCreacion() {
        return usuarioCreacion;
    }

    public void setUsuarioCreacion(String usuarioCreacion) {
        this.usuarioCreacion = usuarioCreacion;
    }



    public EstadoCandidatoEntity getEstado() {
        return estado;
    }

    public void setEstado(EstadoCandidatoEntity estado) {
        this.estado = estado;
    }

    public String getUsuarioValidacion() {
        return usuarioValidacion;
    }

    public void setUsuarioValidacion(String usuarioValidacion) {
        this.usuarioValidacion = usuarioValidacion;
    }

    public LocalDate getFechaValidacion() {
        return fechaValidacion;
    }

    public void setFechaValidacion(LocalDate fechaValidacion) {
        this.fechaValidacion = fechaValidacion;
    }

    public String getComentarioValidacion() {
        return comentarioValidacion;
    }

    public void setComentarioValidacion(String comentarioValidacion) {
        this.comentarioValidacion = comentarioValidacion;
    }

    public String getObservacionRechazo() {
        return observacionRechazo;
    }

    public void setObservacionRechazo(String observacionRechazo) {
        this.observacionRechazo = observacionRechazo;
    }

    @Override
    public String toString() {
        return "CandidatoEntity{" +
                "id=" + id +
                ", nombre='" + nombre + '\'' +
                ", apellido1='" + apellido1 + '\'' +
                ", apellido2='" + apellido2 + '\'' +
                ", orden=" + orden +
                ", usuarioCreacion='" + usuarioCreacion + '\'' +
                '}';
    }
}
