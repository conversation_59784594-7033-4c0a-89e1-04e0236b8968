import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

export interface CandidaturaDto {
  id: number;
  orden: number;
  usuarioCreacion?: string;
  fechaCreacion?: string;
  formacionPolitica?: { id: number; nombre?: string; siglas?: string };
  circunscripcion?: { id: number; nombre?: string; codigoProvincia?: string };
  tipoCandidatura?: { id?: number; valor?: string } | string;
  estadoCandidatura?: { id?: number; valor?: string } | string;
}

export interface CandidaturaCreateRequest {
  formacionPolitica: { id: number };
  circunscripcion: { id: number };
  orden: number;
}

@Injectable({ providedIn: 'root' })
export class CandidaturasService {
  private readonly apiUrl = environment.apiUrl + '/candidaturas';

  constructor(private http: HttpClient) {}

  getAll(): Observable<CandidaturaDto[]> {
    return this.http.get<CandidaturaDto[]>(this.apiUrl);
  }

  getById(id: number): Observable<CandidaturaDto> {
    return this.http.get<CandidaturaDto>(`${this.apiUrl}/${id}`);
  }

  create(payload: CandidaturaCreateRequest): Observable<any> {
    return this.http.post<any>(this.apiUrl, payload);
  }

  update(id: number, payload: any): Observable<any> {
    return this.http.put<any>(`${this.apiUrl}/${id}`, payload);
  }

  delete(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }
}

