package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.controller;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.PlantillaEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.TipoDocumentoEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service.PlantillaService;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service.TipoDocumentoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/api/plantillas")
@CrossOrigin(origins = { "http://localhost:4200", "https://localhost:4200" })
public class PlantillaController {

    @Autowired
    private PlantillaService plantillaService;

    @Autowired
    private TipoDocumentoService tipoDocumentoService;

    @GetMapping
    public ResponseEntity<List<PlantillaEntity>> getAll() {
        return ResponseEntity.ok(plantillaService.findAll());
    }

    @GetMapping("/{id}")
    public ResponseEntity<PlantillaEntity> getById(@PathVariable Long id) {
        return plantillaService.findById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @PostMapping
    public ResponseEntity<PlantillaEntity> create(@RequestBody PlantillaEntity plantilla) {
        PlantillaEntity saved = plantillaService.save(plantilla);
        return ResponseEntity.status(201).body(saved);
    }

    @PutMapping("/{id}")
    public ResponseEntity<PlantillaEntity> update(@PathVariable Long id,
            @RequestBody PlantillaEntity plantilla) {
        plantilla.setId(id);
        PlantillaEntity updated = plantillaService.save(plantilla);
        return ResponseEntity.ok(updated);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        plantillaService.deleteById(id);
        return ResponseEntity.noContent().build();
    }

    @PostMapping("/subir")
    public ResponseEntity<PlantillaEntity> crearConArchivo(
            @RequestParam("nombre") String nombre,
            @RequestParam("tipoDocumentoId") Long tipoDocumentoId,
            @RequestParam("activa") Boolean activa,
            @RequestParam("archivo") MultipartFile archivo) throws IOException {
        TipoDocumentoEntity tipoDocumento = tipoDocumentoService.findById(tipoDocumentoId);
        if (tipoDocumento == null) {
            return ResponseEntity.badRequest().build();
        }
        PlantillaEntity plantilla = new PlantillaEntity();
        plantilla.setNombre(nombre);
        plantilla.setTipoDocumento(tipoDocumento);
        plantilla.setActiva(activa);
        plantilla.setContenido(archivo.getBytes());
        PlantillaEntity saved = plantillaService.save(plantilla);
        return ResponseEntity.status(201).body(saved);
    }

    @PutMapping("/{id}/activar")
    public ResponseEntity<Void> activar(@PathVariable Long id) {
        plantillaService.activar(List.of(id));
        return ResponseEntity.ok().build();
    }

    @PutMapping("/{id}/desactivar")
    public ResponseEntity<Void> desactivar(@PathVariable Long id) {
        plantillaService.desactivar(List.of(id));
        return ResponseEntity.ok().build();
    }

    @PutMapping("/activar")
    public ResponseEntity<Void> activarVarias(@RequestBody List<Long> ids) {
        plantillaService.activar(ids);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/desactivar")
    public ResponseEntity<Void> desactivarVarias(@RequestBody List<Long> ids) {
        plantillaService.desactivar(ids);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/{id}/descargar")
    public ResponseEntity<byte[]> descargarArchivo(@PathVariable Long id) {
        return plantillaService.findById(id)
                .map(plantilla -> {
                    byte[] contenido = plantilla.getContenido();
                    String nombreArchivo = plantilla.getNombre().replaceAll(" ", "_") +
                            (esHtml(contenido) ? ".html" : ".docx");
                    return ResponseEntity.ok()
                            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + nombreArchivo + "\"")
                            .contentType(MediaType.APPLICATION_OCTET_STREAM)
                            .body(contenido);
                })
                .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/descargar")
    public ResponseEntity<byte[]> descargarZip(@RequestParam List<Long> ids) throws IOException {
        List<PlantillaEntity> plantillas = plantillaService.findAllByIds(ids);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ZipOutputStream zos = new ZipOutputStream(baos);
        for (PlantillaEntity plantilla : plantillas) {
            if (plantilla.getContenido() != null) {
                String nombreArchivo = plantilla.getNombre().replaceAll(" ", "_") +
                        (esHtml(plantilla.getContenido()) ? ".html" : ".docx");
                ZipEntry entry = new ZipEntry(nombreArchivo);
                zos.putNextEntry(entry);
                zos.write(plantilla.getContenido());
                zos.closeEntry();
            }
        }
        zos.close();
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"plantillas.zip\"")
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(baos.toByteArray());
    }

    private boolean esHtml(byte[] contenido) {
        String texto = new String(contenido, 0, Math.min(contenido.length, 100)).toLowerCase();
        return texto.contains("<html") || texto.contains("<!doctype");
    }

}

