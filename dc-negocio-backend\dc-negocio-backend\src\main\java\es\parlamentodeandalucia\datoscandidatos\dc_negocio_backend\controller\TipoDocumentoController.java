package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.controller;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.TipoDocumentoEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service.TipoDocumentoService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/tipos-documento")
public class TipoDocumentoController {

    private final TipoDocumentoService service;

    public TipoDocumentoController(TipoDocumentoService service) {
        this.service = service;
    }

    @GetMapping
    public List<TipoDocumentoEntity> getAll() {
        return service.findAll();
    }

    @GetMapping("/{id}")
    public TipoDocumentoEntity getById(@PathVariable Long id) {
        return service.findById(id);
    }

    @PostMapping
    public TipoDocumentoEntity create(@RequestBody TipoDocumentoEntity entity) {
        return service.save(entity);
    }

    @PutMapping("/{id}")
    public TipoDocumentoEntity update(@PathVariable Long id, @RequestBody TipoDocumentoEntity entity) {
        entity.setId(id);
        return service.save(entity);
    }

    @DeleteMapping("/{id}")
    public void delete(@PathVariable Long id) {
        service.deleteById(id);
    }
}
