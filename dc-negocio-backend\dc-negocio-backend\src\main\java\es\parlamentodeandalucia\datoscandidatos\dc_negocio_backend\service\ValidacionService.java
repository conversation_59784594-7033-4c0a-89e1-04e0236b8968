package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.*;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

@Service
public class ValidacionService {

    @Autowired
    private CandidatoRepository candidatoRepository;

    @Autowired
    private CandidaturaRepository candidaturaRepository;

    @Autowired
    private FormacionPoliticaRepository formacionPoliticaRepository;

    @Autowired
    private CircunscripcionRepository circunscripcionRepository;

    // Patrones de validación (consistentes con la BD: varchar(255))
    // Incluye caracteres españoles, catalanes, gallegos, vascos y otros caracteres europeos comunes
    private static final Pattern NOMBRE_PATTERN = Pattern.compile("^[a-zA-ZáéíóúÁÉÍÓÚñÑüÜàèìòùÀÈÌÒÙâêîôûÂÊÎÔÛäëïöÄËÏÖçÇ\\s.'\\-]{2,255}$");
    private static final Pattern APELLIDO_PATTERN = Pattern.compile("^[a-zA-ZáéíóúÁÉÍÓÚñÑüÜàèìòùÀÈÌÒÙâêîôûÂÊÎÔÛäëïöÄËÏÖçÇ\\s.'\\-]{2,255}$");
    private static final Pattern SIGLAS_PATTERN = Pattern.compile("^[A-Z]{2,10}$");

    /**
     * Valida un candidato antes de guardarlo
     */
    public List<String> validarCandidato(CandidatoEntity candidato) {
        List<String> errores = new ArrayList<>();

        // Validaciones de campos obligatorios
        if (candidato.getNombre() == null || candidato.getNombre().trim().isEmpty()) {
            errores.add("El nombre es obligatorio");
        } else if (!NOMBRE_PATTERN.matcher(candidato.getNombre().trim()).matches()) {
            errores.add("El nombre debe contener solo letras y espacios, entre 2 y 255 caracteres");
        }

        if (candidato.getApellido1() == null || candidato.getApellido1().trim().isEmpty()) {
            errores.add("El primer apellido es obligatorio");
        } else if (!APELLIDO_PATTERN.matcher(candidato.getApellido1().trim()).matches()) {
            // Debug: mostrar exactamente qué está fallando
            String apellido = candidato.getApellido1().trim();
            errores.add("El primer apellido debe contener solo letras y espacios, entre 2 y 255 caracteres. " +
                       "Valor recibido: '" + apellido + "' (longitud: " + apellido.length() + ")");
        }

        if (candidato.getApellido2() != null && !candidato.getApellido2().trim().isEmpty()) {
            if (!APELLIDO_PATTERN.matcher(candidato.getApellido2().trim()).matches()) {
                // Debug: mostrar exactamente qué está fallando
                String apellido2 = candidato.getApellido2().trim();
                errores.add("El segundo apellido debe contener solo letras y espacios, entre 2 y 255 caracteres. " +
                           "Valor recibido: '" + apellido2 + "' (longitud: " + apellido2.length() + ")");
            }
        }

        if (candidato.getCandidatura() == null) {
            errores.add("La candidatura es obligatoria");
        }

        if (candidato.getTipo() == null) {
            errores.add("El tipo de candidato es obligatorio");
        }

        // Validación de duplicados
        if (candidato.getCandidatura() != null) {
            boolean existeDuplicado = candidatoRepository.existsDuplicateInCandidatura(
                candidato.getCandidatura().getId(),
                candidato.getNombre(),
                candidato.getApellido1(),
                candidato.getApellido2(),
                candidato.getId()
            );
            
            if (existeDuplicado) {
                errores.add("Ya existe un candidato con el mismo nombre y apellidos en esta candidatura");
            }
        }

        // Validación de orden
        if (candidato.getOrden() != null && candidato.getOrden() <= 0) {
            errores.add("El orden debe ser un número positivo");
        }

        return errores;
    }

    /**
     * Valida una candidatura antes de guardarla
     */
    public List<String> validarCandidatura(CandidaturaEntity candidatura) {
        List<String> errores = new ArrayList<>();

        // Validaciones de campos obligatorios
        if (candidatura.getFormacionPolitica() == null) {
            errores.add("La formación política es obligatoria");
        }

        if (candidatura.getCircunscripcion() == null) {
            errores.add("La circunscripción es obligatoria");
        }

        // Validación de duplicados
        if (candidatura.getFormacionPolitica() != null && candidatura.getCircunscripcion() != null) {
            boolean existeDuplicado = candidaturaRepository.existsDuplicate(
                candidatura.getFormacionPolitica().getId(),
                candidatura.getCircunscripcion().getId(),
                candidatura.getId()
            );
            
            if (existeDuplicado) {
                errores.add("Ya existe una candidatura para esta formación política en esta circunscripción");
            }
        }

        // Validación de orden
        if (candidatura.getOrden() != null && candidatura.getOrden() <= 0) {
            errores.add("El orden debe ser un número positivo");
        }

        return errores;
    }

    /**
     * Valida una formación política antes de guardarla
     */
    public List<String> validarFormacionPolitica(FormacionPoliticaEntity formacion) {
        List<String> errores = new ArrayList<>();

        // Validaciones de campos obligatorios
        if (formacion.getNombre() == null || formacion.getNombre().trim().isEmpty()) {
            errores.add("El nombre de la formación política es obligatorio");
        } else if (formacion.getNombre().trim().length() < 3 || formacion.getNombre().trim().length() > 100) {
            errores.add("El nombre debe tener entre 3 y 100 caracteres");
        }

        if (formacion.getSiglas() == null || formacion.getSiglas().trim().isEmpty()) {
            errores.add("Las siglas son obligatorias");
        } else if (!SIGLAS_PATTERN.matcher(formacion.getSiglas().trim()).matches()) {
            errores.add("Las siglas deben contener solo letras mayúsculas, entre 2 y 10 caracteres");
        }

        // Validación de duplicados
        if (formacion.getNombre() != null && formacion.getSiglas() != null) {
            boolean existeDuplicado = formacionPoliticaRepository.existsDuplicate(
                formacion.getNombre().trim(),
                formacion.getSiglas().trim(),
                formacion.getId()
            );
            
            if (existeDuplicado) {
                errores.add("Ya existe una formación política con el mismo nombre o siglas");
            }
        }

        return errores;
    }

    /**
     * Valida una circunscripción antes de guardarla
     */
    public List<String> validarCircunscripcion(CircunscripcionEntity circunscripcion) {
        List<String> errores = new ArrayList<>();

        // Validaciones de campos obligatorios
        if (circunscripcion.getNombre() == null || circunscripcion.getNombre().trim().isEmpty()) {
            errores.add("El nombre de la circunscripción es obligatorio");
        } else if (circunscripcion.getNombre().trim().length() < 3 || circunscripcion.getNombre().trim().length() > 50) {
            errores.add("El nombre debe tener entre 3 y 50 caracteres");
        }

        if (circunscripcion.getCodigoProvincia() != null) {
            try {
                int codigo = Integer.parseInt(circunscripcion.getCodigoProvincia());
                if (codigo < 1 || codigo > 99) {
                    errores.add("El código de provincia debe estar entre 1 y 99");
                }
            } catch (NumberFormatException e) {
                errores.add("El código de provincia debe ser un número válido");
            }
        }

        // Validación de duplicados por nombre
        if (circunscripcion.getNombre() != null) {
            boolean existeNombre = circunscripcionRepository.findByNombre(circunscripcion.getNombre().trim())
                .filter(existing -> !existing.getId().equals(circunscripcion.getId()))
                .isPresent();
            
            if (existeNombre) {
                errores.add("Ya existe una circunscripción con el mismo nombre");
            }
        }

        return errores;
    }

    /**
     * Valida si se puede eliminar una candidatura
     */
    public List<String> validarEliminacionCandidatura(CandidaturaEntity candidatura) {
        List<String> errores = new ArrayList<>();

        if (candidatura.getEstadoCandidatura() != null) {
            String estado = candidatura.getEstadoCandidatura().getValor();
            if ("VALIDADA_POR_JEA".equals(estado) || "VALIDADA_POR_JEP".equals(estado)) {
                errores.add("No se puede eliminar una candidatura que ya ha sido validada");
            }
        }

        // Verificar si tiene candidatos asociados
        if (candidatura.getCandidatos() != null && !candidatura.getCandidatos().isEmpty()) {
            errores.add("No se puede eliminar una candidatura que tiene candidatos asociados. Elimine primero los candidatos.");
        }

        return errores;
    }

    /**
     * Valida si se puede eliminar un candidato
     */
    public List<String> validarEliminacionCandidato(CandidatoEntity candidato) {
        List<String> errores = new ArrayList<>();

        if (candidato.getEstado() != null) {
            String estado = candidato.getEstado().getValor();
            if ("VALIDADO".equals(estado)) {
                errores.add("No se puede eliminar un candidato que ya ha sido validado");
            }
        }

        return errores;
    }

    /**
     * Valida si se puede cambiar el estado de una candidatura
     */
    public List<String> validarCambioEstadoCandidatura(CandidaturaEntity candidatura, String nuevoEstado) {
        List<String> errores = new ArrayList<>();

        if (candidatura.getEstadoCandidatura() != null) {
            String estadoActual = candidatura.getEstadoCandidatura().getValor();
            
            // Reglas de transición de estados
            switch (estadoActual) {
                case "IMPORTADA":
                case "MANUAL":
                    // Puede cambiar a cualquier estado
                    break;
                case "VALIDADA_POR_JEA":
                    if (!"VALIDADA_POR_JEP".equals(nuevoEstado) && !"RECHAZADA".equals(nuevoEstado)) {
                        errores.add("Una candidatura validada por JEA solo puede pasar a validada por JEP o rechazada");
                    }
                    break;
                case "VALIDADA_POR_JEP":
                    errores.add("Una candidatura validada por JEP no puede cambiar de estado");
                    break;
                case "RECHAZADA":
                    if (!"MANUAL".equals(nuevoEstado)) {
                        errores.add("Una candidatura rechazada solo puede volver a estado manual para corrección");
                    }
                    break;
            }
        }

        return errores;
    }

    /**
     * Valida los límites de candidatos por tipo en una candidatura
     */
    public List<String> validarLimitesCandidatos(Long candidaturaId, Long tipoId, int nuevoCandidatos) {
        List<String> errores = new ArrayList<>();

        Long candidatosActuales = candidatoRepository.countByCandidaturaIdAndTipoId(candidaturaId, tipoId);
        Long totalCandidatos = candidatosActuales + nuevoCandidatos;

        // Límites configurables (podrían venir de configuración)
        int maxTitulares = 50; // Ejemplo
        int maxSuplentes = 50; // Ejemplo

        // Aquí necesitarías identificar si tipoId corresponde a TITULAR o SUPLENTE
        // Por simplicidad, asumo que tipoId 1 = TITULAR, tipoId 2 = SUPLENTE
        if (tipoId == 1 && totalCandidatos > maxTitulares) {
            errores.add("Se ha superado el límite máximo de candidatos titulares (" + maxTitulares + ")");
        } else if (tipoId == 2 && totalCandidatos > maxSuplentes) {
            errores.add("Se ha superado el límite máximo de candidatos suplentes (" + maxSuplentes + ")");
        }

        return errores;
    }

    /**
     * Valida que los datos de importación sean correctos
     */
    public List<String> validarDatosImportacion(String[] campos, int numeroLinea) {
        List<String> errores = new ArrayList<>();

        if (campos.length < 6) {
            errores.add("Línea " + numeroLinea + ": Se requieren al menos 6 campos (formación, siglas, circunscripción, nombre, apellido1, apellido2)");
            return errores;
        }

        // Validar formación política
        if (campos[0].trim().isEmpty()) {
            errores.add("Línea " + numeroLinea + ": El nombre de la formación política es obligatorio");
        }

        if (campos[1].trim().isEmpty()) {
            errores.add("Línea " + numeroLinea + ": Las siglas de la formación política son obligatorias");
        } else if (!SIGLAS_PATTERN.matcher(campos[1].trim()).matches()) {
            errores.add("Línea " + numeroLinea + ": Las siglas deben contener solo letras mayúsculas");
        }

        // Validar circunscripción
        if (campos[2].trim().isEmpty()) {
            errores.add("Línea " + numeroLinea + ": El nombre de la circunscripción es obligatorio");
        }

        // Validar candidato
        if (campos[3].trim().isEmpty()) {
            errores.add("Línea " + numeroLinea + ": El nombre del candidato es obligatorio");
        } else if (!NOMBRE_PATTERN.matcher(campos[3].trim()).matches()) {
            errores.add("Línea " + numeroLinea + ": El nombre del candidato contiene caracteres no válidos");
        }

        if (campos[4].trim().isEmpty()) {
            errores.add("Línea " + numeroLinea + ": El primer apellido del candidato es obligatorio");
        } else if (!APELLIDO_PATTERN.matcher(campos[4].trim()).matches()) {
            errores.add("Línea " + numeroLinea + ": El primer apellido contiene caracteres no válidos");
        }

        // Validar tipo de candidato si se proporciona
        if (campos.length > 6 && !campos[6].trim().isEmpty()) {
            String tipo = campos[6].trim().toUpperCase();
            if (!"TITULAR".equals(tipo) && !"SUPLENTE".equals(tipo)) {
                errores.add("Línea " + numeroLinea + ": El tipo de candidato debe ser TITULAR o SUPLENTE");
            }
        }

        return errores;
    }
}
