import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { HttpClientModule } from '@angular/common/http';
import { ConfirmationService, MessageService, MenuItem } from 'primeng/api';
import { forkJoin } from 'rxjs';

import { HeaderComponent } from '../header/header.component';
import { FooterComponent } from '../footer/footer.component';

// PrimeNG UI Modules
import { TableModule, Table } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { DropdownModule } from 'primeng/dropdown';
import { CheckboxModule } from 'primeng/checkbox';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
import { PaginatorModule } from 'primeng/paginator';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { RippleModule } from 'primeng/ripple';
import { MenuModule, Menu } from 'primeng/menu';
import { MultiSelectModule } from 'primeng/multiselect';
import { CalendarModule } from 'primeng/calendar';
import { TabViewModule } from 'primeng/tabview';
import { ToastModule } from 'primeng/toast';



// Interfaces
interface Candidatura {
    id: number;
    nombre: string;
    formacionPolitica: string;
    circunscripcion: string;
    orden: number;
    tipo: 'Manual' | 'Importada';
    estado: 'Pendiente' | 'Validado' | 'Rechazado';
}

interface DropdownOption {
    label: string;
    value: any;
}

@Component({
    selector: 'app-validacion-candidaturas',
    standalone: true,
    imports: [
        CommonModule,
        RouterModule,
        FormsModule,
        HttpClientModule,
        HeaderComponent,
        FooterComponent,
        // PrimeNG
        TableModule,
        ButtonModule,
        InputTextModule,
        DropdownModule,
        CheckboxModule,
        IconFieldModule,
        InputIconModule,
        PaginatorModule,
        ConfirmDialogModule,
        DialogModule,
        RippleModule,
        MenuModule,
        MultiSelectModule,
        CalendarModule,
        TabViewModule,
        ToastModule
    ],
    providers: [ConfirmationService, MessageService],
    templateUrl: './validacion-candidaturas.component.html',
    styleUrl: './validacion-candidaturas.component.scss'
})
export class ValidacionCandidaturasComponent implements OnInit {
    @ViewChild('dtPendientes') dtPendientes: Table | undefined;
    @ViewChild('dtValidados') dtValidados: Table | undefined;

    candidaturas: Candidatura[] = [];
    candidaturasPendientes: Candidatura[] = [];
    candidaturasValidadas: Candidatura[] = [];
    
    selectedCandidaturas: Candidatura[] = [];
    
    showMassiveActions = false;
    openedDropdownId: number | null = null;
    
    activeTabIndex = 0;

    // Opciones de filtro para el dropdown
    formacionesPoliticas: DropdownOption[] = [];
    circunscripciones: DropdownOption[] = [];
    tipos: DropdownOption[] = [];
    
    constructor(
        private confirmationService: ConfirmationService,
        private messageService: MessageService
    ) {}

    ngOnInit(): void {
        this.loadCandidaturas();
        this.loadFilterOptions();
    }

    loadCandidaturas(): void {
        // Datos de prueba provisionales
        const allCandidaturas: Candidatura[] = [
        { id: 1, nombre: 'Laura Ramirez Santos', formacionPolitica: 'PSOE', circunscripcion: 'Sevilla', orden: 1, tipo: 'Manual', estado: 'Pendiente' },
        { id: 2, nombre: 'Juan Pérez García', formacionPolitica: 'PP', circunscripcion: 'Málaga', orden: 2, tipo: 'Manual', estado: 'Pendiente' },
        { id: 3, nombre: 'María Lopez Fernández', formacionPolitica: 'VOX', circunscripcion: 'Granada', orden: 3, tipo: 'Importada', estado: 'Pendiente' },
        { id: 4, nombre: 'Pedro Gómez Ruiz', formacionPolitica: 'PSOE', circunscripcion: 'Sevilla', orden: 2, tipo: 'Manual', estado: 'Validado' },
        { id: 5, nombre: 'Ana Torres Gil', formacionPolitica: 'Ciudadanos', circunscripcion: 'Jaén', orden: 1, tipo: 'Importada', estado: 'Validado' },
        { id: 6, nombre: 'Marta Díaz León', formacionPolitica: 'Podemos', circunscripcion: 'Cádiz', orden: 1, tipo: 'Manual', estado: 'Pendiente' },
        { id: 7, nombre: 'Sergio Romero Ruiz', formacionPolitica: 'PP', circunscripcion: 'Huelva', orden: 3, tipo: 'Manual', estado: 'Validado' },
        { id: 8, nombre: 'Isabel Fernández Rey', formacionPolitica: 'PSOE', circunscripcion: 'Sevilla', orden: 3, tipo: 'Importada', estado: 'Pendiente' },
        { id: 9, nombre: 'Jose Antonio Reyes', formacionPolitica: 'VOX', circunscripcion: 'Granada', orden: 1, tipo: 'Manual', estado: 'Validado' },
        { id: 10, nombre: 'Elena Castillo Vazquez', formacionPolitica: 'Ciudadanos', circunscripcion: 'Jaén', orden: 2, tipo: 'Importada', estado: 'Validado' },
        { id: 11, nombre: 'Javier Luque Ramos', formacionPolitica: 'PSOE', circunscripcion: 'Córdoba', orden: 1, tipo: 'Manual', estado: 'Pendiente' },
        { id: 12, nombre: 'Lucía Morales', formacionPolitica: 'PP', circunscripcion: 'Almería', orden: 2, tipo: 'Manual', estado: 'Validado' },
      ];


        this.candidaturas = allCandidaturas;
        this.candidaturasPendientes = this.candidaturas.filter(c => c.estado === 'Pendiente');
        this.candidaturasValidadas = this.candidaturas.filter(c => c.estado === 'Validado');
    }
    
    loadFilterOptions(): void {
        this.formacionesPoliticas = [
            { label: 'PSOE', value: 'PSOE' },
            { label: 'PP', value: 'PP' },
            { label: 'VOX', value: 'VOX' },
            { label: 'Ciudadanos', value: 'Ciudadanos' },
        ];
        this.circunscripciones = [
            { label: 'Sevilla', value: 'Sevilla' },
            { label: 'Málaga', value: 'Málaga' },
            { label: 'Granada', value: 'Granada' },
            { label: 'Jaén', value: 'Jaén' },
        ];
        this.tipos = [
            { label: 'Manual', value: 'Manual' },
            { label: 'Importada', value: 'Importada' }
        ];
    }

    clearFilters(): void {
        if (this.activeTabIndex === 0) {
            this.dtPendientes?.clear();
        } else {
            this.dtValidados?.clear();
        }
    }

    isRowSelectable(row: { data: Candidatura }): boolean {
        return true;
    }

    // Acciones de fila
    toggleDropdown(id: number): void {
        this.openedDropdownId = this.openedDropdownId === id ? null : id;
    }

    // Acciones específicas de "Pendientes"
    validarCandidatura(id: number): void {
        const candidatura = this.candidaturas.find(c => c.id === id);
        if (candidatura) {
            candidatura.estado = 'Validado';
            this.messageService.add({ severity: 'success', summary: 'Validado', detail: 'Candidatura validada correctamente' });
            this.loadCandidaturas();
        }
        this.openedDropdownId = null;
    }

    rechazarCandidatura(id: number): void {
        const candidatura = this.candidaturas.find(c => c.id === id);
        if (candidatura) {
            candidatura.estado = 'Rechazado';
            this.messageService.add({ severity: 'warn', summary: 'Rechazado', detail: 'Candidatura rechazada' });
            this.loadCandidaturas();
        }
        this.openedDropdownId = null;
    }

    // Acciones específicas de "Validados"
    revertirValidacion(id: number): void {
        const candidatura = this.candidaturas.find(c => c.id === id);
        if (candidatura) {
            candidatura.estado = 'Pendiente';
            this.messageService.add({ severity: 'info', summary: 'Revertido', detail: 'Validación revertida, la candidatura ahora está pendiente' });
            this.loadCandidaturas();
        }
        this.openedDropdownId = null;
    }

    // Acciones masivas
    validarSeleccionadas(): void {
        this.selectedCandidaturas.forEach(c => c.estado = 'Validado');
        this.messageService.add({ severity: 'success', summary: 'Validado', detail: `${this.selectedCandidaturas.length} candidaturas validadas` });
        this.loadCandidaturas();
        this.selectedCandidaturas = [];
        this.showMassiveActions = false;
    }

    rechazarSeleccionadas(): void {
        this.selectedCandidaturas.forEach(c => c.estado = 'Rechazado');
        this.messageService.add({ severity: 'warn', summary: 'Rechazado', detail: `${this.selectedCandidaturas.length} candidaturas rechazadas` });
        this.loadCandidaturas();
        this.selectedCandidaturas = [];
        this.showMassiveActions = false;
    }

    revertirSeleccionadas(): void {
        this.selectedCandidaturas.forEach(c => c.estado = 'Pendiente');
        this.messageService.add({ severity: 'info', summary: 'Revertido', detail: `${this.selectedCandidaturas.length} candidaturas revertidas a pendientes` });
        this.loadCandidaturas();
        this.selectedCandidaturas = [];
        this.showMassiveActions = false;
    }

    // Estilos dinámicos
    getTipoClass(tipo: string): string {
        return tipo === 'Manual' ? 'status-manual' : 'status-importada';
    }
    
    getStatusClass(estado: string): string {
        if (estado === 'Validado') {
            return 'status-validado';
        } else if (estado === 'Pendiente') {
            return 'status-pendiente';
        } else {
            return 'status-rechazado';
        }
    }

    onTabChange(event: any) {
      this.activeTabIndex = event.index;
      this.selectedCandidaturas = []; // Limpiar la selección al cambiar de pestaña
      this.showMassiveActions = false;
    }

    // Estilos de fila alternos
    getRowStyle(candidatura: Candidatura, rowIndex: number): any {
        return {
            'row-par': rowIndex % 2 === 0,
            'row-impar': rowIndex % 2 !== 0,
            'p-highlight': this.selectedCandidaturas.includes(candidatura)
        };
    }
}