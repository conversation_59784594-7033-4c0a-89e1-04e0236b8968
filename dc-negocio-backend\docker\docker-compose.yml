version: '3.8'

services:
  # Servicio de la base de datos PostgreSQL
  postgres:
    image: postgres:17
    container_name: datos-candidatos-postgres
    environment:
      POSTGRES_DB: datosCandidatos
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../script-bd:/docker-entrypoint-initdb.d
    networks:
      - datos-candidatos-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d datosCandidatos"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # Servicio del backend de Spring Boot
  backend:
    image: albertogutierrezsoltel/datos-candidatos-backend:0.0.5
    container_name: datos-candidatos-backend
    restart: unless-stopped
    environment:
      SPRING_DATASOURCE_URL: ***********************************************
      SPRING_DATASOURCE_USERNAME: postgres
      SPRING_DATASOURCE_PASSWORD: postgres
      SPRING_PROFILES_ACTIVE: prod
      JAVA_OPTS: "-Xmx768m -Xms512m"
      
      # --- NUEVAS VARIABLES DE ENTORNO CLAVE ---
      # Forzar la ruta del keystore a través de una variable de entorno
      SERVER_SSL_KEY_STORE: file:/app/config/keystore-prod.p12 
      # Contraseña del keystore SSL/TLS
      SSL_KEY_STORE_PASSWORD: 123456
      # --- FIN NUEVAS VARIABLES DE ENTORNO ---

    ports:
      - "8084:8084"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - datos-candidatos-network
    healthcheck:
      test: ["CMD", "curl", "-f", "https://localhost:8084/actuator/health", "--insecure"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 90s
    deploy:
      resources:
        limits:
          memory: 768M
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # Servicio del frontend de Angular (servido por Nginx)
  frontend:
    image: albertogutierrezsoltel/datos-candidatos-frontend:0.0.5
    container_name: datos-candidatos-frontend
    restart: unless-stopped
    environment:
      - NODE_ENV=production
    ports:
      - "8007:443"
    networks:
      - datos-candidatos-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 3s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'
    labels:
      - "com.docker.compose.project=datos-candidatos"
      - "description=Frontend de Datos Candidatos - Angular 19 App"

volumes:
  postgres_data:
    driver: local

networks:
  datos-candidatos-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
