package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.dto;

public class CandidaturaCreateRequest {
    private FormacionPoliticaDto formacionPolitica;
    private CircunscripcionDto circunscripcion;
    private Integer orden;

    // Constructors
    public CandidaturaCreateRequest() {}

    public CandidaturaCreateRequest(FormacionPoliticaDto formacionPolitica, CircunscripcionDto circunscripcion, Integer orden) {
        this.formacionPolitica = formacionPolitica;
        this.circunscripcion = circunscripcion;
        this.orden = orden;
    }

    // Getters and Setters
    public FormacionPoliticaDto getFormacionPolitica() {
        return formacionPolitica;
    }

    public void setFormacionPolitica(FormacionPoliticaDto formacionPolitica) {
        this.formacionPolitica = formacionPolitica;
    }

    public CircunscripcionDto getCircunscripcion() {
        return circunscripcion;
    }

    public void setCircunscripcion(CircunscripcionDto circunscripcion) {
        this.circunscripcion = circunscripcion;
    }

    public Integer getOrden() {
        return orden;
    }

    public void setOrden(Integer orden) {
        this.orden = orden;
    }

    @Override
    public String toString() {
        return "CandidaturaCreateRequest{" +
                "formacionPolitica=" + formacionPolitica +
                ", circunscripcion=" + circunscripcion +
                ", orden=" + orden +
                '}';
    }

    // DTOs internos
    public static class FormacionPoliticaDto {
        private Long id;
        private String nombre;
        private String siglas;
        private String codigoInterno;

        // Constructors
        public FormacionPoliticaDto() {}

        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        public String getNombre() { return nombre; }
        public void setNombre(String nombre) { this.nombre = nombre; }
        public String getSiglas() { return siglas; }
        public void setSiglas(String siglas) { this.siglas = siglas; }
        public String getCodigoInterno() { return codigoInterno; }
        public void setCodigoInterno(String codigoInterno) { this.codigoInterno = codigoInterno; }
    }

    public static class CircunscripcionDto {
        private Long id;
        private String nombre;
        private String codigo;
        private String provincia;
        private String comunidad_autonoma;

        // Constructors
        public CircunscripcionDto() {}

        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        public String getNombre() { return nombre; }
        public void setNombre(String nombre) { this.nombre = nombre; }
        public String getCodigo() { return codigo; }
        public void setCodigo(String codigo) { this.codigo = codigo; }
        public String getProvincia() { return provincia; }
        public void setProvincia(String provincia) { this.provincia = provincia; }
        public String getComunidad_autonoma() { return comunidad_autonoma; }
        public void setComunidad_autonoma(String comunidad_autonoma) { this.comunidad_autonoma = comunidad_autonoma; }
    }
}
