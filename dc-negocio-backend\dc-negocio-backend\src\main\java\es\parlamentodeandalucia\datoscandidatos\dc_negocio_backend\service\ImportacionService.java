package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.*;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
public class ImportacionService {

    @Autowired
    private ImportacionRepository importacionRepository;

    @Autowired
    private ErrorImportRepository errorImportRepository;

    @Autowired
    private CsvImportService csvImportService;

    /**
     * Obtiene todas las importaciones
     */
    public List<ImportacionEntity> findAll() {
        return importacionRepository.findAll();
    }

    /**
     * Busca una importación por ID
     */
    public Optional<ImportacionEntity> findById(Long id) {
        return importacionRepository.findById(id);
    }

    /**
     * Busca importaciones con filtros y paginación
     */
    public Page<ImportacionEntity> findWithFilters(String usuarioImportacion, String formatoFichero, 
                                                  Boolean importado, Pageable pageable) {
        return importacionRepository.findWithFilters(usuarioImportacion, formatoFichero, importado, pageable);
    }

    /**
     * Busca importaciones por usuario
     */
    public List<ImportacionEntity> findByUsuario(String usuarioImportacion) {
        return importacionRepository.findByUsuarioImportacion(usuarioImportacion);
    }

    /**
     * Busca importaciones con errores
     */
    public List<ImportacionEntity> findImportacionesConErrores() {
        return importacionRepository.findImportacionesConErrores();
    }

    /**
     * Busca importaciones exitosas
     */
    public List<ImportacionEntity> findImportacionesExitosas() {
        return importacionRepository.findImportacionesExitosas();
    }

    /**
     * Obtiene estadísticas de importaciones por usuario
     */
    public Object[] getEstadisticasPorUsuario(String usuarioImportacion) {
        return importacionRepository.getEstadisticasPorUsuario(usuarioImportacion);
    }

    /**
     * Procesa un archivo CSV de importación (sin transacción global)
     */
    public ImportacionEntity procesarArchivoCsv(MultipartFile archivo, String usuarioImportacion, String ip, Long formacionPoliticaId) {
        try {
            // Validar que sea un archivo CSV
            String nombreArchivo = archivo.getOriginalFilename();
            if (nombreArchivo == null || !nombreArchivo.endsWith(".csv")) {
                throw new IllegalArgumentException("El archivo debe ser un CSV (.csv)");
            }

            // Crear registro de importación
            ImportacionEntity importacion = new ImportacionEntity(
                usuarioImportacion,
                LocalDate.now(),
                nombreArchivo,
                "CSV",
                archivo.getBytes()
            );

            importacion = guardarImportacion(importacion);

            // Procesar archivo CSV usando el servicio especializado
            Map<String, Object> resultado = csvImportService.procesarCsv(archivo, usuarioImportacion, formacionPoliticaId);

            // Actualizar contadores
            Integer candidatosCreados = (Integer) resultado.get("candidatosCreados");
            Integer candidaturasCreadas = (Integer) resultado.get("candidaturasCreadas");

            importacion.setFilasCorrectas(candidatosCreados != null ? candidatosCreados.longValue() : 0L);
            importacion.setFilasIncorrectas(0L); // Se calculará basado en errores

            // Registrar errores si los hay
            @SuppressWarnings("unchecked")
            List<String> errores = (List<String>) resultado.get("errores");
            if (errores != null && !errores.isEmpty()) {
                importacion.setFilasIncorrectas((long) errores.size());
                for (int i = 0; i < errores.size(); i++) {
                    registrarError(importacion, (long) (i + 1), "PROCESAMIENTO", errores.get(i));
                }
            }

            // Generar y guardar el log de resultado
            String logResultado = generarLogResultado(importacion, resultado);
            importacion.setResultado(logResultado.getBytes());

            return actualizarImportacion(importacion);

        } catch (IOException e) {
            throw new RuntimeException("Error al procesar el archivo", e);
        }
    }

    /**
     * Guarda una importación en transacción separada
     */
    @Transactional
    private ImportacionEntity guardarImportacion(ImportacionEntity importacion) {
        return importacionRepository.save(importacion);
    }

    /**
     * Actualiza una importación en transacción separada
     */
    @Transactional
    private ImportacionEntity actualizarImportacion(ImportacionEntity importacion) {
        return importacionRepository.save(importacion);
    }





    /**
     * Registra un error de importación
     */
    private void registrarError(ImportacionEntity importacion, Long linea, String campo, String error) {
        ErrorImportEntity errorEntity = new ErrorImportEntity(importacion, linea, campo, error);
        errorImportRepository.save(errorEntity);
    }

    /**
     * Genera el log de resultado de la importación
     */
    private String generarLogResultado(ImportacionEntity importacion, Map<String, Object> resultado) {
        StringBuilder log = new StringBuilder();

        log.append("=== LOG DE IMPORTACIÓN ===\n\n");
        log.append("Archivo: ").append(importacion.getNombreFichero()).append("\n");
        log.append("Usuario: ").append(importacion.getUsuarioImportacion()).append("\n");
        log.append("Fecha: ").append(importacion.getFechaImportacion()).append("\n");
        log.append("Formato: ").append(importacion.getFormatoFichero()).append("\n\n");

        log.append("=== RESULTADOS ===\n");
        log.append("Candidatos creados: ").append(resultado.get("candidatosCreados")).append("\n");
        log.append("Candidaturas creadas: ").append(resultado.get("candidaturasCreadas")).append("\n");
        log.append("Filas correctas: ").append(importacion.getFilasCorrectas()).append("\n");
        log.append("Filas incorrectas: ").append(importacion.getFilasIncorrectas()).append("\n");
        log.append("Importación exitosa: ").append(resultado.get("exito")).append("\n\n");

        @SuppressWarnings("unchecked")
        List<String> errores = (List<String>) resultado.get("errores");
        if (errores != null && !errores.isEmpty()) {
            log.append("=== ERRORES ENCONTRADOS ===\n");
            for (int i = 0; i < errores.size(); i++) {
                log.append(i + 1).append(". ").append(errores.get(i)).append("\n");
            }
        } else {
            log.append("=== IMPORTACIÓN EXITOSA ===\n");
            log.append("No se encontraron errores durante el procesamiento.\n");
            log.append("Todos los registros se importaron correctamente.\n");
        }

        log.append("\n--- FIN DEL LOG ---");

        return log.toString();
    }

    /**
     * Elimina una importación y sus errores asociados
     */
    public void deleteById(Long id) {
        ImportacionEntity importacion = findById(id)
            .orElseThrow(() -> new RuntimeException("Importación no encontrada"));
        
        // Eliminar errores asociados
        errorImportRepository.deleteByImportacion(importacion);
        
        // Eliminar importación
        importacionRepository.delete(importacion);
    }
}
