import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { HeaderComponent } from '../header/header.component';
import { FooterComponent } from '../footer/footer.component';

import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { TagModule } from 'primeng/tag';
import { DialogModule } from 'primeng/dialog';

import { FuncionalidadService, Funcionalidad } from '../../services/funcionalidad.service';
import { DropdownModule } from 'primeng/dropdown';
import { CheckboxModule } from 'primeng/checkbox';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ToastModule } from 'primeng/toast';
import { forkJoin } from 'rxjs';
import { ConfirmDialogModule } from 'primeng/confirmdialog';


@Component({
  selector: 'app-gestion-funcionalidades',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    HeaderComponent,
    FooterComponent,
    TableModule,
    ButtonModule,
    InputTextModule,
    TagModule,
    DialogModule,
    DropdownModule,
    CheckboxModule,
    ToastModule,
    ConfirmDialogModule,
  ],
  providers: [MessageService, ConfirmationService],
  templateUrl: './gestion-funcionalidades.component.html',
  styleUrls: ['./gestion-funcionalidades.component.scss']
})
export class GestionFuncionalidadesComponent implements OnInit {
  @ViewChild('dt') dt: any;

  funcionalidades: Funcionalidad[] = [];
  selectedFuncionalidades: Funcionalidad[] = [];
  selectedFuncionalidadesId: string | null = null;

  globalFilterValue = '';
  showModal = false;
  openedDropdownId: string | null = null;

  showMassiveActions = false;


  estados = [
    { label: 'Activo', value: 'true' },
    { label: 'Inactivo', value: 'false' },
  ];

  funcionalidadSeleccionada: Funcionalidad | null = null;

  constructor(private funcionalidadService: FuncionalidadService,  private confirmationService: ConfirmationService, private messageService: MessageService) {}

  ngOnInit(): void {
    this.cargarFuncionalidades();
  }

  cargarFuncionalidades(): void {
    this.funcionalidadService.getAll().subscribe(data => {
      this.funcionalidades = data;
    });
  }

  cambiarEstado(funcionalidad: Funcionalidad): void {
    const nuevoEstado = !funcionalidad.activo;
    this.funcionalidadService.toggleActivo(funcionalidad.id, nuevoEstado).subscribe({
      next: (res) => {
        funcionalidad.activo = res.activo;
      },
      error: (err) => {
        console.error('Error al cambiar el estado de la funcionalidad:', err);
      }
    });
  }

  eliminar(funcionalidad: Funcionalidad): void {
    this.confirmationService.confirm({
      message: `¿Estás seguro de que deseas eliminar la funcionalidad "${funcionalidad.nombre}"?`,
      header: 'Confirmar eliminación',
      icon: 'pi pi-exclamation-triangle',
      acceptLabel: 'Sí',
      rejectLabel: 'No',
      accept: () => {
        this.funcionalidadService.delete(funcionalidad.id).subscribe({
          next: () => {
            this.messageService.add({
              severity: 'success',
              summary: 'Eliminado',
              detail: 'Funcionalidad eliminada correctamente'
            });
            this.cargarFuncionalidades();
          },
          error: () => {
            this.messageService.add({
              severity: 'error',
              summary: 'Error',
              detail: 'No se pudo eliminar la funcionalidad'
            });
          }
        });
      }
    });
  }


  abrirModalEdicion(funcionalidad: Funcionalidad): void {
    this.funcionalidadSeleccionada = { ...funcionalidad };
    this.showModal = true;
  }

  abrirModalNueva(): void {
    this.funcionalidadSeleccionada = {
      id: 0,
      nombre: '',
      activo: true
    };
    this.showModal = true;
  }

guardar(): void {
  if (!this.funcionalidadSeleccionada) return;

  // Clonamos y le indicamos a TypeScript que ahora los campos son opcionales
  const funcionalidadParaEnviar: Partial<Funcionalidad> = { ...this.funcionalidadSeleccionada };

  // Eliminar el id si es 0 o undefined (para creación)
  if (!funcionalidadParaEnviar.id || funcionalidadParaEnviar.id === 0) {
    delete funcionalidadParaEnviar.id;
  }

  const esEdicion = !!this.funcionalidadSeleccionada.id;

  const obs = esEdicion
    ? this.funcionalidadService.update(this.funcionalidadSeleccionada.id!, funcionalidadParaEnviar as Funcionalidad)
    : this.funcionalidadService.create(funcionalidadParaEnviar as Funcionalidad);

    obs.subscribe({
      next: (respuesta) => {
        this.showModal = false;
        this.cargarFuncionalidades();
        if (!esEdicion) {
          this.messageService.add({
            severity: 'success',
            summary: 'Éxito',
            detail: 'Funcionalidad creada correctamente'
          });
        } else {
          this.messageService.add({
            severity: 'success',
            summary: 'Éxito',
            detail: 'Funcionalidad actualizada correctamente'
          });
        }
      },
      error: (err) => {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'No se pudo guardar la funcionalidad'
        });
      }
    });
  }

  cancelar(): void {
    this.funcionalidadSeleccionada = null;
    this.showModal = false;
  }

  clearFilters(): void {
    this.globalFilterValue = '';
    this.dt.filterGlobal('', 'contains');
    this.dt.reset();
  }

  getStatusClass(activo: boolean): string {
    return activo ? 'status-active' : 'status-inactive';
  }

  toggleDropdown(selectedFuncionalidadesId: string): void {
    this.openedDropdownId = this.openedDropdownId === selectedFuncionalidadesId ? null : selectedFuncionalidadesId;
  }

  getRowStyle(funcionalidad: Funcionalidad, index: number): string {
    const isSelected = this.selectedFuncionalidades.some(f => f.id === funcionalidad.id);
    return isSelected ? 'p-highlight' : (index % 2 === 0 ? 'row-par' : 'row-impar');
  }

  activateAll(): void {
    const requests = this.selectedFuncionalidades.map(funci => {
      funci.activo = true;
      return this.funcionalidadService.toggleActivo(funci.id, true);
    });

    forkJoin(requests).subscribe({
      next: () => {
        this.messageService.add({
          severity: 'success',
          summary: 'Activación completa',
          detail: 'Todas las funcionalidades seleccionadas fueron activadas correctamente.'
        });
        this.cargarFuncionalidades(); // recarga si es necesario
      },
      error: () => {
        this.messageService.add({
          severity: 'error',
          summary: 'Error en activación',
          detail: 'Ocurrió un error al activar algunas funcionalidades.'
        });
      }
  });
}

deactivateAll(): void {
  const requests = this.selectedFuncionalidades.map(funci => {
    funci.activo = false;
    return this.funcionalidadService.toggleActivo(funci.id, false);
  });

  forkJoin(requests).subscribe({
    next: () => {
        this.messageService.add({
          severity: 'success',
          summary: 'Desactivación completa',
          detail: 'Todas las funcionalidades seleccionadas fueron desactivadas correctamente.'
        });
        this.cargarFuncionalidades(); // recarga si es necesario
      },
      error: () => {
        this.messageService.add({
          severity: 'error',
          summary: 'Error en desactivación',
          detail: 'Ocurrió un error al desactivar algunas funcionalidades.'
        });
      }
    });
  }

}
