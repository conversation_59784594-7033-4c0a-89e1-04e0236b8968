package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.DeclaracionEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository.DeclaracionRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class DeclaracionService {

    @Autowired
    private DeclaracionRepository declaracionRepository;

    /**
     * Obtiene todas las declaraciones
     */
    public List<DeclaracionEntity> findAll() {
        return declaracionRepository.findAll();
    }

    /**
     * Busca una declaración por ID
     */
    public Optional<DeclaracionEntity> findById(Long id) {
        return declaracionRepository.findById(id);
    }

    /**
     * Guarda una declaración
     */
    public DeclaracionEntity save(DeclaracionEntity declaracion) {
        return declaracionRepository.save(declaracion);
    }

    /**
     * Elimina una declaración por ID
     */
    public void deleteById(Long id) {
        declaracionRepository.deleteById(id);
    }

    /**
     * Verifica si existe una declaración con el ID especificado
     */
    public boolean existsById(Long id) {
        return declaracionRepository.existsById(id);
    }

    /**
     * Cuenta el total de declaraciones
     */
    public long count() {
        return declaracionRepository.count();
    }

    /**
     * Crea una nueva declaración vacía
     */
    public DeclaracionEntity crearDeclaracionVacia() {
        DeclaracionEntity declaracion = new DeclaracionEntity();
        return save(declaracion);
    }
}
