package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.controller;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.EstadoCandidatoEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service.EstadoCandidatoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/estados-candidato")
@CrossOrigin(origins = { "http://localhost:4200", "https://localhost:4200" })
public class EstadoCandidatoController {

    @Autowired
    private EstadoCandidatoService estadoCandidatoService;

    /**
     * Obtiene todos los estados de candidato
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<List<EstadoCandidatoEntity>> getAll() {
        return ResponseEntity.ok(estadoCandidatoService.findAll());
    }

    /**
     * Obtiene un estado de candidato por ID
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<EstadoCandidatoEntity> getById(@PathVariable Long id) {
        return estadoCandidatoService.findById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Obtiene un estado de candidato por valor
     */
    @GetMapping("/valor/{valor}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<EstadoCandidatoEntity> getByValor(@PathVariable String valor) {
        return estadoCandidatoService.findByValor(valor)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Busca estados de candidato por texto
     */
    @GetMapping("/buscar")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<List<EstadoCandidatoEntity>> buscarPorTexto(@RequestParam String texto) {
        return ResponseEntity.ok(estadoCandidatoService.findByTextoContaining(texto));
    }

    /**
     * Crea un nuevo estado de candidato
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<EstadoCandidatoEntity> create(@RequestBody EstadoCandidatoEntity estadoCandidato) {
        try {
            EstadoCandidatoEntity saved = estadoCandidatoService.save(estadoCandidato);
            return ResponseEntity.ok(saved);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Actualiza un estado de candidato
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<EstadoCandidatoEntity> update(@PathVariable Long id, 
                                                       @RequestBody EstadoCandidatoEntity estadoCandidato) {
        return estadoCandidatoService.findById(id)
                .map(existing -> {
                    existing.setValor(estadoCandidato.getValor());
                    return ResponseEntity.ok(estadoCandidatoService.save(existing));
                })
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Elimina un estado de candidato
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        if (estadoCandidatoService.findById(id).isPresent()) {
            estadoCandidatoService.deleteById(id);
            return ResponseEntity.ok().build();
        }
        return ResponseEntity.notFound().build();
    }
}
