package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.CandidatoEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.CandidaturaEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.TipoCandidatoEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.EstadoCandidatoEntity;

@Repository
public interface CandidatoRepository extends JpaRepository<CandidatoEntity, Long> {

    // Métodos legacy eliminados según Script 2 (activo y circunscripcion no existen)

    // Nuevos métodos basados en el modelo completo
    List<CandidatoEntity> findByCandidatura(CandidaturaEntity candidatura);
    List<CandidatoEntity> findByCandidaturaId(Long candidaturaId);
    List<CandidatoEntity> findByCandidaturaIdAndOrden(Long candidaturaId, Long orden);
    List<CandidatoEntity> findByTipo(TipoCandidatoEntity tipo);
    List<CandidatoEntity> findByEstado(EstadoCandidatoEntity estado);

    /**
     * Busca candidatos por candidatura ordenados por orden
     */
    @Query("SELECT c FROM CandidatoEntity c WHERE c.candidatura.id = :candidaturaId ORDER BY c.orden ASC")
    List<CandidatoEntity> findByCandidaturaIdOrderByOrden(@Param("candidaturaId") Long candidaturaId);

    /**
     * Busca candidatos con filtros y paginación
     */
    @Query("SELECT c FROM CandidatoEntity c WHERE " +
           "(:candidaturaId IS NULL OR c.candidatura.id = :candidaturaId) AND " +
           "(:tipoId IS NULL OR c.tipo.id = :tipoId) AND " +
           "(:estadoId IS NULL OR c.estado.id = :estadoId) AND " +
           "(:nombre IS NULL OR LOWER(c.nombre) LIKE LOWER(CONCAT('%', :nombre, '%'))) AND " +
           "(:apellido1 IS NULL OR LOWER(c.apellido1) LIKE LOWER(CONCAT('%', :apellido1, '%')))")
    Page<CandidatoEntity> findWithFilters(@Param("candidaturaId") Long candidaturaId,
                                         @Param("tipoId") Long tipoId,
                                         @Param("estadoId") Long estadoId,
                                         @Param("nombre") String nombre,
                                         @Param("apellido1") String apellido1,
                                         Pageable pageable);

    /**
     * Busca candidatos por texto en nombre o apellidos
     */
    @Query("SELECT c FROM CandidatoEntity c WHERE " +
           "LOWER(c.nombre) LIKE LOWER(CONCAT('%', :texto, '%')) OR " +
           "LOWER(c.apellido1) LIKE LOWER(CONCAT('%', :texto, '%')) OR " +
           "LOWER(c.apellido2) LIKE LOWER(CONCAT('%', :texto, '%'))")
    List<CandidatoEntity> findByTextoContaining(@Param("texto") String texto);

    /**
     * Verifica si existe un candidato con el mismo nombre y apellidos en una candidatura
     */
    @Query("SELECT COUNT(c) > 0 FROM CandidatoEntity c WHERE " +
           "c.candidatura.id = :candidaturaId AND " +
           "LOWER(c.nombre) = LOWER(:nombre) AND " +
           "LOWER(c.apellido1) = LOWER(:apellido1) AND " +
           "(:apellido2 IS NULL OR LOWER(c.apellido2) = LOWER(:apellido2)) AND " +
           "(:excludeId IS NULL OR c.id != :excludeId)")
    boolean existsDuplicateInCandidatura(@Param("candidaturaId") Long candidaturaId,
                                        @Param("nombre") String nombre,
                                        @Param("apellido1") String apellido1,
                                        @Param("apellido2") String apellido2,
                                        @Param("excludeId") Long excludeId);

    /**
     * Busca el siguiente orden disponible en una candidatura
     */
    @Query("SELECT COALESCE(MAX(c.orden), 0) + 1 FROM CandidatoEntity c WHERE c.candidatura.id = :candidaturaId")
    Long findNextOrdenInCandidatura(@Param("candidaturaId") Long candidaturaId);

    /**
     * Cuenta candidatos por tipo en una candidatura
     */
    @Query("SELECT COUNT(c) FROM CandidatoEntity c WHERE c.candidatura.id = :candidaturaId AND c.tipo.id = :tipoId")
    Long countByCandidaturaIdAndTipoId(@Param("candidaturaId") Long candidaturaId, @Param("tipoId") Long tipoId);
}
