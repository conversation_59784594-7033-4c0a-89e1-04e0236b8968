import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ConvocatoriaService {
  private apiUrl = '/api/convocatorias/convocatoria';

  constructor(private http: HttpClient) {}

  getFechaCancelacion(): Observable<string> {
    return this.http.get<string>(`${this.apiUrl}/fecha-cancelacion`);
  }
}
