package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.FormacionPoliticaEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface FormacionPoliticaRepository extends JpaRepository<FormacionPoliticaEntity, Long> {

    Optional<FormacionPoliticaEntity> findByNombre(String nombre);
    Optional<FormacionPoliticaEntity> findBySiglas(String siglas);
    Optional<FormacionPoliticaEntity> findByCodigoInterno(String codigoInterno);

    /**
     * Verifica si existe una formación política con el nombre especificado
     */
    boolean existsByNombre(String nombre);

    /**
     * Verifica si existe una formación política con las siglas especificadas
     */
    boolean existsBySiglas(String siglas);

    /**
     * Verifica si existe una formación política con el código interno especificado
     */
    boolean existsByCodigoInterno(String codigoInterno);

    /**
     * Busca formaciones políticas que contengan el texto en nombre o siglas
     */
    @Query("SELECT f FROM FormacionPoliticaEntity f WHERE " +
           "LOWER(f.nombre) LIKE LOWER(CONCAT('%', :texto, '%')) OR " +
           "LOWER(f.siglas) LIKE LOWER(CONCAT('%', :texto, '%'))")
    List<FormacionPoliticaEntity> findByTextoContaining(@Param("texto") String texto);

    /**
     * Busca formaciones políticas ordenadas por nombre
     */
    List<FormacionPoliticaEntity> findAllByOrderByNombreAsc();

    /**
     * Verifica duplicados excluyendo un ID específico
     */
    @Query("SELECT COUNT(f) > 0 FROM FormacionPoliticaEntity f WHERE " +
           "(LOWER(f.nombre) = LOWER(:nombre) OR LOWER(f.siglas) = LOWER(:siglas)) AND " +
           "(:excludeId IS NULL OR f.id != :excludeId)")
    boolean existsDuplicate(@Param("nombre") String nombre,
                           @Param("siglas") String siglas,
                           @Param("excludeId") Long excludeId);
}
