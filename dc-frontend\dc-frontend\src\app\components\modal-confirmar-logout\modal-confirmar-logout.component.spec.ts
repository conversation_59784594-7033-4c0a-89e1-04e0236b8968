import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ModalConfirmarLogoutComponent } from './modal-confirmar-logout.component';

describe('ModalConfirmarLogoutComponent', () => {
  let component: ModalConfirmarLogoutComponent;
  let fixture: ComponentFixture<ModalConfirmarLogoutComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ModalConfirmarLogoutComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(ModalConfirmarLogoutComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
