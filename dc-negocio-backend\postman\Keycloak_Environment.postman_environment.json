{"id": "keycloak-datos-candidatos-env", "name": "Keycloak - <PERSON><PERSON>", "values": [{"key": "keycloak_url", "value": "https://***************:8453", "type": "default", "enabled": true}, {"key": "keycloak_realm", "value": "parlamento", "type": "default", "enabled": true}, {"key": "keycloak_username", "value": "TU_USUARIO_AQUI", "type": "default", "enabled": true}, {"key": "keycloak_password", "value": "TU_PASSWORD_AQUI", "type": "secret", "enabled": true}, {"key": "backend_url", "value": "http://localhost:8080", "type": "default", "enabled": true}, {"key": "access_token", "value": "", "type": "default", "enabled": true}, {"key": "refresh_token", "value": "", "type": "default", "enabled": true}, {"key": "token_type", "value": "", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2025-01-10T12:00:00.000Z", "_postman_exported_using": "Postman/10.0.0"}