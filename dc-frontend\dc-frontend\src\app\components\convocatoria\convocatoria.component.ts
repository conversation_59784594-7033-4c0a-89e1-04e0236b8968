import { Component, OnInit, ViewChild } from '@angular/core';
import { HeaderComponent } from "../header/header.component";
import { FooterComponent } from "../footer/footer.component";
import { CommonModule } from '@angular/common';
// Importa la interfaz y el servicio del backend
import { Convocatoria, ConvocatoriaService } from '../../services/convocatoriaService';
// Si vas a reintroducir los logs, necesitarás crear este servicio:
// import { LogService, LogEntry } from '../../services/logService';

import { ButtonModule } from "primeng/button";
import { FormsModule } from '@angular/forms';
import { CalendarModule } from "primeng/calendar";
import { DialogModule } from 'primeng/dialog';
import { CheckboxModule } from 'primeng/checkbox';
import { InputTextModule } from 'primeng/inputtext';
import { DatePickerModule } from "primeng/datepicker";
// Si vas a permitir subir el logotipo, necesitarás FileUploadModule
// import { FileUploadModule } from 'primeng/fileupload';

@Component({
  selector: 'app-convocatoria',
  standalone: true,
  imports: [
    HeaderComponent,
    FooterComponent,
    ButtonModule,
    FormsModule,
    CalendarModule,
    DialogModule,
    CheckboxModule,
    InputTextModule,
    DatePickerModule,
    CommonModule
  ],
  templateUrl: './convocatoria.component.html',
  styleUrl: './convocatoria.component.scss'
})
export class ConvocatoriaComponent implements OnInit {
  displayEditModal: boolean = false;
  originalConvocatoria: any;
  modoEditar: boolean = false;

  constructor(
    private convocatoriaService: ConvocatoriaService,
    // private logService: LogService // Descomentar si reintroduces los logs
  ) { }

  @ViewChild('dt', { static: false }) dt!: any; // Si no usas p-table, puedes eliminar esto

  // Objeto que contendrá los datos de la convocatoria y se vinculará al formulario
  convocatoria: Convocatoria = {
    id: 0,
    fecha: null,
    fechaElecciones: null,
    fechaInicioPresentacion: null,
    fechaFinPresentacion: null,
    fechaInicioPublicacionBoja: null,
    fechaIrregularidades: null,
    fechaInicioSubsanacion: null,
    fechaFinSubsanacion: null,
    fechaProclamacion: null,
    fechaPublicacionProclamada: null,
    fechaInicioDeclaracion: null,
    fechaFinDeclaracion: null,
    fechaInicioPublicacionInternet: null,
    fechaFinPublicacionInternet: null,
    fechaCancelacion: null,
    logotipo: ''
  };

  // globalFilterValue: string = ''; // Si no usas p-table, puedes eliminar esto

  ngOnInit(): void {
    if (this.convocatoria.id > 0) {
      this.loadConvocatoriaData(this.convocatoria.id);
    } else {
      console.log('Creando nueva convocatoria. No se carga desde backend.');
    }
  }

  loadConvocatoriaData(id: number): void {
    this.convocatoriaService.getById(id).subscribe({
      next: (data: Convocatoria) => {
        // Es crucial convertir las strings de fecha (si el backend las envía así)
        // a objetos Date para que p-calendar funcione correctamente.
        this.convocatoria = this.convertDatesToObjects(data);
        console.log('Datos de convocatoria cargados desde el backend:', this.convocatoria);
      },
      error: (err) => {
        console.error('Error al cargar la convocatoria desde el backend:', err);
        alert('Error al cargar los datos de la convocatoria. Asegúrate de que el backend esté funcionando y que exista una convocatoria con el ID especificado.');
        // Puedes inicializar con valores por defecto si no se carga nada
        this.convocatoria = { ...this.convocatoria, id: id };
      }
    });
  }

  private convertDatesToObjects(data: Convocatoria): Convocatoria {
    const newData: Convocatoria = { ...data };
    // Actualiza esta lista con TODOS los campos de fecha de tu interfaz de backend
    const dateFields: Array<keyof Convocatoria> = [
      'fecha', 'fechaElecciones', 'fechaInicioPresentacion', 'fechaFinPresentacion',
      'fechaInicioPublicacionBoja',
      'fechaIrregularidades', 'fechaInicioSubsanacion', 'fechaFinSubsanacion',
      'fechaProclamacion', 'fechaPublicacionProclamada', 'fechaInicioDeclaracion',
      'fechaFinDeclaracion', 'fechaInicioPublicacionInternet', 'fechaFinPublicacionInternet',
      'fechaCancelacion'
    ];

    dateFields.forEach(field => {
      const dateValue = (newData as any)[field];
      if (typeof dateValue === 'string' && dateValue !== '') {
        (newData as any)[field] = new Date(dateValue);
      } else if (dateValue === null) {
        (newData as any)[field] = undefined; // PrimeNG Calendar prefiere undefined para vacío
      }
    });
    return newData;
  }

  private convertDatesToStrings(data: Convocatoria): Convocatoria {
    const dataToSend: Convocatoria = { ...data };

    // Actualiza esta lista con TODOS los campos de fecha de tu interfaz de backend
    const dateFields: Array<keyof Convocatoria> = [
      'fecha', 'fechaElecciones', 'fechaInicioPresentacion', 'fechaFinPresentacion', 'fechaInicioPublicacionBoja',
      'fechaIrregularidades', 'fechaInicioSubsanacion', 'fechaFinSubsanacion',
      'fechaProclamacion', 'fechaPublicacionProclamada', 'fechaInicioDeclaracion',
      'fechaFinDeclaracion', 'fechaInicioPublicacionInternet', 'fechaFinPublicacionInternet',
      'fechaCancelacion'
    ];

    dateFields.forEach(field => {
      const dateValue = (dataToSend as any)[field];
      if (dateValue instanceof Date && !isNaN(dateValue.getTime())) {
        (dataToSend as any)[field] = dateValue.toISOString();
      } else {
        (dataToSend as any)[field] = null; // Enviar null si la fecha no está definida
      }
    });
    return dataToSend;
  }

  guardarCambios(): void {
    const changesDetected = this.originalConvocatoria &&
      JSON.stringify(this.convocatoria) !== JSON.stringify(this.originalConvocatoria);

    if (changesDetected) {
      const convocatoriaToUpdate = this.convertDatesToStrings(this.convocatoria);

      this.convocatoriaService.update(this.convocatoria.id, convocatoriaToUpdate).subscribe({
        next: (res) => {
          console.log('Convocatoria actualizada con éxito en el backend:', res);
          this.loadConvocatoriaData(this.convocatoria.id);
          this.displayEditModal = false;
          alert('Convocatoria actualizada correctamente.');
        },
        error: (err) => {
          console.error('Error al actualizar la convocatoria en el backend:', err);
          alert('Error al guardar los cambios. Por favor, inténtalo de nuevo.');
        }
      });
    } else {
      alert('No se detectaron cambios en la convocatoria.');
      this.displayEditModal = false;
    }
    console.log('Datos guardados:', this.convocatoria);
    this.modoEditar = false;
  }

  // Funciones que pueden no ser relevantes si este componente solo maneja una convocatoria
  clearFilters(): void {
    console.log('Limpiar filtros clicado');
    // if (this.dt) { this.dt.filterGlobal('', 'contains'); this.dt.reset(); }
    this.loadConvocatoriaData(this.convocatoria.id);
  }

  addNew() {
    console.log('Método addNew llamado.');
  }
}