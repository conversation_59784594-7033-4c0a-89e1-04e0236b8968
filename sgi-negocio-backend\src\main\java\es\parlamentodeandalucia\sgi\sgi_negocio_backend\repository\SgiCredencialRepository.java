package es.parlamentodeandalucia.sgi.sgi_negocio_backend.repository;

import es.parlamentodeandalucia.sgi.sgi_negocio_backend.entity.SgiCredencialEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repositorio para la entidad SgiCredencialEntity
 * Proporciona métodos de acceso a datos para el User Storage SPI
 */
@Repository
public interface SgiCredencialRepository extends JpaRepository<SgiCredencialEntity, Long> {

    /**
     * Buscar usuario por username (para autenticación)
     */
    Optional<SgiCredencialEntity> findByUsername(String username);

    /**
     * Buscar usuario por email
     */
    Optional<SgiCredencialEntity> findByEmail(String email);

    /**
     * Buscar usuario por NIF
     */
    Optional<SgiCredencialEntity> findByNif(String nif);

    /**
     * Verificar si existe un usuario con el username
     */
    boolean existsByUsername(String username);

    /**
     * Verificar si existe un usuario con el email
     */
    boolean existsByEmail(String email);

    /**
     * Verificar si existe un usuario con el NIF
     */
    boolean existsByNif(String nif);

    /**
     * Buscar usuarios habilitados
     */
    List<SgiCredencialEntity> findByEnabledTrue();

    /**
     * Buscar usuarios por estado de cuenta (habilitados y no bloqueados)
     */
    @Query("SELECT c FROM SgiCredencialEntity c WHERE c.enabled = true AND (c.accountLocked = false OR c.accountLocked IS NULL)")
    List<SgiCredencialEntity> findActiveUsers();

    /**
     * Buscar usuarios por nombre o apellidos (para búsquedas)
     */
    @Query("SELECT c FROM SgiCredencialEntity c WHERE " +
           "LOWER(c.firstName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(c.lastName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(c.nombre) LIKE LOWER(CONCAT('%', :searchTerm, '%'))")
    List<SgiCredencialEntity> findByNameContaining(@Param("searchTerm") String searchTerm);

    /**
     * Contar usuarios activos
     */
    @Query("SELECT COUNT(c) FROM SgiCredencialEntity c WHERE c.enabled = true AND (c.accountLocked = false OR c.accountLocked IS NULL)")
    long countActiveUsers();

    /**
     * Buscar usuarios con cuentas bloqueadas
     */
    List<SgiCredencialEntity> findByAccountLockedTrue();

    /**
     * Buscar usuarios con intentos fallidos de login
     */
    @Query("SELECT c FROM SgiCredencialEntity c WHERE c.failedLoginAttempts > 0")
    List<SgiCredencialEntity> findUsersWithFailedAttempts();
}
