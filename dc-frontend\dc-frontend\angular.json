{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"dc-frontend": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/dc-frontend", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss", "@fortawesome/fontawesome-free/css/all.min.css", "primeicons/primeicons.css"], "scripts": [], "outputHashing": "all"}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "1MB", "maximumError": "1.86MB"}, {"type": "anyComponentStyle", "maximumWarning": "8kB", "maximumError": "12kB"}], "outputHashing": "all", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}]}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.development.ts"}]}, "lab": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/enviroment.lab.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "budgets": [{"type": "initial", "maximumWarning": "1MB", "maximumError": "1.86MB"}, {"type": "anyComponentStyle", "maximumWarning": "8kb", "maximumError": "12kb"}]}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "dc-frontend:build:production"}, "development": {"buildTarget": "dc-frontend:build:development"}, "lab": {"buildTarget": "dc-frontend:build:lab"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["src/styles.scss"], "scripts": []}}}}}, "cli": {"analytics": false}}