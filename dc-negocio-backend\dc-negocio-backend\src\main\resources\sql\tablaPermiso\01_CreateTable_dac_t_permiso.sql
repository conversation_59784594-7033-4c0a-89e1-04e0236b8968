CREATE TABLE dac_t_permiso (
    dac_id_permiso BIGSERIAL PRIMARY KEY,
    dac_tx_codigo VARCHAR(50) NOT NULL,
    dac_tx_descripcion VARCHAR(255) NOT NULL,
    dac_bo_activo BOOLEAN NOT NULL,
    dac_id_funcionalidad BIGINT NOT NULL,
    CONSTRAINT fk_permiso_funcionalidad
        FOREIGN KEY (dac_id_funcionalidad)
        REFERENCES dac_t_funcionalidad (dac_id_funcionalidad)
        ON UPDATE CASCADE
        ON DELETE RESTRICT
);