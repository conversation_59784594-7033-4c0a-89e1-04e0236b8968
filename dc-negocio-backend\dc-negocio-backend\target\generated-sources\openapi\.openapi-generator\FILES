README.md
pom.xml
src/main/java/es/parlamentodeandalucia/datoscandidatos/api/AdministracinApi.java
src/main/java/es/parlamentodeandalucia/datoscandidatos/api/ApiUtil.java
src/main/java/es/parlamentodeandalucia/datoscandidatos/api/DefaultApi.java
src/main/java/es/parlamentodeandalucia/datoscandidatos/dto/Candidato.java
src/main/java/es/parlamentodeandalucia/datoscandidatos/dto/Candidatura.java
src/main/java/es/parlamentodeandalucia/datoscandidatos/dto/CandidaturaEstadoCandidatura.java
src/main/java/es/parlamentodeandalucia/datoscandidatos/dto/CandidaturaInput.java
src/main/java/es/parlamentodeandalucia/datoscandidatos/dto/Circunscripcion.java
src/main/java/es/parlamentodeandalucia/datoscandidatos/dto/Convocatoria.java
src/main/java/es/parlamentodeandalucia/datoscandidatos/dto/FormacionPolitica.java
src/main/java/es/parlamentodeandalucia/datoscandidatos/dto/FormacionPoliticaInput.java
src/main/java/es/parlamentodeandalucia/datoscandidatos/dto/Importacion.java
src/main/java/es/parlamentodeandalucia/datoscandidatos/dto/ImportacionesProcesarCsvPost200Response.java
src/main/java/es/parlamentodeandalucia/datoscandidatos/dto/ImportacionesProcesarCsvPost200ResponseErroresInner.java
src/main/java/es/parlamentodeandalucia/datoscandidatos/dto/ImportacionesProcesarCsvPost400Response.java
src/main/java/es/parlamentodeandalucia/datoscandidatos/dto/Plantilla.java
src/main/java/es/parlamentodeandalucia/datoscandidatos/dto/Representante.java
src/main/java/es/parlamentodeandalucia/datoscandidatos/dto/ResetConfirmacion.java
src/main/java/es/parlamentodeandalucia/datoscandidatos/dto/Rol.java
src/main/java/es/parlamentodeandalucia/datoscandidatos/dto/Usuario.java
