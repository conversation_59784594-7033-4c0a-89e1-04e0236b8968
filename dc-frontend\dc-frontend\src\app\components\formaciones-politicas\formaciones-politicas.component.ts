import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { HttpClientModule } from '@angular/common/http';
import { forkJoin } from 'rxjs'; // Import forkJoin for concurrent API calls
import { ConfirmationService, MessageService } from 'primeng/api';

import { HeaderComponent } from '../header/header.component';
import { FooterComponent } from '../footer/footer.component';

import { FormacionPoliticaService, PoliticalFormationBackend } from '../../services/formacion-politica.service';
import { CircunscripcionesService, Circunscripcion } from '../../services/circunscripcionService';
import { CandidaturasService, CandidaturaDto } from '../../services/candidaturas.service'; // Import CandidaturasService

// PrimeNG UI Modules
import { TableModule, Table } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { CheckboxModule } from 'primeng/checkbox';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
import { PaginatorModule } from 'primeng/paginator';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { SidebarModule } from 'primeng/sidebar';
import { RippleModule } from 'primeng/ripple';


// Interfaz para el modelo visual/formulario
interface DisplayPoliticalFormation {
  id?: number;
  code: string;
  name: string;
  acronym: string;
  constituency: string; // Will now be populated from Candidaturas
  activeCandidacies: boolean; // Will now be populated from Candidaturas
}

interface DropdownOption {
  label: string;
  value: any;
}


@Component({
  selector: 'app-formaciones-politicas',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    HttpClientModule,
    HeaderComponent,
    FooterComponent,
    // PrimeNG
    TableModule,
    ButtonModule,
    InputTextModule,
    DialogModule,
    DropdownModule,
    CheckboxModule,
    IconFieldModule,
    InputIconModule,
    PaginatorModule,
    ConfirmDialogModule,
    SidebarModule,
    RippleModule
  ],
  providers: [ConfirmationService, MessageService],
  templateUrl: './formaciones-politicas.component.html',
  styleUrls: ['./formaciones-politicas.component.scss']
})
export class FormacionesPoliticasComponent implements OnInit {
  @ViewChild('dt') dt: Table | undefined;

  politicalFormations: DisplayPoliticalFormation[] = [];
  selectedRows: DisplayPoliticalFormation[] = [];

  showFormationSidebar = false;
  sidebarTitle = 'Añadir Formación Política';

  newFormation: DisplayPoliticalFormation = {
    code: '',
    name: '',
    acronym: '',
    constituency: '',
    activeCandidacies: true
  };

  constituencies: DropdownOption[] = [];
  globalFilterValue = '';
  showMassiveActions = false;
  openedDropdownId: number | null = null;

  constructor(
    private formacionPoliticaService: FormacionPoliticaService,
    private circunscripcionesService: CircunscripcionesService,
    private candidaturasService: CandidaturasService, // Inject CandidaturasService
    private confirmationService: ConfirmationService,
    private messageService: MessageService
  ) {}

  ngOnInit(): void {
    // Load all necessary data concurrently
    this.loadPoliticalFormationsAndRelatedData();
  }

  /**
   * Loads political formations, constituencies, and candidacies concurrently,
   * then maps the data to the DisplayPoliticalFormation interface,
   * enriching each political formation with its associated constituency
   * and active candidacy status.
   */
  loadPoliticalFormationsAndRelatedData(): void {
    forkJoin([
      this.formacionPoliticaService.getAll(),
      this.circunscripcionesService.getAll(),
      this.candidaturasService.getAll()
    ]).subscribe({
      next: ([formationsData, constituenciesData, candidaciesData]) => {
        // Populate constituencies dropdown data
        this.constituencies = constituenciesData.map(c => ({
          label: c.nombre,
          value: c.nombre
        }));
        console.log('Constituencies loaded:', this.constituencies);

        // Create a map for quick lookup of constituency names by ID
        const constituencyIdToNameMap = new Map<number, string>();
        constituenciesData.forEach(c => constituencyIdToNameMap.set(c.id, c.nombre));

        // Create a map to easily check for candidacies per political formation
        // And to store the constituency name if a candidacy exists
        const formationCandidacyInfo = new Map<number, { hasActive: boolean, constituencyName: string }>();

        candidaciesData.forEach(candidacy => {
          if (candidacy.formacionPolitica?.id && candidacy.circunscripcion?.id) {
            const formationId = candidacy.formacionPolitica.id;
            const constituencyName = constituencyIdToNameMap.get(candidacy.circunscripcion.id) || 'N/A';

            // If this formation already has candidacy info, update if necessary
            // Otherwise, set it. We prioritize setting a constituency if one is found.
            if (!formationCandidacyInfo.has(formationId)) {
                formationCandidacyInfo.set(formationId, { hasActive: true, constituencyName: constituencyName });
            } else {
                // If the formation already has an entry, just ensure hasActive is true
                // and potentially update constituency if 'N/A' was previously set
                const existingInfo = formationCandidacyInfo.get(formationId)!;
                existingInfo.hasActive = true;
                if (existingInfo.constituencyName === 'N/A' && constituencyName !== 'N/A') {
                    existingInfo.constituencyName = constituencyName;
                }
            }
          }
        });


        // Map backend data to DisplayPoliticalFormation
        this.politicalFormations = formationsData.map(f => {
          const candidacyInfo = formationCandidacyInfo.get(f.id!);
          return {
            id: f.id,
            code: f.codigoInterno,
            name: f.nombre,
            acronym: f.siglas,
            // If candidacy info exists, use its constituency name, otherwise 'N/A'
            constituency: candidacyInfo ? candidacyInfo.constituencyName : 'N/A',
            // If candidacy info exists and has active candidacies, set to true, otherwise false
            activeCandidacies: candidacyInfo ? candidacyInfo.hasActive : false
          };
        });

        this.dt?.reset();
        this.selectedRows = [];
      },
      error: (err) => {
        console.error('Error loading political formations or related data:', err);
        this.messageService.add({severity:'error', summary:'Error', detail:'Failed to load data.'});
      }
    });
  }

  // Renamed loadPoliticalFormations to loadPoliticalFormationsAndRelatedData
  // and loadConstituencies is now effectively handled within the new method.
  // The original loadConstituencies method is no longer needed as a separate call.

  clearFilters(): void {
    this.globalFilterValue = '';
    this.dt?.clear();
  }

  isRowSelectable(row: { data: DisplayPoliticalFormation }): boolean {
    return true;
  }

  openFormationSidebar(formation: DisplayPoliticalFormation | null): void {
    if (formation) {
      this.sidebarTitle = 'Editar Formación Política';
      this.newFormation = { ...formation };
    } else {
      this.sidebarTitle = 'Añadir Formación Política';
      this.newFormation = {
        code: '',
        name: '',
        acronym: '',
        constituency: '',
        activeCandidacies: true
      };
    }
    this.showFormationSidebar = true;
    this.openedDropdownId = null;
  }

  saveFormation(): void {
    console.log('Saving formation:', this.newFormation);
    // Here you would typically call your formacionPoliticaService.create or update
    // based on whether newFormation.id exists.
    // After successful save, reload the data:
    // this.loadPoliticalFormationsAndRelatedData();
    this.showFormationSidebar = false;
    this.messageService.add({severity:'success', summary:'Success', detail:'Formation saved successfully!'});
  }

  toggleDropdown(id: number): void {
    this.openedDropdownId = this.openedDropdownId === id ? null : id;
  }

  activateSelected(): void {
    console.log('Activating selected formations:', this.selectedRows);
    // Add logic to update activeCandidacies in the backend if applicable
    this.messageService.add({severity:'info', summary:'Info', detail:'Selected formations activated.'});
    this.showMassiveActions = false;
  }

  deactivateSelected(): void {
    console.log('Deactivating selected formations:', this.selectedRows);
    // Add logic to update activeCandidacies in the backend if applicable
    this.messageService.add({severity:'info', summary:'Info', detail:'Selected formations deactivated.'});
    this.showMassiveActions = false;
  }

  deactivateThisFormation(id: number): void {
    console.log(`Deactivating formation with id: ${id}`);
    // You would call a service method to deactivate this specific formation
    // For demonstration, update locally and show message
    const index = this.politicalFormations.findIndex(f => f.id === id);
    if (index !== -1) {
      this.politicalFormations[index].activeCandidacies = false;
      this.messageService.add({severity:'success', summary:'Success', detail:`Formation ${id} deactivated.`});
    }
    this.openedDropdownId = null;
  }

  activateThisFormation(id: number): void {
    console.log(`Activating formation with id: ${id}`);
    // You would call a service method to activate this specific formation
    // For demonstration, update locally and show message
    const index = this.politicalFormations.findIndex(f => f.id === id);
    if (index !== -1) {
      this.politicalFormations[index].activeCandidacies = true;
      this.messageService.add({severity:'success', summary:'Success', detail:`Formation ${id} activated.`});
    }
    this.openedDropdownId = null;
  }
}
