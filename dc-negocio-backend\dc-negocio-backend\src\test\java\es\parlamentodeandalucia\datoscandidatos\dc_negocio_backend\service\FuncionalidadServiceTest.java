package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.FuncionalidadEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository.FuncionalidadRepository;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository.PermisoRepository;

@ExtendWith(MockitoExtension.class)
public class FuncionalidadServiceTest {

    @Mock
    private FuncionalidadRepository funcionalidadRepository;

    @Mock
    private PermisoRepository permisoRepository;

    @InjectMocks
    private FuncionalidadService funcionalidadService;

    private FuncionalidadEntity funcionalidad;

    @BeforeEach
    void setUp() {
        funcionalidad = new FuncionalidadEntity();
        funcionalidad.setId(1L);
    	funcionalidad.setCodigo("Consultar datos");
        funcionalidad.setDescripcion("Consultar descripcion");
    }

    @Test
    void testFindAll() {
        when(funcionalidadRepository.findAll()).thenReturn(Arrays.asList(funcionalidad));

        List<FuncionalidadEntity> result = funcionalidadService.findAll();

        assertThat(result).hasSize(1);
        assertThat(result.get(0).getCodigo()).isEqualTo("Consultar datos");
        assertThat(result.get(0).getDescripcion()).isEqualTo("Consultar descripcion");
        verify(funcionalidadRepository, times(1)).findAll();
    }

    @Test
    void testFindById_existingId() {
        when(funcionalidadRepository.findById(1L)).thenReturn(Optional.of(funcionalidad));

        Optional<FuncionalidadEntity> result = funcionalidadService.findById(1L);

        assertThat(result).isPresent();
        assertThat(result.get().getId()).isEqualTo(1L);
        verify(funcionalidadRepository).findById(1L);
    }

    @Test
    void testFindById_nonExistingId() {
        when(funcionalidadRepository.findById(999L)).thenReturn(Optional.empty());

        Optional<FuncionalidadEntity> result = funcionalidadService.findById(999L);

        assertThat(result).isNotPresent();
        verify(funcionalidadRepository).findById(999L);
    }

    @Test
    void testSaveFuncionalidad() {
        when(funcionalidadRepository.save(funcionalidad)).thenReturn(funcionalidad);

        FuncionalidadEntity result = funcionalidadService.save(funcionalidad);

        assertThat(result).isNotNull();
        assertThat(result.getCodigo()).isEqualTo("Consultar datos");
        assertThat(result.getDescripcion()).isEqualTo("Consultar descripcion");
        verify(funcionalidadRepository).save(funcionalidad);
    }

    @Test
    void testUpdateFuncionalidad_existingId() {
        FuncionalidadEntity updated = new FuncionalidadEntity();
        updated.setCodigo("Actualizar datos");
        updated.setDescripcion("Consultar descripcion");

        when(funcionalidadRepository.findById(1L)).thenReturn(Optional.of(funcionalidad));
        when(funcionalidadRepository.save(any())).thenAnswer(i -> i.getArgument(0));

        FuncionalidadEntity result = funcionalidadService.updateFuncionalidad(1L, updated);

        assertThat(result.getCodigo()).isEqualTo("Actualizar datos");
        assertThat(result.getDescripcion()).isEqualTo("Consultar descripcion");
        verify(funcionalidadRepository).findById(1L);
        verify(funcionalidadRepository).save(any());
    }

    @Test
    void testUpdateFuncionalidad_nonExistingId() {
        FuncionalidadEntity updated = new FuncionalidadEntity();
        updated.setCodigo("Actualizar datos");
        updated.setDescripcion("Actualizar descripcion");

        when(funcionalidadRepository.findById(999L)).thenReturn(Optional.empty());

        assertThatThrownBy(() -> funcionalidadService.updateFuncionalidad(999L, updated))
                .isInstanceOf(RuntimeException.class)
                .hasMessageContaining("Funcionalidad no encontrada");

        verify(funcionalidadRepository).findById(999L);
        verify(funcionalidadRepository, never()).save(any());
    }
    
    @Test
    void testDeleteById_nonExistingId() {
        when(funcionalidadRepository.findById(999L)).thenReturn(Optional.empty());

        assertThatThrownBy(() -> funcionalidadService.deleteById(999L))
                .isInstanceOf(RuntimeException.class)
                .hasMessageContaining("Funcionalidad no encontrada");

        verify(funcionalidadRepository, never()).delete(any());
        verify(permisoRepository, never()).existsByFuncionalidadId(any());
    }

    @Test
    void testDeleteById_withPermissions() {
        when(funcionalidadRepository.findById(1L)).thenReturn(Optional.of(funcionalidad));
        when(permisoRepository.existsByFuncionalidadId(1L)).thenReturn(true);

        assertThatThrownBy(() -> funcionalidadService.deleteById(1L))
                .isInstanceOf(RuntimeException.class)
                .hasMessageContaining("tiene permisos asociados");

        verify(funcionalidadRepository, never()).delete(any());
    }

    @Test
    void testDeleteById_withoutPermissions() {
        when(funcionalidadRepository.findById(1L)).thenReturn(Optional.of(funcionalidad));
        when(permisoRepository.existsByFuncionalidadId(1L)).thenReturn(false);

        funcionalidadService.deleteById(1L);

        verify(funcionalidadRepository).delete(funcionalidad);
    }

}
