package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.CandidaturaEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.CircunscripcionEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.FormacionPoliticaEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.EstadoCandidaturaEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.TipoCandidaturaEntity;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CandidaturaRepository extends JpaRepository<CandidaturaEntity, Long> {

    // Método legacy para compatibilidad
    long countByCircunscripcion(CircunscripcionEntity circunscripcion);

    // Nuevos métodos basados en el modelo completo
    List<CandidaturaEntity> findByFormacionPolitica(FormacionPoliticaEntity formacionPolitica);
    List<CandidaturaEntity> findByCircunscripcion(CircunscripcionEntity circunscripcion);
    List<CandidaturaEntity> findByEstadoCandidatura(EstadoCandidaturaEntity estadoCandidatura);
    List<CandidaturaEntity> findByTipoCandidatura(TipoCandidaturaEntity tipoCandidatura);

    /**
     * Busca candidaturas con filtros y paginación
     */
    @Query("SELECT c FROM CandidaturaEntity c WHERE " +
           "(:formacionPoliticaId IS NULL OR c.formacionPolitica.id = :formacionPoliticaId) AND " +
           "(:circunscripcionId IS NULL OR c.circunscripcion.id = :circunscripcionId) AND " +
           "(:estadoCandidaturaId IS NULL OR c.estadoCandidatura.id = :estadoCandidaturaId) AND " +
           "(:tipoCandidaturaId IS NULL OR c.tipoCandidatura.id = :tipoCandidaturaId)")
    Page<CandidaturaEntity> findWithFilters(@Param("formacionPoliticaId") Long formacionPoliticaId,
                                           @Param("circunscripcionId") Long circunscripcionId,
                                           @Param("estadoCandidaturaId") Long estadoCandidaturaId,
                                           @Param("tipoCandidaturaId") Long tipoCandidaturaId,
                                           Pageable pageable);

    /**
     * Busca candidaturas con sus candidatos cargados
     */
    @Query("SELECT DISTINCT c FROM CandidaturaEntity c LEFT JOIN FETCH c.candidatos WHERE c.id = :id")
    Optional<CandidaturaEntity> findByIdWithCandidatos(@Param("id") Long id);

    /**
     * Busca candidaturas con todas las relaciones cargadas
     */
    @Query("SELECT DISTINCT c FROM CandidaturaEntity c " +
           "LEFT JOIN FETCH c.formacionPolitica " +
           "LEFT JOIN FETCH c.circunscripcion " +
           "LEFT JOIN FETCH c.estadoCandidatura " +
           "LEFT JOIN FETCH c.tipoCandidatura " +
           "LEFT JOIN FETCH c.candidatos " +
           "WHERE c.id = :id")
    Optional<CandidaturaEntity> findByIdWithAllRelations(@Param("id") Long id);

    /**
     * Verifica si existe una candidatura duplicada
     */
    @Query("SELECT COUNT(c) > 0 FROM CandidaturaEntity c WHERE " +
           "c.formacionPolitica.id = :formacionPoliticaId AND " +
           "c.circunscripcion.id = :circunscripcionId AND " +
           "(:excludeId IS NULL OR c.id != :excludeId)")
    boolean existsDuplicate(@Param("formacionPoliticaId") Long formacionPoliticaId,
                           @Param("circunscripcionId") Long circunscripcionId,
                           @Param("excludeId") Long excludeId);

    /**
     * Busca candidaturas por formación política y circunscripción
     */
    Optional<CandidaturaEntity> findByFormacionPoliticaIdAndCircunscripcionId(Long formacionPoliticaId, Long circunscripcionId);

    /**
     * Cuenta candidaturas por estado
     */
    @Query("SELECT COUNT(c) FROM CandidaturaEntity c WHERE c.estadoCandidatura.id = :estadoId")
    Long countByEstadoCandidaturaId(@Param("estadoId") Long estadoId);

    /**
     * Busca candidaturas ordenadas por formación política y circunscripción
     */
    @Query("SELECT c FROM CandidaturaEntity c " +
           "ORDER BY c.formacionPolitica.nombre ASC, c.circunscripcion.nombre ASC")
    List<CandidaturaEntity> findAllOrderedByFormacionAndCircunscripcion();
}
