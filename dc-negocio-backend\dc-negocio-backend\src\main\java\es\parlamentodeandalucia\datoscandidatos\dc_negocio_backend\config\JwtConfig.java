package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.NimbusJwtDecoder;

import java.io.ByteArrayInputStream;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.interfaces.RSAPublicKey;
import java.util.Base64;

@Configuration
public class JwtConfig {

    @Bean
    public JwtDecoder jwtDecoder() {
        try {
            // Certificado X.509 de Keycloak (actualizado 2025-08-08)
            // kid: "4EW_62BsAxdr6JYJpR_nZeTgjuNG3b2YTSasFZIbBg4"
            String certPEM = "MIICozCCAYsCBgGYhJ9/1TANBgkqhkiG9w0BAQsFADAVMRMwEQYDVQQDDApwYXJsYW1lbnRvMB4XDTI1MDgwNzEzMDAwMloXDTM1MDgwNzEzMDE0MlowFTETMBEGA1UEAwwKcGFybGFtZW50bzCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAJs1onoOFBe2HgUEa3sVTtpr3FJDTnfjO9QZpf5Mt7pmO4LS8H4aHB1f206TrtMB07OBuXnmJUXi4+8wPI6p+UBTft/dgz7WiIfyyIej+SWIOUC6WYxKjw1iWOXKmkwGPuywduh5uSIjw/LFwgDJVFUwM1+ka7Ye3Ub02TMo9RODG4yQFTuBZ/hWBNbAv+rglxQc/AYjOFtK9LkazfCNAK3H1uPO7afscV/P3caoAAeBC3Gt4b/bcfre0PJk622p/3pEizmirGvY+pdNKio4MzwjYGsQjfwhmIXn7KuXwGTsYi9HDvv+Ol89P0fA0CWWlpeMLfGDE/jW68M28w8PNAcCAwEAATANBgkqhkiG9w0BAQsFAAOCAQEANwGv7QDftfEisR9y9CNxcLARJVlXs8AMWkOMZ9Q+Nq4RC+O3NuDFvPEMeC1HFPKXO7Pmx8VUayJ97zt0gVWLnVDM3nsdtOpKQllRs97utkYcdDAhRlI2fTvDOwJywkrI8rVe5Am0rr5JdK65+jMPySqujlmotvVFwhW/pw1LVo9Jqpu1n46RaI+2nxSmLiSYWv1i6/SObhPNuM41wCm4iBV+/1a44Ql+jelHs1q2NPu86sGcdi4VVuoD6EQnz5Gbjh70UCDf2RaaEwHVWAfjYV1p82RcsxKUOIFSyDlVYyqRC/sfRsTY6KKa0gYgUJZKZR33o2TUTsdjUDd3gN6y4g==";

            // Decodificar el certificado X.509
            byte[] certBytes = Base64.getDecoder().decode(certPEM);
            CertificateFactory certFactory = CertificateFactory.getInstance("X.509");
            X509Certificate certificate = (X509Certificate) certFactory.generateCertificate(
                new ByteArrayInputStream(certBytes)
            );

            // Extraer la clave pública RSA del certificado
            RSAPublicKey publicKey = (RSAPublicKey) certificate.getPublicKey();

            // Crear el decoder JWT con la clave pública
            return NimbusJwtDecoder.withPublicKey(publicKey)
                .build();

        } catch (Exception e) {
            throw new RuntimeException("Error configurando JWT decoder", e);
        }
    }
}
