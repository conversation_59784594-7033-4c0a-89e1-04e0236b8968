import { HttpInterceptorFn, HttpErrorResponse, HttpRequest, HttpHandlerFn, HttpEvent } from '@angular/common/http';
import { inject } from '@angular/core';
import { KeycloakService } from 'keycloak-angular';
import { from, Observable, throwError } from 'rxjs';
import { switchMap, catchError } from 'rxjs/operators';
import { Router } from '@angular/router';

export const authInterceptor: HttpInterceptorFn = (req, next) => {
  const keycloak = inject(KeycloakService);
  const router = inject(Router);
        
      return from(keycloak.getToken()).pipe(
        switchMap((token) => {
          if (!token) {
            console.warn('Token vacío o indefinido');
            router.navigate(['/']);
            return throwError(() => new Error('Token vacío o indefinido'));
          }

          const authReq = req.clone({
            setHeaders: {
              Authorization: `Bearer ${token}`
            }
          });
          return next(authReq);
    }),
    catchError((error: HttpErrorResponse | any) => {
      console.error('Error en el interceptor de autenticación:', error);
      if (error instanceof HttpErrorResponse && error.status === 401) {
        keycloak.logout();
      }

      return throwError(() => error);
    })
  );
};