:host {
  width: 100%;
  overflow-x: hidden;
}

.layout-container{display:flex;flex-direction:column;height:100vh;overflow:hidden;width:100%}
.page-wrapper{margin-top:60px;margin-bottom:50px;overflow-y:auto;overflow-x:hidden;background:#f8f8f8;width:100%}
.breadcrumbs{margin:10px auto;max-width:1400px;width:calc(100% - 40px);font-size:14px;color:#666;display:flex;align-items:center}
.breadcrumbs .separator{margin:0 5px;color:#999}
.title-section{margin:0 auto 20px auto;max-width:1400px;width:calc(100% - 40px);display:flex;align-items:center;justify-content:space-between}
.title-with-back{display:flex;align-items:center;gap:12px}
.btn-back{background:none;border:none;color:#7aa12d;cursor:pointer;padding:8px;border-radius:4px;display:flex;align-items:center;justify-content:center;transition:background-color 0.2s ease}
.btn-back:hover{background-color:#f4f9f2}
.title-section h1{margin:0;font-size:24px;font-weight:600;color:#333}
.usuarios-page-container{flex:1;background:#fff;padding:16px;max-width:1400px;width:calc(100% - 40px);margin:0 auto 20px;box-sizing:border-box}
.filter-actions-bar{display:flex;justify-content:space-between;align-items:center;margin-bottom:16px;flex-wrap:wrap}
.btn-text{background:none;border:none;color:#7aa12d;font-weight:500;font-size:14px;display:inline-flex;align-items:center;gap:6px;cursor:pointer;transition:background .2s ease, color .2s ease}
.btn-text:hover{background:#f4f9f2}
.btn-text.is-open{color:#567a16}
.dropdown-masivo{position:relative}
.btn-massive{display:flex;align-items:center;background:#fff;border:1px solid #b5cc63;border-radius:6px;padding:6px 12px;font-size:14px;font-weight:500;color:#7aa12d;gap:6px;transition:all .2s ease}
.btn-massive:hover:not(.is-disabled){background:#f4f9f2;border-color:#90b44b}
.btn-massive.is-open{background:#eef6e3;border-color:#7aa12d}
.acciones-cell{display:flex;justify-content:flex-end}
:host ::ng-deep .p-dialog .p-dialog-content{padding-top:.5rem; overflow:visible}
:host ::ng-deep .p-dropdown-panel{z-index:12000}
:host ::ng-deep .p-overlaypanel, :host ::ng-deep .p-menu{z-index:12000}
:host ::ng-deep .p-dropdown{width:100%}
:host ::ng-deep .p-inputtext{width:100%}
:host ::ng-deep .p-inputnumber{width:100%}
.footer-fixed{position:fixed;bottom:0;left:0;width:100%;background:#fff;height:50px;box-sizing:border-box}
.btn-massive:disabled{opacity:.5;cursor:not-allowed;border-color:#ccc;color:#999;background:#f0f0f0}
.field{width:95%;display:flex;align-items:center;gap:12px;margin:8px 0}
.field label{flex:0 0 180px;margin:0;color:#2d2d2d}
:host ::ng-deep .p-dialog .p-dialog-content{overflow:visible}
:host ::ng-deep .p-dialog .field .p-dropdown,
:host ::ng-deep .p-dialog .field .p-inputtext,
:host ::ng-deep .p-dialog .field .p-inputnumber{
  flex:1 1 auto;
  width:100%;
  min-width:0;
  box-sizing:border-box;
}
:host ::ng-deep .p-dropdown-panel{z-index:12000}
:host ::ng-deep .p-menu, :host ::ng-deep .p-overlaypanel, :host ::ng-deep .p-column-filter-overlay{z-index:12000}

.btn-massive.is-disabled{opacity:.5;cursor:not-allowed;background:#f0f0f0;color:#999;border-color:#ccc}

.header-fixed{position:fixed;top:0;left:0;width:100%;background:#fff;height:60px;box-sizing:border-box;z-index:1040}


:host ::ng-deep .p-dialog{overflow:visible}
:host ::ng-deep .p-dropdown-panel{position:fixed}

// Solución con box-shadow para líneas perfectamente continuas
:host ::ng-deep .p-datatable {
  table {
    border-collapse: collapse !important;
  }

  .p-datatable-thead > tr > th {
    background-color: #f8f9fa !important;
    font-weight: 600;
    color: #495057;
    border: none !important;
    box-shadow: inset 0 -1px 0 #dee2e6 !important;
    padding: 1rem 0.75rem;
    vertical-align: middle;
  }

  .p-datatable-tbody > tr {
    border: none !important;
    box-shadow: inset 0 -1px 0 #dee2e6 !important;

    &:hover {
      background-color: #f8f9fa !important;
    }

    &:last-child {
      box-shadow: none !important;
    }
  }

  .p-datatable-tbody > tr > td {
    border: none !important;
    padding: 1rem 0.75rem;
    vertical-align: middle;
  }
}

// Arreglar z-index para que los menús aparezcan por encima de la tabla
:host ::ng-deep .p-datatable-wrapper {
  position: relative;
  z-index: 1;
}

:host ::ng-deep .p-datatable {
  position: relative;
  z-index: 1;
}

// Asegurar que los dropdowns del header tengan mayor z-index
:host ::ng-deep .navbar-nav .dropdown-menu,
:host ::ng-deep .nav-item .dropdown-menu,
:host ::ng-deep .dropdown-menu {
  z-index: 1050 !important;
  position: absolute !important;
}

// Específicamente para el dropdown de administración
:host ::ng-deep .navbar .dropdown-menu {
  z-index: 9999 !important;
}

// Asegurar que el navbar tenga z-index alto
:host ::ng-deep .navbar,
:host ::ng-deep .header-fixed {
  z-index: 1040 !important;
}

// Estilos para el diálogo de confirmación
:host ::ng-deep .p-confirm-dialog {
  .p-dialog-header {
    background: #fff;
    border-bottom: 1px solid #e9ecef;
    padding: 1.5rem;
  }

  .p-dialog-title {
    font-weight: 600;
    color: #495057;
    font-size: 1.1rem;
  }

  .p-dialog-content {
    padding: 1.5rem;

    .p-confirm-dialog-message {
      margin-left: 1rem;
      font-size: 1rem;
      color: #495057;
      line-height: 1.5;
    }

    .p-confirm-dialog-icon {
      font-size: 2rem;
      color: #f39c12;
    }
  }

  .p-dialog-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;

    .p-button {
      margin: 0 0.25rem;
      padding: 0.75rem 1.5rem;
      font-weight: 500;
      border-radius: 6px;

      &.p-button-danger {
        background: #dc3545;
        border-color: #dc3545;

        &:hover {
          background: #c82333;
          border-color: #bd2130;
        }
      }

      &:not(.p-button-danger) {
        background: #6c757d;
        border-color: #6c757d;

        &:hover {
          background: #5a6268;
          border-color: #545b62;
        }
      }
    }
  }
}
