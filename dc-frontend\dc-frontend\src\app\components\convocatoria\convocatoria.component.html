<div class="layout-container">

    <app-header class="header-fixed"></app-header>

    <div class="page-wrapper scrollable-content">
        <div class="breadcrumbs">
            <!-- Icono de casa para el inicio del breadcrumbs -->
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="16px" height="16px"
                style="vertical-align: middle; margin-right: 3px;">
                <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z" />
            </svg>
            <span class="separator">&gt;</span> <!-- Separador después del icono de casa -->
            <span>Administración</span>
            <span class="separator">&gt;</span>
            <strong>Convocatoria</strong>
        </div>
        <div class="title-section">
            <a routerLink="/home" class="back-button"> <!-- <PERSON><PERSON>s la clase back-button para estilizar la flecha -->
                <!-- Icon<PERSON> de flecha hacia atrás (SVG simple para visualización) -->
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="24px"
                    height="24px">
                    <path d="M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z" transform="rotate(180 12 12)" />
                </svg>
            </a>
            <h1 class="page-main-title">Convocatoria</h1>
            <!-- Usamos una clase específica para el título principal -->
            <div class="header-actions">
                <p-button label="Consultar histórico" styleClass="p-button-secondary"></p-button>
            </div>
        </div>

        <div class="convocatoria-form-container">
            <div class="header-actions-right">
                <p-button *ngIf="!modoEditar" icon="pi pi-pencil" label="Modificar información"
                    class="p-button-success" (click)="modoEditar = true">
                </p-button>

                <p-button *ngIf="modoEditar" icon="pi pi-check" label="Guardar cambios" styleClass="p-button-success"
                    (click)="guardarCambios()">
                </p-button>
            </div>
            <div class="form-grid">
                <div class="form-column">
                    <div class="form-field horizontal">
                        <label for="fechaConvocatoria">Fechas de convocatoria</label>
                        <p-datePicker inputId="fechaConvocatoria" [showIcon]="true"
                            dateFormat="dd/mm/yy" [(ngModel)]="convocatoria.fecha" [disabled]="!modoEditar" placeholder="00/00/0000"></p-datePicker>
                    </div>
                    <div class="form-field horizontal">
                        <label for="fechaElecciones">Fechas de elecciones</label>
                        <p-datePicker inputId="fechaConvocatoria" [showIcon]="true" dateFormat="dd/mm/yy"
                            [(ngModel)]="convocatoria.fechaElecciones" [disabled]="!modoEditar" placeholder="00/00/0000">
                        </p-datePicker>
                    </div>
                    <div class="form-field horizontal">
                        <label for="inicioPresentacionCandidatura">Fecha inicio presentación candidatura</label>
                        <p-datePicker inputId="inicioPresentacionCandidatura" [showIcon]="true"
                            dateFormat="dd/mm/yy" [(ngModel)]="convocatoria.fechaInicioPresentacion" [disabled]="!modoEditar" placeholder="00/00/0000"></p-datePicker>
                    </div>
                    <div class="form-field horizontal">
                        <label for="finPresentacionCandidatura">Fecha fin presentación candidatura</label>
                        <p-datePicker inputId="finPresentacionCandidatura" [showIcon]="true"
                            dateFormat="dd/mm/yy" [(ngModel)]="convocatoria.fechaFinPresentacion" [disabled]="!modoEditar" placeholder="00/00/0000"></p-datePicker>
                    </div>
                    <div class="form-field horizontal">
                        <label for="fechaPublicacionBoja">Fecha publicación en BOJA</label>
                        <p-datePicker inputId="fechaPublicacionBoja" [showIcon]="true"
                            dateFormat="dd/mm/yy" [(ngModel)]="convocatoria.fechaInicioPublicacionBoja" [disabled]="!modoEditar" placeholder="00/00/0000"></p-datePicker>
                    </div>
                    <div class="form-field horizontal">
                        <label for="fechaComunicacionIrregularidades">Fecha comunicación irregularidades</label>
                        <p-datePicker inputId="fechaComunicacionIrregularidades" [showIcon]="true"
                            dateFormat="dd/mm/yy" [(ngModel)]="convocatoria.fechaIrregularidades" [disabled]="!modoEditar" placeholder="00/00/0000"></p-datePicker>
                    </div>
                    <div class="form-field horizontal">
                        <label for="fechaInicioSubsanacion">Fecha inicio subsanación</label>
                        <p-datePicker inputId="fechaInicioSubsanacion" [showIcon]="true"
                            dateFormat="dd/mm/yy" [(ngModel)]="convocatoria.fechaInicioSubsanacion" [disabled]="!modoEditar" placeholder="00/00/0000"></p-datePicker>
                    </div>
                    <div class="form-field horizontal">
                        <label for="fechaFinSubsanacion">Fecha fin subsanación</label>
                        <p-datePicker inputId="fechaFinSubsanacion" [showIcon]="true"
                            dateFormat="dd/mm/yy" [(ngModel)]="convocatoria.fechaFinSubsanacion" [disabled]="!modoEditar" placeholder="00/00/0000"></p-datePicker>
                    </div>
                </div>

                <div class="form-column">
                    <div class="form-field horizontal">
                        <label for="fechaProclamacion">Fecha proclamación</label>
                        <p-datePicker inputId="fechaProclamacion" [showIcon]="true"
                            dateFormat="dd/mm/yy" [(ngModel)]="convocatoria.fechaProclamacion" [disabled]="!modoEditar" placeholder="00/00/0000"></p-datePicker>
                    </div>
                    <div class="form-field horizontal">
                        <label for="fechaPublicacionProclamadaBoja">Fecha publicación proclamada</label>
                        <p-datePicker inputId="fechaPublicacionProclamadaBoja" [showIcon]="true"
                            dateFormat="dd/mm/yy" [(ngModel)]="convocatoria.fechaPublicacionProclamada" [disabled]="!modoEditar" placeholder="00/00/0000"></p-datePicker>
                    </div>
                    <div class="form-field horizontal">
                        <label for="inicioDeclaraciones">Fecha inicio declaraciones</label>
                        <p-datePicker inputId="inicioDeclaraciones" [showIcon]="true"
                            dateFormat="dd/mm/yy" [(ngModel)]="convocatoria.fechaInicioDeclaracion" [disabled]="!modoEditar" placeholder="00/00/0000"></p-datePicker>
                    </div>
                    <div class="form-field horizontal">
                        <label for="finDeclaraciones">Fecha fin declaraciones</label>
                        <p-datePicker inputId="finDeclaraciones" [showIcon]="true" 
                        dateFormat="dd/mm/yy" [(ngModel)]="convocatoria.fechaFinDeclaracion" [disabled]="!modoEditar" placeholder="00/00/0000"></p-datePicker>
                    </div>
                    <div class="form-field horizontal">
                        <label for="fechaInicioPublicacionInternet">Fecha inicio publicación internet</label>
                        <p-datePicker inputId="fechaInicioPublicacionInternet" [showIcon]="true"
                            dateFormat="dd/mm/yy" [(ngModel)]="convocatoria.fechaInicioPublicacionInternet" [disabled]="!modoEditar" placeholder="00/00/0000"></p-datePicker>
                    </div>
                    <div class="form-field horizontal">
                        <label for="fechaFinPublicacionInternet">Fecha fin publicación internet</label>
                        <p-datePicker inputId="fechaFinPublicacionInternet" [showIcon]="true"
                            dateFormat="dd/mm/yy" [(ngModel)]="convocatoria.fechaFinPublicacionInternet" [disabled]="!modoEditar" placeholder="00/00/0000"></p-datePicker>
                    </div>
                    <div class="form-field horizontal">
                        <label for="fechaCancelacion">Fecha cancelación</label>
                        <p-datePicker inputId="fechaCancelacion" iconDisplay="input"
                            dateFormat="dd/mm/yy" [(ngModel)]="convocatoria.fechaCancelacion" [disabled]="!modoEditar" placeholder="00/00/0000"></p-datePicker>
                    </div>
                    <div class="form-field horizontal">
                        <label for="logotipoProceso">Logotipo del proceso</label>
                        <div class="fake-file-input">
                            <i class="pi pi-plus icon-plus-green"></i>
                        </div>
                        <span class="file-name">logotipo.png</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <app-footer class="footer-fixed"></app-footer>
</div>