<div class="layout-container">
  <app-header class="header-fixed"></app-header>

  <div class="page-wrapper scrollable-content">
    <div class="breadcrumbs">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="16px" height="16px">
        <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z" />
      </svg>
      <span class="separator">&gt;</span>
      <span>Administración</span>
      <span class="separator">&gt;</span>
      <strong>Plantillas</strong>
    </div>

    <div class="title-section">
      <a routerLink="/home" class="back-button">
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24" width="24px" height="24px">
          <path d="M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z" transform="rotate(180 12 12)" />
        </svg>
      </a>
      <h1 class="page-main-title">Plantillas</h1>
      <button class="btn-primary" (click)="addNew()">+ Añadir</button>
    </div>

    <div class="usuarios-page-container">
      <div class="filter-actions-bar">
        <button class="btn-text" (click)="clearFilters()">
          <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24" width="16px" height="16px">
            <path
              d="M6 13c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm14-10v2h-3.21l-3.42 8H18v2h-7.15c-.78 1.76-2.58 3-4.85 3-2.21 0-4-1.79-4-4s1.79-4 4-4c.78 0 1.5.22 2.15.62L12.58 4H20V2h2v2h-2z" />
          </svg>
          Limpiar filtros
        </button>

        <div class="dropdown-masivo" (clickOutside)="showMassiveActions = false">
          <button class="btn-massive" (click)="showMassiveActions = !showMassiveActions">
            Acciones masivas
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
              <path d="M7 10l5 5 5-5H7z" />
            </svg>
          </button>
          <ul class="dropdown-options" *ngIf="showMassiveActions">
            <li (click)="activateTemplates()">Activar todos</li>
            <li (click)="deactivateTemplates()">Desactivar todos</li>
            <li (click)="downloadSelected()">Descargar</li>
          </ul>
        </div>
      </div>

      <p-table #dt [value]="plantillas" [paginator]="true" [rows]="10"
        [globalFilterFields]="['nombre', 'tipo', 'formato', 'estado']" [selection]="selectedRows"
        (selectionChange)="selectedRows = $event" [rowHover]="true" dataKey="id"
        paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown">

        <ng-template pTemplate="header">
          <tr>
            <th style="width: 3rem">
              <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
            </th>
            <th pSortableColumn="nombre">
              Nombre
              <p-sortIcon field="nombre" />
              <p-columnFilter field="nombre" matchMode="startsWith" display="menu" type="text"></p-columnFilter>
            </th>
            <th pSortableColumn="tipo">
              Tipo
              <p-sortIcon field="tipo" />
              <p-columnFilter field="tipo" matchMode="equals" display="menu">
                <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                  <p-dropdown [options]="tipos" [ngModel]="value" (onChange)="filter($event.value)" optionLabel="label"
                    optionValue="value" placeholder="Selecciona tipo" class="w-full"></p-dropdown>
                </ng-template>
              </p-columnFilter>
            </th>
            <th pSortableColumn="formato">
              Formato
              <p-sortIcon field="formato" />
              <p-columnFilter field="formato" matchMode="startsWith" display="menu" type="text"></p-columnFilter>
            </th>
            <th pSortableColumn="estado">
              Estado
              <p-sortIcon field="estado" />
              <p-columnFilter field="estado" matchMode="equals" display="menu">
                <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                  <p-dropdown [options]="estados" [ngModel]="value" (onChange)="filter($event.value)"
                    optionLabel="label" optionValue="value" placeholder="Selecciona estado" class="w-full"></p-dropdown>
                </ng-template>
              </p-columnFilter>
            </th>
            <th style="width: 3rem;"></th>
          </tr>
        </ng-template>

        <ng-template pTemplate="body" let-plantilla let-rowIndex="rowIndex">
          <tr [ngClass]="getRowStyle(plantilla, rowIndex)" [class.p-highlight]="selectedRows.includes(plantilla)">
            <td><p-tableCheckbox [value]="plantilla"></p-tableCheckbox></td>
            <td>{{ plantilla.nombre }}</td>
            <td>
              <span class="role-badge" [ngClass]="getTipoClass(plantilla.tipo)">
                {{ plantilla.tipo }}
              </span>
            </td>
            <td>{{ plantilla.formato }}</td>
            <td>
              <span class="status-badge" [ngClass]="getStatusClass(plantilla.estado)">
                {{ plantilla.estado }}
              </span>
            </td>
            <td class="acciones-cell">
              <div class="dropdown">
                <button class="btn-icon dropdown-toggle" (click)="toggleDropdown(plantilla.id)">
                  Acciones
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor"
                    viewBox="0 0 24 24">
                    <path
                      d="M12 7a2 2 0 1 0 0-4 2 2 0 0 0 0 4zm0 2a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm0 6a2 2 0 1 0 0 4 2 2 0 0 0 0-4z" />
                  </svg>
                </button>
                <div class="dropdown-menu" *ngIf="openedDropdownId === plantilla.id">
                  <a class="dropdown-item" (click)="rowAction(plantilla.id, 'ver')">Ver</a>
                  <a class="dropdown-item" (click)="rowAction(plantilla.id, 'editar')">Editar</a>
                  <a class="dropdown-item" (click)="rowAction(plantilla.id, 'descargar')">Descargar</a>
                  <a class="dropdown-item" (click)="rowAction(plantilla.id, 'desactivar')">Desactivar</a>
                </div>
              </div>
            </td>
          </tr>
        </ng-template>
      </p-table>
    </div>
  </div>

  <app-footer class="footer-fixed"></app-footer>
</div>