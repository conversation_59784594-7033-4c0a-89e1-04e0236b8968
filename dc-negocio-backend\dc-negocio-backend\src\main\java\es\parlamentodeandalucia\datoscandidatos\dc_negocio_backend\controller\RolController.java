package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.controller;

import es.parlamentodeandalucia.datoscandidatos.dto.Rol;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/api/roles")
@CrossOrigin(origins = {"http://localhost:4200", "https://localhost:4200"})
public class RolController {

    @GetMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<List<Rol>> getRoles(@AuthenticationPrincipal Jwt jwt) {
        
        // Datos de ejemplo - aquí conectarías con tu servicio/repositorio
        List<Rol> roles = new ArrayList<>();
        
        Rol adminRole = new Rol()
            .id(1)
            .nombre("ADMIN");
        roles.add(adminRole);
        
        Rol userRole = new Rol()
            .id(2)
            .nombre("USER");
        roles.add(userRole);
        
        Rol representanteRole = new Rol()
            .id(3)
            .nombre("REPRESENTANTE");
        roles.add(representanteRole);
        
        return ResponseEntity.ok(roles);
    }
}
