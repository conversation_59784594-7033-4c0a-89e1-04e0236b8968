package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.controller;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.ImportacionEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service.ImportacionService;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository.ErrorImportRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;
import org.springframework.http.HttpStatus;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ImportacionControllerTest {

    @Mock
    private ImportacionService importacionService;

    @Mock
    private ErrorImportRepository errorImportRepository;

    @InjectMocks
    private ImportacionController importacionController;

    private ImportacionEntity importacionTest;

    @BeforeEach
    void setUp() {
        // Crear entidad de importación de prueba
        importacionTest = new ImportacionEntity("testuser", LocalDate.now(), "test.csv", "CSV", new byte[0]);
        importacionTest.setId(1L);
        importacionTest.setFilasCorrectas(2L);
        importacionTest.setFilasIncorrectas(0L);
    }

    @Test
    void testGetAll() {
        // Given
        List<ImportacionEntity> importaciones = List.of(importacionTest);
        when(importacionService.findAll()).thenReturn(importaciones);

        // When
        ResponseEntity<List<ImportacionEntity>> response = importacionController.getAll();

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(1, response.getBody().size());
        assertEquals(1L, response.getBody().get(0).getId());
        verify(importacionService).findAll();
    }

    @Test
    void testGetById() {
        // Given
        when(importacionService.findById(1L)).thenReturn(Optional.of(importacionTest));

        // When
        ResponseEntity<ImportacionEntity> response = importacionController.getById(1L, null);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(1L, response.getBody().getId());
        verify(importacionService).findById(1L);
    }

    @Test
    void testGetById_NoEncontrada() {
        // Given
        when(importacionService.findById(1L)).thenReturn(Optional.empty());

        // When
        ResponseEntity<ImportacionEntity> response = importacionController.getById(1L, null);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        verify(importacionService).findById(1L);
    }

    @Test
    void testDelete() {
        // Given
        when(importacionService.findById(1L)).thenReturn(Optional.of(importacionTest));
        doNothing().when(importacionService).deleteById(1L);

        // When
        ResponseEntity<Void> response = importacionController.delete(1L);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        verify(importacionService).findById(1L);
        verify(importacionService).deleteById(1L);
    }

    @Test
    void testDelete_NoEncontrada() {
        // Given
        when(importacionService.findById(1L)).thenReturn(Optional.empty());

        // When
        ResponseEntity<Void> response = importacionController.delete(1L);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        verify(importacionService).findById(1L);
        verify(importacionService, never()).deleteById(1L);
    }


}
