.app-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-top: 4px solid #C2E038;
  font-family: "Open Sans", sans-serif;
  font-size: 14px;
  background-color: #FFFFFF;
  color: #333333;

  .left {
    white-space: nowrap;
    font-size: 13px;
  }

  .right {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;

    a {
      color: #333333;
      text-decoration: none;
      font-size: 14px;
      line-height: 20px;
      white-space: nowrap;
      transition: color 0.2s ease;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
        color: #5A7500;
      }
    }
  }

  // 📱 Responsive
  @media (max-width: 600px) {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 8px;
    padding: 12px 16px;

    .left {
      font-size: 12px;
    }

    .right {
      flex-wrap: wrap;
      justify-content: center;
      gap: 12px;

      a {
        font-size: 13px;
        line-height: 18px;
      }
    }
  }
}
