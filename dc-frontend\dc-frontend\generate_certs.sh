#!/bin/sh

CERT_DIR="/etc/nginx/certs"
CN="***************"  # Reemplaza con tu IP o dominio si cambia

# Asegurar que el directorio exista
mkdir -p "$CERT_DIR"

# Solo generar si no existen los certificados
if [ ! -f "$CERT_DIR/nginx.crt" ] || [ ! -f "$CERT_DIR/nginx.key" ]; then
    echo "[INFO] Generando certificados SSL autofirmados para CN=$CN..."

    openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
        -keyout "$CERT_DIR/nginx.key" \
        -out "$CERT_DIR/nginx.crt" \
        -subj "/C=ES/ST=Andalusia/L=Mairena/O=DatosCandidatos/CN=$CN"

    echo "[INFO] Certificados generados correctamente en $CERT_DIR"
else
    echo "[INFO] Certificados ya existen. Omitiendo generación."
fi
