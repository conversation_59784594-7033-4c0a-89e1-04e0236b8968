<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>DcFrontend</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" type="image/x-icon" href="favicon.ico">

  <!-- Configuración de seguridad -->
  <meta http-equiv="Content-Security-Policy" content="
    default-src 'self';
    script-src 'self' 'unsafe-inline' 'unsafe-eval' https://***************:8453;
    style-src 'self' 'unsafe-inline';
    img-src 'self' data: https:;
    font-src 'self' data:;
    connect-src 'self' https://***************:8453 http://localhost:8080;
    frame-src 'self' https://***************:8453;
    object-src 'none';
    base-uri 'self';
    form-action 'self';
  ">

  <!-- Prevenir ataques de clickjacking -->
  <meta http-equiv="X-Frame-Options" content="SAMEORIGIN">

  <!-- Prevenir MIME type sniffing -->
  <meta http-equiv="X-Content-Type-Options" content="nosniff">

  <!-- Forzar HTTPS en producción -->
  <meta http-equiv="Strict-Transport-Security" content="max-age=31536000; includeSubDomains">

  <!-- Estilos gestionados por Angular en angular.json -->
  <!-- Eliminado: referencias directas a node_modules y styles.css -->
</head>
<body>
  <app-root></app-root>
</body>
</html>
