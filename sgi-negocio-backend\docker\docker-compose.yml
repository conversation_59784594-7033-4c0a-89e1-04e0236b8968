version: '3.8'

services:
  sgi-db:
    image: postgres:14-alpine
    container_name: sgi-database
    ports:
      - "5433:5432"
    environment:
      POSTGRES_DB: sgiDatabase
      POSTGRES_USER: sgi_user
      POSTGRES_PASSWORD: sgi_password
    command: ["postgres", "-c", "max_prepared_transactions=100"]
    volumes:
      - sgi_data:/var/lib/postgresql/data
      - ./scripts:/docker-entrypoint-initdb.d/
    networks:
      - sgi-network

volumes:
  sgi_data:

networks:
  sgi-network:
    driver: bridge