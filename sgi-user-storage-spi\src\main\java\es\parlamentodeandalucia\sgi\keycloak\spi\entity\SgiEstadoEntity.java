package es.parlamentodeandalucia.sgi.keycloak.spi.entity;

import jakarta.persistence.*;

/**
 * Entidad para estados del SGI
 * Mapea con la tabla sgi_t_estado
 */
@Entity
@Table(name = "sgi_t_estado")
public class SgiEstadoEntity {

    @Id
    @Column(name = "sgi_id_estado")
    private Long id;

    @Column(name = "sgi_tx_valor", unique = true)
    private String valor;

    // Constructores
    public SgiEstadoEntity() {}

    public SgiEstadoEntity(Long id, String valor) {
        this.id = id;
        this.valor = valor;
    }

    // Getters y Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getValor() { return valor; }
    public void setValor(String valor) { this.valor = valor; }
}
