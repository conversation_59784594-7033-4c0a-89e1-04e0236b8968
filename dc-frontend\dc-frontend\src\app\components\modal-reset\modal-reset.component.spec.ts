import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ModalResetComponent } from './modal-reset.component';
import { DialogModule } from 'primeng/dialog';
import { ButtonModule } from 'primeng/button';

describe('ModalResetComponent', () => {
  let component: ModalResetComponent;
  let fixture: ComponentFixture<ModalResetComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ModalResetComponent],
      imports: [DialogModule, ButtonModule]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ModalResetComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should emit visibleChange when closing the modal', () => {
    const spy = spyOn(component.visibleChange, 'emit');
    component.close();
    expect(spy).toHaveBeenCalledWith(false);
  });

  it('should emit confirmDelete when confirming action', () => {
    const spy = spyOn(component.confirmDelete, 'emit');
    component.confirmAction();
    expect(spy).toHaveBeenCalled();
  });
});