events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # Configuración de logs
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log warn;

    # Configuración de rendimiento
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    # Compresión gzip
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        application/atom+xml
        application/geo+json
        application/javascript
        application/x-javascript
        application/json
        application/ld+json
        application/manifest+json
        application/rdf+xml
        application/rss+xml
        application/xhtml+xml
        application/xml
        font/eot
        font/otf
        font/ttf
        image/svg+xml
        text/css
        text/javascript
        text/plain
        text/xml;

    # --- HTTP Server: Redirige a HTTPS ---
    server {
        listen 80;
        listen [::]:80;
        server_name ***************;

        # Redirige todo el tráfico HTTP a HTTPS
        return 301 https://$host$request_uri;
    }

    # --- HTTPS Server: Sirve tu aplicación Angular ---
    server {
        listen 443 ssl;
        listen [::]:443 ssl;
        server_name ***************;

        # Rutas a los certificados SSL generados por el script
        ssl_certificate /etc/nginx/certs/nginx.crt;
        ssl_certificate_key /etc/nginx/certs/nginx.key;

        # Configuración SSL/TLS básica para mayor seguridad
        ssl_session_cache shared:SSL:1m;
        ssl_session_timeout 5m;
        ssl_ciphers HIGH:!aNULL:!MD5;
        ssl_prefer_server_ciphers on;

        root /usr/share/nginx/html;
        index index.html;

        # Configuración de seguridad
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header Referrer-Policy "no-referrer-when-downgrade" always;
        # Importante: ajusta tu CSP. 'unsafe-inline' es flexible pero menos seguro.
        # Permitir conexiones y iframes hacia Keycloak (8453) y backend (8054)
        add_header Content-Security-Policy "default-src 'self' https://***************:8088 https://***************:8054 'unsafe-inline' 'unsafe-eval' data: blob:; connect-src 'self' https://***************:8054 https://***************:8453; frame-src 'self' https://***************:8453;" always;


        # Configuración de caché para archivos estáticos (dentro del HTTPS server)
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            try_files $uri =404;
        }

        # Configuración para Angular routing (SPA) (dentro del HTTPS server)
        location / {
            try_files $uri $uri/ /index.html;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
        }

        # Health check endpoint para Portainer (dentro del HTTPS server)
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # Configuración para API proxy (opcional)
        # Descomenta y ajusta si necesitas proxy a tu backend Spring Boot
        # Asegúrate de usar el nombre del servicio Docker Compose 'backend'
        # y el puerto interno '8080'.
        location /api/ {
            proxy_pass http://***************:8054/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme; # Esto es importante para el backend sepa que la solicitud original fue HTTPS
        }

        # Manejo de errores
        error_page 404 /index.html; # Angular maneja 404s vía index.html
        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }
}