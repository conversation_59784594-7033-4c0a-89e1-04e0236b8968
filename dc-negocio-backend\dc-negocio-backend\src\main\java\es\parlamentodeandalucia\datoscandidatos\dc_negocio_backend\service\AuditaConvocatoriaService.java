package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.AuditaConvocatoriaEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.ConvocatoriaEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository.AuditaConvocatoriaRepository;
import jakarta.transaction.Transactional;

@Service
@Transactional
public class AuditaConvocatoriaService {

    @Autowired
    private AuditaConvocatoriaRepository auditaConvocatoriaRepository;

    public void auditarCambioFecha(
            ConvocatoriaEntity original,
            ConvocatoriaEntity actualizada,
            String campo,
            java.sql.Date valorOriginal,
            java.sql.Date nuevoValor,
            String motivo) {

        if (valorOriginal == null && nuevoValor == null)
            return;
        if (valorOriginal != null && valorOriginal.equals(nuevoValor))
            return;
        if (nuevoValor != null && nuevoValor.equals(valorOriginal))
            return;

        AuditaConvocatoriaEntity auditoria = new AuditaConvocatoriaEntity();
        auditoria.setColumna(campo);
        auditoria.setValorAnterior(valorOriginal != null ? valorOriginal.toString() : null);
        auditoria.setValorNuevo(nuevoValor != null ? nuevoValor.toString() : null);
        auditoria.setFechaModificacion(new java.util.Date());
        auditoria.setMotivo(motivo);
        auditoria.setConvocatoria(actualizada);

        auditaConvocatoriaRepository.save(auditoria);
    }

}
