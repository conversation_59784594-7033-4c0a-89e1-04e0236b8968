package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.controller;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.ConvocatoriaEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service.ConvocatoriaService;
import io.swagger.v3.oas.annotations.parameters.RequestBody;

@RestController
@RequestMapping("/api/convocatorias")
@CrossOrigin(origins = { "http://localhost:4200", "https://localhost:4200" })
public class ConvocatoriaController {

    @Autowired
    private ConvocatoriaService convocatoriaService;

    @GetMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<List<ConvocatoriaEntity>> listarConvocatorias() {
        List<ConvocatoriaEntity> convocatorias = convocatoriaService.findAll();
        return ResponseEntity.ok(convocatorias);
    }

    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ConvocatoriaEntity> crearConvocatoria(@RequestBody ConvocatoriaEntity convocatoria) {
        ConvocatoriaEntity nueva = convocatoriaService.crearConvocatoria(convocatoria);
        return ResponseEntity.ok(nueva);
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<ConvocatoriaEntity> obtenerConvocatoria(@PathVariable Long id) {
        Optional<ConvocatoriaEntity> resultado = convocatoriaService.obtenerConvocatoriaPorId(id);
        return resultado.map(ResponseEntity::ok).orElseGet(() -> ResponseEntity.notFound().build());
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ConvocatoriaEntity> actualizarConvocatoria(
            @PathVariable Long id,
            @RequestBody ConvocatoriaEntity convocatoriaActualizada) {

        Optional<ConvocatoriaEntity> existente = convocatoriaService.obtenerConvocatoriaPorId(id);
        if (existente.isEmpty()) {
            return ResponseEntity.notFound().build();
        }

        convocatoriaActualizada.setId(id); // Asegurar que se actualiza la correcta
        ConvocatoriaEntity actualizada = convocatoriaService.actualizarConvocatoria(convocatoriaActualizada);
        return ResponseEntity.ok(actualizada);
    }

    @PostMapping("/logotipo/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<String> subirLogotipo(@PathVariable Long id, @RequestParam("file") MultipartFile file)
            throws IOException {
        if (!file.getContentType().equals("image/png") && !file.getContentType().equals("image/svg+xml")) {
            return ResponseEntity.badRequest().body("Formato no permitido. Solo PNG o SVG.");
        }

        if (file.getSize() > 1024 * 1024) {
            return ResponseEntity.badRequest().body("El archivo supera 1 MB.");
        }

        Optional<ConvocatoriaEntity> opt = convocatoriaService.obtenerConvocatoriaPorId(id);
        if (opt.isEmpty()) {
            return ResponseEntity.notFound().build();
        }

        ConvocatoriaEntity convocatoria = opt.get();
        convocatoria.setLogotipo(file.getBytes());
        convocatoriaService.actualizarConvocatoria(convocatoria);

        return ResponseEntity.ok("Logotipo actualizado correctamente.");
    }
}
