<div class="page-container">
  <app-header></app-header>

  <main class="main-content">
    <div class="title-section">
      <div class="title-with-back">
        <button class="btn-back" (click)="goBack()">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
            <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
          </svg>
        </button>
        <h1>Importación de Candidaturas</h1>
      </div>
      <div class="actions">
        <button pButton type="button" label="Importar" icon="pi pi-plus" (click)="abrirModalImportar()"></button>
      </div>
    </div>

    <div class="importaciones-page-container">
      <!-- Tabla de historial de importaciones -->
      <p-table
        #dt
        [value]="importaciones"
        [paginator]="true"
        [rows]="10"
        [loading]="loading"
        [globalFilterFields]="['nombreFichero', 'usuario', 'fechaImportacion']"
        dataKey="id"
        styleClass="p-datatable-gridlines">

        <ng-template pTemplate="caption">
          <div class="table-header">
            <span>Historial de Importaciones</span>
          </div>
        </ng-template>

        <ng-template pTemplate="header">
          <tr>
            <th pSortableColumn="nombreFichero">
              Nombre Fichero
              <p-sortIcon field="nombreFichero"></p-sortIcon>
            </th>
            <th pSortableColumn="usuario">
              Usuario
              <p-sortIcon field="usuario"></p-sortIcon>
            </th>
            <th pSortableColumn="fechaImportacion">
              Fecha
              <p-sortIcon field="fechaImportacion"></p-sortIcon>
            </th>
            <th pSortableColumn="filasCorrectas">
              Filas Correctas
              <p-sortIcon field="filasCorrectas"></p-sortIcon>
            </th>
            <th pSortableColumn="filasIncorrectas">
              Filas Incorrectas
              <p-sortIcon field="filasIncorrectas"></p-sortIcon>
            </th>
            <th>Resultado</th>
          </tr>
        </ng-template>

        <ng-template pTemplate="body" let-importacion>
          <tr>
            <td>{{ importacion.nombreFichero }}</td>
            <td>{{ importacion.usuario || 'Usuario por defecto' }}</td>
            <td>{{ importacion.fechaImportacion | date:'dd/MM/yyyy' }}</td>
            <td>{{ importacion.filasCorrectas || 0 }}</td>
            <td>{{ importacion.filasIncorrectas || 0 }}</td>
            <td>
              <button
                pButton
                type="button"
                icon="pi pi-download"
                class="p-button-text p-button-sm"
                (click)="descargarLog(importacion)"
                pTooltip="Descargar log de importación">
              </button>
            </td>
          </tr>
        </ng-template>

        <ng-template pTemplate="emptymessage">
          <tr>
            <td colspan="6" class="text-center">No se encontraron importaciones</td>
          </tr>
        </ng-template>
      </p-table>
    </div>
  </main>

  <app-footer></app-footer>
</div>

<!-- Modal de importación -->
<p-dialog
  header="Importar archivo CSV"
  [(visible)]="showImportModal"
  [modal]="true"
  [closable]="!isUploading"
  [draggable]="false"
  [resizable]="false"
  styleClass="import-modal"
  [style]="{width: '600px'}">

  <div class="modal-content">
    <!-- Sección de selección de archivo -->
    <div class="upload-section" *ngIf="!showLog">
      <div class="file-info" *ngIf="!selectedFile">
        <div class="upload-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" fill="currentColor" viewBox="0 0 24 24">
            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
          </svg>
        </div>
        <p class="upload-text">Seleccione un archivo CSV para importar candidaturas</p>
        <p class="upload-subtext">Solo se aceptan archivos .csv</p>
      </div>

      <div class="file-selected" *ngIf="selectedFile">
        <div class="file-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" viewBox="0 0 24 24">
            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
          </svg>
        </div>
        <div class="file-details">
          <p class="file-name">{{ selectedFile.name }}</p>
          <p class="file-size">{{ (selectedFile.size / 1024).toFixed(1) }} KB</p>
        </div>
      </div>

      <div class="upload-actions">
        <input
          #fileInput
          type="file"
          accept=".csv"
          (change)="onFileChange($event)"
          style="display: none;">

        <button
          pButton
          type="button"
          [label]="selectedFile ? 'Cambiar archivo' : 'Seleccionar archivo'"
          icon="pi pi-upload"
          class="p-button-outlined"
          (click)="onFileSelect()"
          [disabled]="isUploading">
        </button>

        <button
          pButton
          type="button"
          label="Limpiar"
          icon="pi pi-times"
          class="p-button-outlined p-button-secondary"
          (click)="limpiarSeleccion()"
          *ngIf="selectedFile && !isUploading">
        </button>
      </div>

      <!-- Progreso de importación -->
      <div class="progress-section" *ngIf="isUploading">
        <div class="progress-content">
          <div class="progress-icon">
            <i class="pi pi-spin pi-spinner" style="font-size: 2rem; color: #28a745;"></i>
          </div>
          <div class="progress-text">
            <h4>Procesando archivo...</h4>
            <p>Por favor espere mientras se procesa la importación</p>
          </div>
        </div>
        <p-progressBar
          [value]="uploadProgress"
          [showValue]="true"
          styleClass="custom-progress">
        </p-progressBar>
      </div>
    </div>

    <!-- Sección de resultados/log -->
    <div class="log-section" *ngIf="showLog">
      <div class="log-content">
        <pre>{{ logContent }}</pre>
      </div>
    </div>
  </div>

  <ng-template pTemplate="footer">
    <div class="modal-footer">
      <button
        pButton
        type="button"
        label="Cancelar"
        icon="pi pi-times"
        class="p-button-text"
        (click)="cerrarModal()"
        [disabled]="isUploading">
      </button>

      <button
        pButton
        type="button"
        [label]="showLog ? 'Nueva importación' : 'Procesar'"
        [icon]="showLog ? 'pi pi-refresh' : 'pi pi-check'"
        class="p-button-success"
        (click)="showLog ? nuevaImportacion() : procesarImportacion()"
        [disabled]="!showLog && (!selectedFile || isUploading)"
        [loading]="isUploading">
      </button>
    </div>
  </ng-template>
</p-dialog>

<p-toast></p-toast>
