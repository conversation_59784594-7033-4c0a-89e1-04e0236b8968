package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.ErrorImportEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.ImportacionEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ErrorImportRepository extends JpaRepository<ErrorImportEntity, Long> {

    /**
     * Busca errores por importación
     * @param importacion La importación
     * @return Lista de errores de la importación
     */
    List<ErrorImportEntity> findByImportacion(ImportacionEntity importacion);

    /**
     * Busca errores por ID de importación
     * @param importacionId ID de la importación
     * @return Lista de errores de la importación
     */
    List<ErrorImportEntity> findByImportacionId(Long importacionId);

    /**
     * Busca errores por línea específica de una importación
     * @param importacionId ID de la importación
     * @param linea Número de línea
     * @return Lista de errores en esa línea
     */
    @Query("SELECT e FROM ErrorImportEntity e WHERE e.importacion.id = :importacionId AND e.linea = :linea")
    List<ErrorImportEntity> findByImportacionIdAndLinea(@Param("importacionId") Long importacionId, 
                                                        @Param("linea") Long linea);

    /**
     * Busca errores por campo específico
     * @param campo Nombre del campo
     * @return Lista de errores relacionados con ese campo
     */
    List<ErrorImportEntity> findByCampo(String campo);

    /**
     * Busca errores que contengan texto específico en el mensaje de error
     * @param texto Texto a buscar en el error
     * @return Lista de errores que contienen el texto
     */
    @Query("SELECT e FROM ErrorImportEntity e WHERE LOWER(e.error) LIKE LOWER(CONCAT('%', :texto, '%'))")
    List<ErrorImportEntity> findByErrorContaining(@Param("texto") String texto);

    /**
     * Cuenta errores por importación
     * @param importacionId ID de la importación
     * @return Número de errores
     */
    @Query("SELECT COUNT(e) FROM ErrorImportEntity e WHERE e.importacion.id = :importacionId")
    Long countByImportacionId(@Param("importacionId") Long importacionId);

    /**
     * Obtiene resumen de errores por campo para una importación
     * @param importacionId ID de la importación
     * @return Lista de arrays [campo, cantidad_errores]
     */
    @Query("SELECT e.campo, COUNT(e) FROM ErrorImportEntity e WHERE e.importacion.id = :importacionId GROUP BY e.campo")
    List<Object[]> getResumenErroresPorCampo(@Param("importacionId") Long importacionId);

    /**
     * Busca errores por importación ordenados por línea
     * @param importacion La importación
     * @return Lista de errores ordenados por línea
     */
    List<ErrorImportEntity> findByImportacionOrderByLineaAsc(ImportacionEntity importacion);

    /**
     * Elimina todos los errores de una importación
     * @param importacion La importación
     */
    void deleteByImportacion(ImportacionEntity importacion);
}
