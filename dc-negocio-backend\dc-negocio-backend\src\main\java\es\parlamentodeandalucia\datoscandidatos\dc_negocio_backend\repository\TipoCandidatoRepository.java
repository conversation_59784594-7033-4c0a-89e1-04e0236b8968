package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.TipoCandidatoEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface TipoCandidatoRepository extends JpaRepository<TipoCandidatoEntity, Long> {

    /**
     * Busca un tipo de candidato por su valor
     * @param valor El valor del tipo (ej: "TITULAR", "SUPLENTE")
     * @return Optional con el tipo de candidato encontrado
     */
    Optional<TipoCandidatoEntity> findByValor(String valor);

    /**
     * Verifica si existe un tipo de candidato con el valor especificado
     * @param valor El valor a verificar
     * @return true si existe, false en caso contrario
     */
    boolean existsByValor(String valor);

    /**
     * Busca tipos de candidato que contengan el texto especificado en valor o descripción
     * @param texto Texto a buscar
     * @return Lista de tipos que coinciden
     */
    @Query("SELECT t FROM TipoCandidatoEntity t WHERE " +
           "LOWER(t.valor) LIKE LOWER(CONCAT('%', :texto, '%')) OR " +
           "LOWER(t.descripcion) LIKE LOWER(CONCAT('%', :texto, '%'))")
    java.util.List<TipoCandidatoEntity> findByTextoContaining(@Param("texto") String texto);
}
