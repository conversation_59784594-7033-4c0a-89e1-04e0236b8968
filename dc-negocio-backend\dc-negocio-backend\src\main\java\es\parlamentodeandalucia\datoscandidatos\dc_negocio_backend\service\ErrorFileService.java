package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

@Service
public class ErrorFileService {

    private static final Logger logger = LoggerFactory.getLogger(ErrorFileService.class);

    @Value("${app.csv.logs.directory:${user.home}/Documents/ImportacionesCSV}")
    private String logsDirectory;

    @Value("${app.csv.logs.enabled:true}")
    private boolean logsEnabled;

    @Value("${app.csv.logs.max-files:100}")
    private int maxFiles;
    
    /**
     * Genera un archivo de errores basado en el nombre del archivo original
     * @param nombreArchivoOriginal Nombre del archivo CSV original
     * @param errores Lista de errores encontrados
     * @param usuario Usuario que realizó la importación
     * @return Ruta del archivo de errores generado
     */
    public String generarArchivoErrores(String nombreArchivoOriginal, List<String> errores, String usuario) {
        if (!logsEnabled) {
            logger.info("📝 Logs de errores deshabilitados en configuración");
            return null;
        }

        // Generar archivo tanto para errores como para éxitos
        boolean hayErrores = errores != null && !errores.isEmpty();

        try {
            // Generar nombre del archivo de log
            String nombreBase = nombreArchivoOriginal.replaceFirst("[.][^.]+$", ""); // Quitar extensión
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String sufijo = hayErrores ? "_errores_" : "_importacion_";
            String nombreArchivoLog = nombreBase + sufijo + timestamp + ".txt";

            // Usar directorio configurado
            Path rutaCompleta = Paths.get(logsDirectory, nombreArchivoLog);

            logger.info("📝 Generando archivo de log: {}", rutaCompleta.toString());

            // Crear directorio si no existe
            Files.createDirectories(rutaCompleta.getParent());

            // Limpiar archivos antiguos si excede el límite
            limpiarArchivosAntiguos();

            // Escribir contenido al archivo
            try (FileWriter writer = new FileWriter(rutaCompleta.toFile())) {
                if (hayErrores) {
                    writer.write("ARCHIVO DE ERRORES DE IMPORTACIÓN CSV\n");
                    writer.write("=====================================\n");
                } else {
                    writer.write("ARCHIVO DE LOG DE IMPORTACIÓN CSV\n");
                    writer.write("=================================\n");
                }
                writer.write("Archivo original: " + nombreArchivoOriginal + "\n");
                writer.write("Usuario: " + (usuario != null ? usuario : "N/A") + "\n");
                writer.write("Fecha de procesamiento: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss")) + "\n");

                if (hayErrores) {
                    writer.write("Total de errores: " + errores.size() + "\n\n");
                } else {
                    writer.write("Estado: IMPORTACIÓN COMPLETADA SIN ERRORES\n\n");
                }

                if (hayErrores) {
                    // Categorizar errores
                    Map<String, List<String>> erroresPorTipo = categorizarErrores(errores);

                    for (Map.Entry<String, List<String>> categoria : erroresPorTipo.entrySet()) {
                        writer.write("=== " + categoria.getKey() + " ===\n");
                        for (String error : categoria.getValue()) {
                            writer.write("• " + error + "\n");
                        }
                        writer.write("\n");
                    }

                    for (String error : errores) {
                        writer.write(error + "\n");
                    }

                    writer.write("\n--- FIN DEL ARCHIVO DE ERRORES ---\n");
                } else {
                    writer.write("✅ IMPORTACIÓN EXITOSA\n");
                    writer.write("===================\n\n");
                    writer.write("La importación se ha procesado correctamente sin errores.\n");
                    writer.write("Todos los registros del archivo CSV han sido validados e importados exitosamente.\n\n");
                    writer.write("--- FIN DEL LOG DE IMPORTACIÓN ---\n");
                }
            }
            
            logger.info("✅ Archivo de log generado exitosamente: {}", rutaCompleta.toString());
            return rutaCompleta.toString();
            
        } catch (IOException e) {
            logger.error("❌ Error al generar archivo de errores: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * Categoriza errores por tipo para mejor organización en el log
     */
    private Map<String, List<String>> categorizarErrores(List<String> errores) {
        Map<String, List<String>> categorias = new HashMap<>();

        for (String error : errores) {
            String categoria = determinarCategoriaError(error);
            categorias.computeIfAbsent(categoria, k -> new java.util.ArrayList<>()).add(error);
        }

        return categorias;
    }

    private String determinarCategoriaError(String error) {
        if (error.contains("Formación política no encontrada") || error.contains("formacion")) {
            return "ERRORES DE FORMACIÓN POLÍTICA";
        } else if (error.contains("Circunscripción no encontrada") || error.contains("circunscripcion")) {
            return "ERRORES DE CIRCUNSCRIPCIÓN";
        } else if (error.contains("titular") || error.contains("suplente") || error.contains("orden")) {
            return "ERRORES DE TITULARIDAD/ORDEN";
        } else if (error.contains("campo") || error.contains("obligatorio") || error.contains("formato")) {
            return "ERRORES DE FORMATO/CAMPOS";
        } else if (error.contains("Línea incompleta") || error.contains("campos")) {
            return "ERRORES DE ESTRUCTURA CSV";
        } else {
            return "OTROS ERRORES";
        }
    }

    /**
     * Limpia archivos antiguos si excede el límite configurado
     */
    private void limpiarArchivosAntiguos() {
        try {
            Path directorio = Paths.get(logsDirectory);
            if (!Files.exists(directorio)) return;

            File[] archivos = directorio.toFile().listFiles((dir, name) -> name.endsWith("_errores_") && name.endsWith(".txt"));
            if (archivos == null || archivos.length <= maxFiles) return;

            // Ordenar por fecha de modificación (más antiguos primero)
            java.util.Arrays.sort(archivos, (a, b) -> Long.compare(a.lastModified(), b.lastModified()));

            // Eliminar archivos más antiguos
            int archivosAEliminar = archivos.length - maxFiles;
            for (int i = 0; i < archivosAEliminar; i++) {
                Files.deleteIfExists(archivos[i].toPath());
                logger.info("🗑️ Archivo de log antiguo eliminado: {}", archivos[i].getName());
            }

        } catch (IOException e) {
            logger.warn("⚠️ Error al limpiar archivos antiguos: {}", e.getMessage());
        }
    }

    /**
     * Valida si se puede escribir en el directorio de salida
     */
    public boolean validarPermisoEscritura() {
        try {
            Path path = Paths.get(logsDirectory);

            // Crear directorio si no existe
            Files.createDirectories(path);

            // Probar escritura con archivo temporal
            Path archivoTemporal = path.resolve("test_permisos.tmp");
            Files.write(archivoTemporal, "test".getBytes());
            Files.deleteIfExists(archivoTemporal);

            logger.info("✅ Permisos de escritura validados en: {}", logsDirectory);
            return true;

        } catch (IOException e) {
            logger.error("❌ No se puede escribir en el directorio de salida: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Método de compatibilidad para llamadas sin usuario
     */
    public String generarArchivoErrores(String nombreArchivoOriginal, List<String> errores) {
        return generarArchivoErrores(nombreArchivoOriginal, errores, null);
    }
}
