<?xml version="1.0" encoding="UTF-8"?>
<persistence xmlns="https://jakarta.ee/xml/ns/persistence"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xsi:schemaLocation="https://jakarta.ee/xml/ns/persistence https://jakarta.ee/xml/ns/persistence/persistence_3_0.xsd"
             version="3.0">


    <persistence-unit name="user-store" transaction-type="JTA">
        <class>aziz.keycloak.domain.ExternalUserEntity</class>
        <class>aziz.keycloak.domain.ExternalUserRoleEntity</class>
        <properties>
            <property name="hibernate.dialect"
                      value="org.hibernate.dialect.PostgreSQLDialect" />

            <!-- Sets the name of the datasource to be the same as the datasource name in quarkus.properties-->
            <property name="hibernate.connection.datasource"
                      value="user-store" />

            <property name="jakarta.persistence.transactionType"
                      value="JTA" />

            <property name="hibernate.hbm2ddl.auto"
                      value="none" />

            <property name="hibernate.show_sql"
                      value="true" />
        </properties>
    </persistence-unit>

    <persistence-unit name="user-store-2" transaction-type="JTA">
        <class>aziz.keycloak.domain.ExternalUserEntity</class>
        <class>aziz.keycloak.domain.ExternalUserRoleEntity</class>
        <properties>
            <property name="hibernate.dialect"
                      value="org.hibernate.dialect.PostgreSQLDialect" />

            <!-- Sets the name of the datasource to be the same as the datasource name in quarkus.properties-->
            <property name="hibernate.connection.datasource"
                      value="user-store" />

            <property name="jakarta.persistence.transactionType"
                      value="JTA" />

            <property name="hibernate.hbm2ddl.auto"
                      value="none" />

            <property name="hibernate.show_sql"
                      value="false" />
        </properties>
    </persistence-unit>
</persistence>