<div class="layout-container">
    <app-header class="header-fixed"></app-header>

    <div class="page-wrapper scrollable-content">
        <div class="breadcrumbs">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="16px" height="16px"
                style="vertical-align: middle; margin-right: 3px;">
                <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z" />
            </svg>
            <span class="separator">&gt;</span>
            <span>Administración</span>
            <span class="separator">&gt;</span>
            <strong>Formaciones políticas</strong>
        </div>
        <div class="title-section">
            <a routerLink="/home" class="back-button">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="24px"
                    height="24px">
                    <path d="M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z" transform="rotate(180 12 12)" />
                </svg>
            </a>
            <h1>Gestión de Formaciones Políticas</h1>
            <div class="actions">
                <button class="btn-primary" (click)="openFormationSidebar(null)">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24" width="20px"
                        height="20px">
                        <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
                    </svg>
                    Añadir nuevo
                </button>
            </div>
        </div>

        <div class="usuarios-page-container">
            <div class="filter-actions-bar">
                <button class="btn-text" (click)="clearFilters()">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24" width="16px"
                        height="16px">
                        <path
                            d="M6 13c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm14-10v2h-3.21l-3.42 8H18v2h-7.15c-.78 1.76-2.58 3-4.85 3-2.21 0-4-1.79-4-4s1.79-4 4-4c.78 0 1.5.22 2.15.62L12.58 4H20V2h2v2h-2z" />
                    </svg>
                    Limpiar filtros
                </button>
                <div class="dropdown-masivo" (clickOutside)="showMassiveActions = false">
                    <button class="btn-massive" [disabled]="selectedRows.length === 0"
                        (click)="showMassiveActions = !showMassiveActions">
                        Acciones masivas
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                            viewBox="0 0 24 24">
                            <path d="M7 10l5 5 5-5H7z" />
                        </svg>
                    </button>
                    <ul class="dropdown-options" *ngIf="showMassiveActions">
                        <li (click)="activateSelected()">Activar</li>
                        <li (click)="deactivateSelected()">Desactivar</li>
                    </ul>
                </div>
            </div>
            
            <p-table #dt [value]="politicalFormations" [paginator]="true" [rows]="10"  [globalFilterFields]="['code', 'name', 'acronym', 'constituency']" [(selection)]="selectedRows" [rowSelectable]="isRowSelectable" dataKey="id"
                paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown">
                <ng-template pTemplate="header">
                    <tr>
                        <th style="width: 3rem">
                            <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
                        </th>
                        <th pSortableColumn="code">
                            Código
                            <p-sortIcon field="code"></p-sortIcon>
                            <p-columnFilter field="code" matchMode="startsWith" display="menu"
                                type="text"></p-columnFilter>
                        </th>
                        <th pSortableColumn="name">
                            Nombre
                            <p-sortIcon field="name"></p-sortIcon>
                            <p-columnFilter field="name" matchMode="startsWith" display="menu"
                                type="text"></p-columnFilter>
                        </th>
                        <th pSortableColumn="acronym">
                            Siglas
                            <p-sortIcon field="acronym"></p-sortIcon>
                            <p-columnFilter field="acronym" matchMode="startsWith" display="menu"
                                type="text"></p-columnFilter>
                        </th>
                        <th pSortableColumn="constituency">
                            Circunscripción
                            <p-sortIcon field="constituency"></p-sortIcon>
                            <p-columnFilter field="constituency" matchMode="equals" display="menu">
                                <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                    <p-dropdown [options]="constituencies" [ngModel]="value"
                                        (onChange)="filter($event.value)" optionLabel="label" optionValue="value"
                                        placeholder="Seleccionar circunscripción" class="w-full">
                                    </p-dropdown>
                                </ng-template>
                            </p-columnFilter>
                        </th>
                        
                        <th pSortableColumn="activeCandidacies">Candidaturas activas<p-sortIcon field="activeCandidacies"></p-sortIcon></th>
                       
                        <th style="width: 5rem; text-align: center;">Acciones</th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-formation>
                    <tr [pSelectableRow]="formation">
                        <td>
                            <p-tableCheckbox [value]="formation"></p-tableCheckbox>
                        </td>
                        <td>{{ formation.code }}</td>
                        <td>{{ formation.name }}</td>
                        <td>{{ formation.acronym }}</td>
                        <td>{{ formation.constituency }}</td>
                        <td>
                            <span class="status-badge" [ngClass]="formation.activeCandidacies ? 'status-yes' : 'status-no'">
                                {{ formation.activeCandidacies ? 'Sí' : 'No' }}
                            </span>
                        </td>
                        <td class="acciones-cell" (clickOutside)="openedDropdownId = null">
                            <div class="dropdown">
                                <button class="btn-icon dropdown-toggle" (click)="toggleDropdown(formation.id)">
                                    Acciones
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor"
                                        viewBox="0 0 24 24">
                                        <path d="M12 7a2 2 0 1 0 0-4 2 2 0 0 0 0 4zm0 2a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm0 6a2 2 0 1 0 0 4 2 2 0 0 0 0-4z" />
                                    </svg>
                                </button>
                                <div class="dropdown-menu" *ngIf="openedDropdownId === formation.id">
                                    <a class="dropdown-item" (click)="openFormationSidebar(formation)">
                                        Editar
                                    </a>
                                    <a class="dropdown-item" *ngIf="formation.activeCandidacies"
                                        (click)="deactivateThisFormation(formation.id)">
                                        Desactivar
                                    </a>
                                    <a class="dropdown-item" *ngIf="!formation.activeCandidacies"
                                        (click)="activateThisFormation(formation.id)">
                                        Activar
                                    </a>
                                </div>
                            </div>
                        </td>
                    </tr>
                </ng-template>
                <ng-template pTemplate="emptymessage">
                    <tr>
                        <td colspan="9" class="p-text-center">No se han encontrado formaciones políticas.</td>
                    </tr>
                </ng-template>
            </p-table>
        </div>
    </div>
    
    <app-footer></app-footer>
</div>

<p-dialog [(visible)]="showFormationSidebar" [modal]="true" [style]="{width: '500px'}" [header]="sidebarTitle"
    [draggable]="false" [resizable]="false">
    <form (ngSubmit)="saveFormation()">
        <div class="form-group">
            <label for="code">Código</label>
            <input id="code" type="text" pInputText [(ngModel)]="newFormation.code" name="code" required />
        </div>
        <div class="form-group">
            <label for="name">Nombre</label>
            <input id="name" type="text" pInputText [(ngModel)]="newFormation.name" name="name" required />
        </div>
        <div class="form-group">
            <label for="acronym">Siglas</label>
            <input id="acronym" type="text" pInputText [(ngModel)]="newFormation.acronym" name="acronym" required />
        </div>
        <div class="form-group">
            <label for="constituency">Circunscripción</label>
            <p-dropdown [options]="constituencies" [(ngModel)]="newFormation.constituency" name="constituency" placeholder="Seleccionar circunscripción"></p-dropdown>
        </div>
        <div class="checkbox-group">
            <p-checkbox [(ngModel)]="newFormation.activeCandidacies" name="activeCandidacies" [binary]="true" inputId="activeCandidacies"></p-checkbox>
            <label for="activeCandidacies" class="p-checkbox-label ml-2">Candidaturas activas</label>
        </div>
        <div class="sidebar-footer">
            <button type="button" class="btn-secondary" (click)="showFormationSidebar = false">Cancelar</button>
            <button type="submit" class="btn-primary">Guardar</button>
        </div>
    </form>
</p-dialog>