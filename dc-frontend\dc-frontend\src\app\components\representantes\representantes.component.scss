:host {
  display: block;
  font-family: 'Inter', sans-serif;
}

.breadcrumbs {
  margin: 0 auto 20px auto;
  max-width: 1400px;
  width: calc(100% - 40px);
  font-size: 14px;
  color: #666;
  margin-top: 10px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}
.breadcrumbs .separator {
  margin: 0 5px;
  color: #999;
}
.breadcrumbs strong {
  color: #333;
  font-weight: 600;
}

.page-header {
  width: 100%;
  margin: 20px auto 0 auto;
  padding: 0 20px;
  box-sizing: border-box;
}

.title-section {
  margin: 0 auto 20px auto;
  max-width: 1400px;
  width: calc(100% - 40px);
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: nowrap;
  width: 100%;
}
.title-section .back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  color: #333;
  text-decoration: none;
  padding: 0;
  flex-shrink: 0;
}
.title-section .back-button svg {
  width: 24px;
  height: 24px;
  color: #555;
}
.title-section .page-main-title {
  font-size: 28px;
  font-weight: 600;
  color: #333;
  margin-left: 5px;
  white-space: nowrap;
  flex-shrink: 0;
}
.title-section .header-actions {
  display: flex;
  gap: 10px;
  margin-left: auto;
  flex-wrap: nowrap;
  flex-shrink: 0;
}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: 'Inter', sans-serif;
  overflow-y: auto;
  overflow-x: hidden;
}

.layout-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

.header-fixed {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 10;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  height: 60px;
}

.footer-fixed {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 10;
  background-color: #fff;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
  height: 50px;
}

.page-wrapper {
  margin-top: 60px;
  margin-bottom: 50px;
  overflow-y: auto;
  background-color: #f8f8f8;
  padding: 0;
}

.usuarios-page-container {
  flex: 1;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 16px;
  margin: 0 auto 20px auto;
  max-width: 1400px;
  width: 1400px;
  display: flex;
  flex-direction: column;
}

/* Buttons */
.btn-primary,
.btn-text,
.btn-icon {
  padding: 10px 18px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.2s ease, box-shadow 0.2s ease;
}

.btn-primary {
  background-color: #4CAF50;
  color: #fff;
}
.btn-primary:hover {
  background-color: #45a049;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

.btn-secondary {
  padding: 10px 18px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  transition: background-color 0.2s ease, box-shadow 0.2s ease;
}
.btn-secondary:hover {
  background-color: #d5d5d5;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn-text {
  background: none;
  color: #007bff;
  padding: 8px 12px;
}
.btn-text:hover {
  text-decoration: underline;
  background-color: rgba(0, 123, 255, 0.05);
}

.btn-icon {
  background: none;
  border: none;
  padding: 5px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
}
.btn-icon:hover {
  background-color: #f0f0f0;
}
.btn-icon svg {
  width: 20px;
  height: 20px;
}

/* Filtros y acciones */
.filter-actions-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 10px 10px;
  flex-wrap: wrap;
  gap: 15px;
}

/* Dropdown */
.dropdown {
  position: relative;
  display: inline-block;
}
.dropdown-toggle {
  display: flex;
  align-items: center;
  gap: 5px;
}
.dropdown-menu {
  display: none;
  position: absolute;
  background-color: #fff;
  min-width: 160px;
  box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.2);
  z-index: 1;
  border-radius: 6px;
  overflow: hidden;
  top: 100%;
  left: 0;
  margin-top: 5px;
}
.dropdown-item {
  color: #333;
  padding: 12px 16px;
  text-decoration: none;
  display: block;
  cursor: pointer;
  transition: background-color 0.2s ease;
}
.dropdown-item:hover {
  background-color: #f1f1f1;
}
.dropdown:hover .dropdown-menu {
  display: block;
}

/* Tabla */
.table-container {
  max-height: 500px;
  margin-bottom: 20px;
}

table {
  width: 100%;
  border-collapse: collapse;
  min-width: 800px; 
  table-layout: fixed; 
}

thead {
  background-color: #f5f5f5;
}

thead th {
  width: 100%; /* This sets a fixed width for ALL headers */
  padding: 12px 15px;
  text-align: left;
  font-size: 14px;
  font-weight: 600;
  color: #555;
  white-space: nowrap; 
  position: sticky;
  top: 0;
  z-index: 2;
  background-color: #f5f5f5;
}

thead .sort-arrow {
  font-size: 10px;
  margin-left: 5px;
  color: #999;
}

tbody tr {
  border-bottom: 1px solid #eee;
}
tbody tr:last-child {
  border-bottom: none;
}
tbody tr:hover {
  background-color: #f9f9f9;
}

tbody td {
  align-items: center;
  padding: 12px 15px;
  font-size: 14px;
  color: #444;
  white-space: nowrap; 
}

.wrap-email {
  white-space: normal !important;
  word-break: break-word;
  overflow-wrap: break-word;
}



/* Badges */
.status-badge,
.role-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
}

.status-active {
  background-color: #e6ffe6;
  color: #28a745;
}
.status-inactive {
  background-color: #ffe6e6;
  color: #dc3545;
}
.role-admin {
  background-color: #e0f2f7;
  color: #2196f3;
}
.role-candidate {
  background-color: #e6ffe6;
  color: #4CAF50;
}
.role-representative {
  background-color: #fff3e0;
  color: #ff9800;
}
.role-electoral-board {
  background-color: #f3e5f5;
  color: #9c27b0;
}

/* Paginación */
.pagination {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 5px;
  margin-top: 20px;
  padding-right: 10px;
}

.pagination-arrow,
.pagination-number {
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  color: #555;
  transition: background-color 0.2s ease, color 0.2s ease;
}
.pagination-number.active {
  background-color: #007bff;
  color: #fff;
  border-color: #007bff;
}
.pagination-number.active:hover {
  background-color: #0056b3;
}
.pagination-ellipsis {
  padding: 8px 0;
  color: #777;
}

/* === OVERLAY === */
.overlay {
  position: fixed;
  top: 0; left: 0;
  width: 100%; height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 99;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}
.overlay.show {
  opacity: 1;
  visibility: visible;
}

/* === SIDEBAR === */
.sidebar {
  position: fixed;
  top: 0;
  right: -420px;
  width: 400px;
  height: 100%;
  background-color: #fff;
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.2);
  z-index: 100;
  display: flex;
  flex-direction: column;
  transition: right 0.3s ease;
  font-family: 'Inter', sans-serif;
}
.sidebar.show {
  right: 0;
}

/* === HEADER === */
.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid #eee;
  font-size: 18px;
  font-weight: 600;
  color: #222;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* === CONTENT === */
.sidebar-content {
  padding: 20px;
  flex: 1;
  overflow-y: auto;
}
.sidebar-content .form-group {
  margin-bottom: 18px;
}
.sidebar-content label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  font-size: 14px;
  color: #444;
}
.sidebar-content select,
.sidebar-content input[type="text"] {
  width: 100%;
  padding: 10px;
  font-size: 14px;
  border: 1px solid #ccc;
  border-radius: 6px;
  box-sizing: border-box;
}

/* === CHECKBOX LIST === */
.checkbox-list {
  margin-top: 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 10px;
  max-height: 300px;
  overflow-y: auto;
}
.checkbox-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  color: #444;
}
.checkbox-item input[type="checkbox"] {
  margin-right: 10px;
  width: 16px;
  height: 16px;
  accent-color: #C2E038;
}
.checkbox-item:last-child {
  margin-bottom: 0;
}

/* === FOOTER === */
.sidebar-footer {
  padding: 15px 20px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  background-color: #f8f8f8;
}
.sidebar-footer .btn-secondary {
  background-color: #f1f1f1;
  border: none;
  padding: 10px 18px;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
}
.sidebar-footer .btn-primary {
  background-color: #C2E038;
  border: none;
  padding: 10px 18px;
  border-radius: 6px;
  font-weight: 500;
  color: #333;
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
}
.sidebar-footer .btn-primary svg {
  width: 18px;
  height: 18px;
}

input[type="checkbox"] {
  appearance: none;
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  border: 2px solid #bbb;
  border-radius: 4px;
  display: inline-block;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

input[type="checkbox"]:checked {
  background-color: #C2E038;
  border-color: #C2E038;
}

input[type="checkbox"]:checked::after {
  content: "✔";
  position: absolute;
  font-size: 12px;
  font-weight: bold;
  color: #fff;
  top: 0;
  left: 2px;
}
