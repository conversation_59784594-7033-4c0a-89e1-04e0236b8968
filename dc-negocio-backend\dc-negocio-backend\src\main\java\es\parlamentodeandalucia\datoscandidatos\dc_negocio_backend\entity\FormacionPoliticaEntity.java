package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;

@Entity
@Table(name = "dac_t_formacion_politica")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class FormacionPoliticaEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "dac_id_formacion_politica")
    private Long id;

    @Column(name = "dac_tx_nombre", unique = true)
    private String nombre;

    @Column(name = "dac_tx_siglas", unique = true)
    private String siglas;

    @Column(name = "dac_tx_codigo_interno")
    private String codigoInterno;
    
    @Column(name = "dac_bl_activa")
    private Boolean activa;

    public FormacionPoliticaEntity() {
        // Constructor por defecto
    }

    public FormacionPoliticaEntity(Long id, String nombre, String siglas, String codigoInterno, Boolean activa) {
        this.id = id;
        this.nombre = nombre;
        this.siglas = siglas;
        this.codigoInterno = codigoInterno;
        this.activa = activa;
    }
    
    // Getters y setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNombre() {
        return nombre;
    }

    public void setNombre(String nombre) {
        this.nombre = nombre;
    }

    public String getSiglas() {
        return siglas;
    }

    public void setSiglas(String siglas) {
        this.siglas = siglas;
    }

    public String getCodigoInterno() {
        return codigoInterno;
    }

    public void setCodigoInterno(String codigoInterno) {
        this.codigoInterno = codigoInterno;
    }
    
    public Boolean getActiva() {
        return activa;
    }

    public void setActiva(Boolean activa) {
        this.activa = activa;
    }
}