package es.parlamentodeandalucia.sgi.keycloak.spi.provider;

import es.parlamentodeandalucia.sgi.keycloak.spi.adapter.SgiUserAdapter;
import es.parlamentodeandalucia.sgi.keycloak.spi.entity.SgiCredencialEntity;
import org.jboss.logging.Logger;
import org.keycloak.component.ComponentModel;
import org.keycloak.credential.CredentialInput;
import org.keycloak.credential.CredentialInputValidator;
import org.keycloak.credential.CredentialModel;
import org.keycloak.models.*;
import org.keycloak.storage.StorageId;
import org.keycloak.storage.UserStorageProvider;
import org.keycloak.storage.user.UserLookupProvider;
import org.keycloak.storage.user.UserQueryProvider;
import org.mindrot.jbcrypt.BCrypt;

import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.TypedQuery;
import java.util.*;
import java.util.stream.Stream;

/**
 * Proveedor principal del SGI User Storage SPI
 * Implementa la lógica de autenticación y búsqueda de usuarios
 */
public class SgiUserStorageProvider implements 
    UserStorageProvider, 
    UserLookupProvider, 
    UserQueryProvider,
    CredentialInputValidator {

    private static final Logger logger = Logger.getLogger(SgiUserStorageProvider.class);

    private final KeycloakSession session;
    private final ComponentModel model;
    private final EntityManager entityManager;

    public SgiUserStorageProvider(KeycloakSession session, ComponentModel model, EntityManager entityManager) {
        this.session = session;
        this.model = model;
        this.entityManager = entityManager;
    }

    @Override
    public void close() {
        // El EntityManager se cierra automáticamente por Keycloak
    }

    // ========================================
    // UserLookupProvider Implementation
    // ========================================

    @Override
    public UserModel getUserById(RealmModel realm, String id) {
        logger.infof("Buscando usuario por ID: %s", id);
        
        String externalId = StorageId.externalId(id);
        try {
            Long credencialId = Long.parseLong(externalId);
            SgiCredencialEntity credencial = entityManager.find(SgiCredencialEntity.class, credencialId);
            
            if (credencial != null && credencial.isAccountActive()) {
                logger.infof("Usuario encontrado por ID: %s -> %s", id, credencial.getUsername());
                return new SgiUserAdapter(session, realm, model, credencial);
            }
        } catch (NumberFormatException e) {
            logger.warnf("ID de usuario inválido: %s", externalId);
        }
        
        logger.infof("Usuario no encontrado por ID: %s", id);
        return null;
    }

    @Override
    public UserModel getUserByUsername(RealmModel realm, String username) {
        logger.infof("Buscando usuario por username: %s", username);
        
        try {
            TypedQuery<SgiCredencialEntity> query = entityManager.createNamedQuery(
                "SgiCredencialEntity.findByUsername", SgiCredencialEntity.class);
            query.setParameter("username", username);
            
            SgiCredencialEntity credencial = query.getSingleResult();
            
            if (credencial.isAccountActive()) {
                logger.infof("Usuario encontrado por username: %s", username);
                return new SgiUserAdapter(session, realm, model, credencial);
            } else {
                logger.infof("Usuario encontrado pero inactivo: %s", username);
            }
        } catch (NoResultException e) {
            logger.infof("Usuario no encontrado por username: %s", username);
        }
        
        return null;
    }

    @Override
    public UserModel getUserByEmail(RealmModel realm, String email) {
        logger.infof("Buscando usuario por email: %s", email);

        try {
            // Buscar por email en la identidad asociada
            String jpql = "SELECT c FROM SgiCredencialEntity c JOIN c.identidad i WHERE i.email = :email";
            TypedQuery<SgiCredencialEntity> query = entityManager.createQuery(jpql, SgiCredencialEntity.class);
            query.setParameter("email", email);

            SgiCredencialEntity credencial = query.getSingleResult();

            if (credencial.isAccountActive()) {
                logger.infof("Usuario encontrado por email: %s", email);
                return new SgiUserAdapter(session, realm, model, credencial);
            } else {
                logger.infof("Usuario encontrado por email pero inactivo: %s", email);
            }
        } catch (NoResultException e) {
            logger.infof("Usuario no encontrado por email: %s", email);
        }

        return null;
    }

    // ========================================
    // UserQueryProvider Implementation
    // ========================================

    @Override
    public int getUsersCount(RealmModel realm) {
        logger.info("Contando usuarios activos del SGI");
        
        TypedQuery<Long> query = entityManager.createNamedQuery(
            "SgiCredencialEntity.countActiveUsers", Long.class);
        
        Long count = query.getSingleResult();
        logger.infof("Total de usuarios activos: %d", count);
        
        return count.intValue();
    }



    @Override
    public Stream<UserModel> searchForUserStream(RealmModel realm, String search, Integer firstResult, Integer maxResults) {
        logger.infof("Buscando usuarios con término: %s", search);
        
        // Buscar por username, email, nombre, firstName, lastName
        String searchTerm = "%" + search.toLowerCase() + "%";
        
        String jpql = "SELECT c FROM SgiCredencialEntity c JOIN c.estado e JOIN c.identidad i WHERE " +
                     "e.valor = 'ACTIVO' AND " +
                     "(LOWER(c.username) LIKE :search OR " +
                     "LOWER(i.email) LIKE :search OR " +
                     "LOWER(i.nombre) LIKE :search OR " +
                     "LOWER(i.apellido1) LIKE :search OR " +
                     "LOWER(i.apellido2) LIKE :search OR " +
                     "LOWER(i.identificador) LIKE :search)";
        
        TypedQuery<SgiCredencialEntity> query = entityManager.createQuery(jpql, SgiCredencialEntity.class);
        query.setParameter("search", searchTerm);
        
        if (firstResult != null) {
            query.setFirstResult(firstResult);
        }
        if (maxResults != null) {
            query.setMaxResults(maxResults);
        }
        
        return query.getResultList().stream()
            .map(credencial -> new SgiUserAdapter(session, realm, model, credencial));
    }

    @Override
    public Stream<UserModel> searchForUserStream(RealmModel realm, Map<String, String> params, Integer firstResult, Integer maxResults) {
        logger.infof("Búsqueda avanzada de usuarios con parámetros: %s", params);
        
        StringBuilder jpql = new StringBuilder("SELECT c FROM SgiCredencialEntity c JOIN c.estado e JOIN c.identidad i WHERE ");
        jpql.append("e.valor = 'ACTIVO'");

        Map<String, Object> queryParams = new HashMap<>();

        if (params.containsKey("username")) {
            jpql.append(" AND LOWER(c.username) LIKE :username");
            queryParams.put("username", "%" + params.get("username").toLowerCase() + "%");
        }
        
        if (params.containsKey("email")) {
            jpql.append(" AND LOWER(i.email) LIKE :email");
            queryParams.put("email", "%" + params.get("email").toLowerCase() + "%");
        }

        if (params.containsKey("firstName")) {
            jpql.append(" AND LOWER(i.nombre) LIKE :firstName");
            queryParams.put("firstName", "%" + params.get("firstName").toLowerCase() + "%");
        }

        if (params.containsKey("lastName")) {
            jpql.append(" AND (LOWER(i.apellido1) LIKE :lastName OR LOWER(i.apellido2) LIKE :lastName)");
            queryParams.put("lastName", "%" + params.get("lastName").toLowerCase() + "%");
        }
        
        TypedQuery<SgiCredencialEntity> query = entityManager.createQuery(jpql.toString(), SgiCredencialEntity.class);
        
        for (Map.Entry<String, Object> param : queryParams.entrySet()) {
            query.setParameter(param.getKey(), param.getValue());
        }
        
        if (firstResult != null) {
            query.setFirstResult(firstResult);
        }
        if (maxResults != null) {
            query.setMaxResults(maxResults);
        }
        
        return query.getResultList().stream()
            .map(credencial -> new SgiUserAdapter(session, realm, model, credencial));
    }

    @Override
    public Stream<UserModel> getGroupMembersStream(RealmModel realm, GroupModel group, Integer firstResult, Integer maxResults) {
        // No implementamos grupos en el SGI por ahora
        return Stream.empty();
    }

    @Override
    public Stream<UserModel> searchForUserByUserAttributeStream(RealmModel realm, String attrName, String attrValue) {
        logger.infof("Buscando usuarios por atributo: %s = %s", attrName, attrValue);
        
        if ("nif".equals(attrName)) {
            try {
                TypedQuery<SgiCredencialEntity> query = entityManager.createNamedQuery(
                    "SgiCredencialEntity.findByIdentificador", SgiCredencialEntity.class);
                query.setParameter("identificador", attrValue);

                SgiCredencialEntity credencial = query.getSingleResult();
                if (credencial.isAccountActive()) {
                    return Stream.of(new SgiUserAdapter(session, realm, model, credencial));
                }
            } catch (NoResultException e) {
                logger.infof("Usuario no encontrado por NIF: %s", attrValue);
            }
        }
        
        return Stream.empty();
    }

    // ========================================
    // CredentialInputValidator Implementation
    // ========================================

    @Override
    public boolean supportsCredentialType(String credentialType) {
        return CredentialModel.PASSWORD.equals(credentialType);
    }

    @Override
    public boolean isConfiguredFor(RealmModel realm, UserModel user, String credentialType) {
        return supportsCredentialType(credentialType) && getPassword(user) != null;
    }

    @Override
    public boolean isValid(RealmModel realm, UserModel user, CredentialInput credentialInput) {
        if (!supportsCredentialType(credentialInput.getType())) {
            return false;
        }

        String password = getPassword(user);
        if (password == null) {
            logger.warnf("No se encontró contraseña para el usuario: %s", user.getUsername());
            return false;
        }

        String inputPassword = credentialInput.getChallengeResponse();
        if (inputPassword == null) {
            logger.warnf("Contraseña de entrada es null para el usuario: %s", user.getUsername());
            return false;
        }

        logger.infof("Validando contraseña para usuario: %s", user.getUsername());

        try {
            boolean isValid = BCrypt.checkpw(inputPassword, password);

            if (isValid) {
                logger.infof("Contraseña válida para usuario: %s", user.getUsername());
                updateLastLogin(user);
            } else {
                logger.infof("Contraseña inválida para usuario: %s", user.getUsername());
                incrementFailedAttempts(user);
            }

            return isValid;
        } catch (Exception e) {
            logger.errorf(e, "Error validando contraseña para usuario: %s", user.getUsername());
            return false;
        }
    }

    // ========================================
    // Métodos de utilidad privados
    // ========================================

    private String getPassword(UserModel user) {
        if (user instanceof SgiUserAdapter) {
            SgiUserAdapter adapter = (SgiUserAdapter) user;
            return adapter.getSgiCredencial().getPassword();
        }
        return null;
    }

    private void updateLastLogin(UserModel user) {
        if (user instanceof SgiUserAdapter) {
            SgiUserAdapter adapter = (SgiUserAdapter) user;
            SgiCredencialEntity credencial = adapter.getSgiCredencial();
            credencial.updateLastLogin();

            try {
                entityManager.merge(credencial);
                entityManager.flush();
                logger.infof("Último login actualizado para usuario: %s", user.getUsername());
            } catch (Exception e) {
                logger.errorf(e, "Error actualizando último login para usuario: %s", user.getUsername());
            }
        }
    }

    private void incrementFailedAttempts(UserModel user) {
        if (user instanceof SgiUserAdapter) {
            SgiUserAdapter adapter = (SgiUserAdapter) user;
            SgiCredencialEntity credencial = adapter.getSgiCredencial();
            credencial.incrementFailedLoginAttempts();

            try {
                entityManager.merge(credencial);
                entityManager.flush();

                if (credencial.getAccountLocked()) {
                    logger.warnf("Cuenta bloqueada por intentos fallidos para usuario: %s", user.getUsername());
                } else {
                    logger.infof("Incrementados intentos fallidos para usuario: %s (total: %d)",
                        user.getUsername(), credencial.getFailedLoginAttempts());
                }
            } catch (Exception e) {
                logger.errorf(e, "Error incrementando intentos fallidos para usuario: %s", user.getUsername());
            }
        }
    }
}
