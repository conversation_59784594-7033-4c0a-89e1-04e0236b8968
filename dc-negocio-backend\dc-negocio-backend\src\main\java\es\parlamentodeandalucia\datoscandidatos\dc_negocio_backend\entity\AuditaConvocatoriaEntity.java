package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity;

import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;

@Entity
@Table(name = "dac_t_audita_convoc")
public class AuditaConvocatoriaEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "dac_id_audita_convoc")
    private Long id;

    @Column(name = "dac_tx_columna")
    private String columna;

    @Column(name = "dac_tx_valor_anterior")
    private String valorAnterior;

    @Column(name = "dac_tx_valor_nuevo")
    private String valorNuevo;

    @Column(name = "dac_fh_modificacion")
    private Date fechaModificacion;

    @Column(name = "dac_tx_motivo")
    private String motivo;

    @ManyToOne
    @JoinColumn(name = "dac_fk_convocatoria")
    private ConvocatoriaEntity convocatoria;

   /*@ManyToOne
    @JoinColumn(name = "dac_fk_usuario")
    private UsuarioEntity usuario; */ 
    

    // Constructor por defecto
    public AuditaConvocatoriaEntity() {}

    // Constructor con parámetros
    public AuditaConvocatoriaEntity(Long id, String columna, String valorAnterior, String valorNuevo, Date fechaModificacion, String motivo, ConvocatoriaEntity convocatoria, UsuarioEntity usuario) {
        this.id = id;
        this.columna = columna;
        this.valorAnterior = valorAnterior;
        this.valorNuevo = valorNuevo;
        this.fechaModificacion = fechaModificacion;
        this.motivo = motivo;
        this.convocatoria = convocatoria;
        //this.usuario = usuario;
    }

    // Getters y Setters

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getColumna() {
        return columna;
    }

    public void setColumna(String columna) {
        this.columna = columna;
    }

    public String getValorAnterior() {
        return valorAnterior;
    }

    public void setValorAnterior(String valorAnterior) {
        this.valorAnterior = valorAnterior;
    }

    public String getValorNuevo() {
        return valorNuevo;
    }

    public void setValorNuevo(String valorNuevo) {
        this.valorNuevo = valorNuevo;
    }

    public Date getFechaModificacion() {
        return fechaModificacion;
    }

    public void setFechaModificacion(Date fechaModificacion) {
        this.fechaModificacion = fechaModificacion;
    }

    public String getMotivo() {
        return motivo;
    }

    public void setMotivo(String motivo) {
        this.motivo = motivo;
    }

    public ConvocatoriaEntity getConvocatoria() {
        return convocatoria;
    }

    public void setConvocatoria(ConvocatoriaEntity convocatoria) {
        this.convocatoria = convocatoria;
    }

  /*   public UsuarioEntity getUsuario() {
        return usuario;
    }

    public void setUsuario(UsuarioEntity usuario) {
        this.usuario = usuario;
    }*/

}
