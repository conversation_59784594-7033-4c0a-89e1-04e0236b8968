package es.parlamentodeandalucia.sgi.sgi_negocio_backend.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

/**
 * Entidad principal para credenciales del SGI
 * Mapea con la tabla sgi_t_credencial y los campos del User Profile de Keycloak
 */
@Entity
@Table(name = "sgi_t_credencial")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SgiCredencialEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "sgi_id_credencial")
    private Long id;

    @Column(name = "sgi_username", nullable = false, unique = true)
    private String username;

    @Column(name = "sgi_password", nullable = false)
    private String password;

    @Column(name = "sgi_email")
    private String email;

    @Column(name = "sgi_first_name")
    private String firstName;

    @Column(name = "sgi_last_name")
    private String lastName;

    @Column(name = "sgi_nombre")
    private String nombre;

    @Column(name = "sgi_nif")
    private String nif;

    @Column(name = "sgi_enabled")
    @Builder.Default
    private Boolean enabled = true;

    @Column(name = "sgi_created_date")
    @Builder.Default
    private LocalDateTime createdDate = LocalDateTime.now();

    @Column(name = "sgi_last_login")
    private LocalDateTime lastLogin;

    @Column(name = "sgi_failed_login_attempts")
    @Builder.Default
    private Integer failedLoginAttempts = 0;

    @Column(name = "sgi_account_locked")
    @Builder.Default
    private Boolean accountLocked = false;

    // NOTA: Los roles se gestionan en el sistema DAC, no en el SGI

    /**
     * Método para verificar si la cuenta está activa
     */
    public boolean isAccountActive() {
        return enabled != null && enabled && 
               (accountLocked == null || !accountLocked);
    }

    /**
     * Método para incrementar intentos fallidos de login
     */
    public void incrementFailedLoginAttempts() {
        if (failedLoginAttempts == null) {
            failedLoginAttempts = 0;
        }
        failedLoginAttempts++;
        
        // Bloquear cuenta después de 5 intentos fallidos
        if (failedLoginAttempts >= 5) {
            accountLocked = true;
        }
    }

    /**
     * Método para resetear intentos fallidos de login
     */
    public void resetFailedLoginAttempts() {
        failedLoginAttempts = 0;
        accountLocked = false;
    }

    /**
     * Método para actualizar último login
     */
    public void updateLastLogin() {
        lastLogin = LocalDateTime.now();
        resetFailedLoginAttempts();
    }

    @PrePersist
    protected void onCreate() {
        if (createdDate == null) {
            createdDate = LocalDateTime.now();
        }
        if (enabled == null) {
            enabled = true;
        }
        if (failedLoginAttempts == null) {
            failedLoginAttempts = 0;
        }
        if (accountLocked == null) {
            accountLocked = false;
        }
    }
}
