:host ::ng-deep {
  .p-dialog {
    border-radius: 12px;
  }

  .modal-title {
    font-size: 18px;
    font-weight: 600;
    color: #2d2d2d;
  }

  .p-dropdown.custom-dropdown {
    margin-top: 12px;
    border: 2px solid #C2E038;
    border-radius: 8px;
    height: 42px;
    font-size: 14px;
    padding: 0 12px;
    display: flex;
    align-items: center;
  }

  .p-dropdown-label {
    padding: 6px 12px;
    font-weight: 500;
    color: #2d2d2d;
  }

  .p-dropdown-panel {
    z-index: 9999 !important;
    font-size: 14px;
    border-radius: 8px;
    padding: 4px 0;
    min-width: 260px;
  }

  .p-dropdown-item {
    padding: 10px 16px;
    font-weight: 500;
    color: #2d2d2d;
    transition: background-color 0.2s ease;
  }

  .p-dropdown-item:hover {
    background-color: #ECF8D3;
  }

  .p-dropdown-item.p-highlight {
    background-color: #C2E038 !important;
    color: #000 !important;
  }

  .modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
  }

  .btn-primary{
    padding: 6px 20px;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    color: #000;
    transition: background-color 0.2s ease;
    background-color: #C2E038;
  }

  .btn-secondary {
    padding: 6px 20px;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    color: #000;
    transition: background-color 0.2s ease;
    background-color: #F3F4F6;
  }

  .btn-primary:hover {
    background-color: #A8CB5B;
  }

  .btn-secondary:hover {
    background-color: #FECACA;
  }

  .p-multiselect.custom-dropdown {
    margin-top: 12px;
    border: 2px solid #C2E038;
    border-radius: 8px;
    font-size: 14px;
    height: 42px;
    padding: 0 12px;
    display: flex;
    align-items: center;
  }

}
