package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity;

import jakarta.persistence.*;

@Entity
@Table(name = "dac_t_tipo_candidato")
public class TipoCandidatoEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "dac_id_tipo_candidato")
    private Long id;

    @Column(name = "dac_tx_valor")
    private String valor;

    @Column(name = "dac_tx_descripcion")
    private String descripcion;

    // Constructor por defecto
    public TipoCandidatoEntity() {
    }

    // Constructor con parámetros
    public TipoCandidatoEntity(Long id, String valor, String descripcion) {
        this.id = id;
        this.valor = valor;
        this.descripcion = descripcion;
    }

    // Getters y Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getValor() {
        return valor;
    }

    public void setValor(String valor) {
        this.valor = valor;
    }

    public String getDescripcion() {
        return descripcion;
    }

    public void setDescripcion(String descripcion) {
        this.descripcion = descripcion;
    }

    @Override
    public String toString() {
        return "TipoCandidatoEntity{" +
                "id=" + id +
                ", valor='" + valor + '\'' +
                ", descripcion='" + descripcion + '\'' +
                '}';
    }
}
