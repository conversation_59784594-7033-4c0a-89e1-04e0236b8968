// representantes.component.ts
import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FooterComponent } from "../footer/footer.component";
import { HeaderComponent } from '../header/header.component';
import { FormsModule } from '@angular/forms';

interface Representante {
  id: number;
  nombre: string;
  formacion: string;
  candidatosAsignados: number;
  ultimaModificacion: string;
  modificadoPor: string;
}

@Component({
  selector: 'app-representantes',
  standalone: true,
  imports: [CommonModule, RouterModule, FooterComponent, HeaderComponent, FormsModule],
  templateUrl: './representantes.component.html',
  styleUrls: ['./representantes.component.scss']
})

export class RepresentantesComponent {
  
  selectedCandidatos: string[] = [];
  selectAll: boolean = false;

  representantes: Representante[] = [
    {
      id: 1,
      nombre: '<PERSON>',
      formacion: 'G. <PERSON>',
      candidatosAsignados: 50,
      ultimaModificacion: '02/02/2025 10:00:05',
      modificadoPor: 'Francisco <PERSON>íz'
    },
    {
      id: 2,
      nombre: '<PERSON>',
      formacion: 'G. Navarra',
      candidatosAsignados: 42,
      ultimaModificacion: '01/02/2025 09:15:33',
      modificadoPor: 'Eva Pacheco Martín'
    },
    {
      id: 3,
      nombre: 'Alvaro Moreno López',
      formacion: 'G. Andalucía',
      candidatosAsignados: 18,
      ultimaModificacion: '30/01/2025 11:43:12',
      modificadoPor: 'Manuel Toscano Díaz'
    },
    {
      id: 4,
      nombre: 'Sandra Santos Ruiz',
      formacion: 'G. Galicia',
      candidatosAsignados: 25,
      ultimaModificacion: '29/01/2025 08:05:00',
      modificadoPor: 'David González Ramos'
    },
    {
      id: 5,
      nombre: 'Lucia García García',
      formacion: 'G. Cataluña',
      candidatosAsignados: 9,
      ultimaModificacion: '28/01/2025 17:44:20',
      modificadoPor: 'Ana Herrera Navarro'
    },
    {
      id: 6,
      nombre: 'Rosa Garcia Fernández',
      formacion: 'G. Madrid',
      candidatosAsignados: 33,
      ultimaModificacion: '27/01/2025 16:10:41',
      modificadoPor: 'Eva Pacheco Martín'
    },
    {
      id: 7,
      nombre: 'Janira García Méndez',
      formacion: 'G. Valencia',
      candidatosAsignados: 61,
      ultimaModificacion: '26/01/2025 13:25:10',
      modificadoPor: 'Manuel Toscano Díaz'
    },
    {
      id: 8,
      nombre: 'Francisco Caballero Gómez',
      formacion: 'G. La Rioja',
      candidatosAsignados: 31,
      ultimaModificacion: '25/01/2025 10:01:45',
      modificadoPor: 'Francisco Cobos Ruíz'
    },
    {
      id: 9,
      nombre: 'Alvaro Caballero Gómez',
      formacion: 'G. Alicante',
      candidatosAsignados: 31,
      ultimaModificacion: '25/01/2025 10:01:45',
      modificadoPor: 'Francisco Cobos Ruíz'
    }
  ];

  filtroNombre = '';

  limpiarFiltros() {
    this.filtroNombre = '';
  }

  showSidebar = false;

  abrirModal(): void {
    this.showSidebar = true;
  }

  cerrarSidebar(): void {
    this.showSidebar = false;
  }

  candidatos: string[] = [
    'Ana Camacho (Huelva)',
    'Antonio García (Jaén)',
    'Beatriz Bernal (Sevilla)',
    'Ernesto Caballero (Almería)',
    'Francisco Herrera (Cádiz)',
    'Gabriela García (Málaga)',
    'Irene Iglesias (Granada)',
    'Javier Duque (Cádiz)',
    'José María Ruiz (Sevilla)',
    'Juan Conde (Almería)',
    'Julio Romero (Córdoba)',
    'Marcelino Clavel (Cádiz)'
  ];

  toggleSelectAll() {
    if (this.selectAll) {
      this.selectedCandidatos = [...this.candidatos];
    } else {
      this.selectedCandidatos = [];
    }
  }

  toggleCheckbox(nombre: string) {
    if (this.selectedCandidatos.includes(nombre)) {
      this.selectedCandidatos = this.selectedCandidatos.filter(c => c !== nombre);
    } else {
      this.selectedCandidatos.push(nombre);
    }
    this.selectAll = this.selectedCandidatos.length === this.candidatos.length;
  }


}
