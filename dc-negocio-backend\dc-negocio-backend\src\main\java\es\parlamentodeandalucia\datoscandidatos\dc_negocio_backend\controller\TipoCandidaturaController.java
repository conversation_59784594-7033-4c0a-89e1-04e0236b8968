package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.controller;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.TipoCandidaturaEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service.TipoCandidaturaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/tipos-candidatura")
@CrossOrigin(origins = { "http://localhost:4200", "https://localhost:4200" })
public class TipoCandidaturaController {

    @Autowired
    private TipoCandidaturaService tipoCandidaturaService;

    /**
     * Obtiene todos los tipos de candidatura
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<List<TipoCandidaturaEntity>> getAll() {
        return ResponseEntity.ok(tipoCandidaturaService.findAll());
    }

    /**
     * Obtiene un tipo de candidatura por ID
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<TipoCandidaturaEntity> getById(@PathVariable Long id) {
        return tipoCandidaturaService.findById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Obtiene un tipo de candidatura por valor
     */
    @GetMapping("/valor/{valor}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<TipoCandidaturaEntity> getByValor(@PathVariable String valor) {
        return tipoCandidaturaService.findByValor(valor)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Busca tipos de candidatura por texto
     */
    @GetMapping("/buscar")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<List<TipoCandidaturaEntity>> buscarPorTexto(@RequestParam String texto) {
        return ResponseEntity.ok(tipoCandidaturaService.findByTextoContaining(texto));
    }

    /**
     * Crea un nuevo tipo de candidatura
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<TipoCandidaturaEntity> create(@RequestBody TipoCandidaturaEntity tipoCandidatura) {
        try {
            TipoCandidaturaEntity saved = tipoCandidaturaService.save(tipoCandidatura);
            return ResponseEntity.ok(saved);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Actualiza un tipo de candidatura
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<TipoCandidaturaEntity> update(@PathVariable Long id, 
                                                       @RequestBody TipoCandidaturaEntity tipoCandidatura) {
        return tipoCandidaturaService.findById(id)
                .map(existing -> {
                    existing.setValor(tipoCandidatura.getValor());
                    return ResponseEntity.ok(tipoCandidaturaService.save(existing));
                })
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Elimina un tipo de candidatura
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        if (tipoCandidaturaService.findById(id).isPresent()) {
            tipoCandidaturaService.deleteById(id);
            return ResponseEntity.ok().build();
        }
        return ResponseEntity.notFound().build();
    }
}
