package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.controller;

import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.CircunscripcionEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service.CircunscripcionService;

@RestController
@RequestMapping("/api/circunscripciones")
@CrossOrigin
public class CircunscripcionController {

    @Autowired
    private CircunscripcionService circunscripcionService;

    @GetMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<List<CircunscripcionEntity>> getAll() {
        return ResponseEntity.ok(circunscripcionService.findAll());
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<CircunscripcionEntity> getById(@PathVariable Long id) {
        return circunscripcionService.findById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/byCandidaturaActiva")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<List<CircunscripcionEntity>> getByCandidaturaActiva(@RequestParam boolean activa) {
        List<CircunscripcionEntity> circunscripciones = circunscripcionService.findByCandidaturaActiva(activa);
        return ResponseEntity.ok(circunscripciones);
    }

    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<CircunscripcionEntity> create(@RequestBody CircunscripcionEntity circunscripcion) {
        CircunscripcionEntity saved = circunscripcionService.save(circunscripcion);
        return ResponseEntity.status(201).body(saved);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<CircunscripcionEntity> updateCircunscripcion(@PathVariable Long id, @RequestBody CircunscripcionEntity circunscripcionDetails) {
        try {
            CircunscripcionEntity updated = circunscripcionService.update(id, circunscripcionDetails);
            return new ResponseEntity<>(updated, HttpStatus.OK);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        if (!circunscripcionService.findById(id).isPresent()) {
            return ResponseEntity.notFound().build();
        }
        circunscripcionService.deleteById(id);
        return ResponseEntity.noContent().build();
    }

}
