:host {
  display: block;
  font-family: 'Inter', sans-serif;
  width: 100%;
}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: 'Inter', sans-serif;
  overflow-y: auto;
  overflow-x: hidden;
}

.layout-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

.header-fixed {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 10;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  height: 60px;
}

.footer-fixed {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 10;
  background-color: #fff;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
  height: 50px;
}

.page-wrapper {
  margin-top: 60px;
  margin-bottom: 50px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 110px);
  background-color: #f8f8f8;
  padding: 0;
  overflow: hidden;
}

.breadcrumbs {
  margin: 0 auto 20px auto;
  max-width: 1400px;
  width: calc(100% - 40px);
  font-size: 14px;
  color: #666;
  margin-top: 10px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.breadcrumbs .separator {
  margin: 0 5px;
  color: #999;
}

.breadcrumbs strong {
  color: #333;
  font-weight: 600;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0 auto 20px auto;
  max-width: 1400px;
  width: calc(100% - 40px);
  padding: 0 20px;
}

.title-section .back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  color: #333;
  text-decoration: none;
  padding: 0;
  flex-shrink: 0;
}

.title-section .back-button svg {
  width: 24px;
  height: 24px;
  color: #555;
}

.title-section .page-main-title {
  font-size: 28px;
  font-weight: 600;
  color: #333;
  margin-left: 5px;
  white-space: nowrap;
  flex-shrink: 0;
}

.main-container {
  background-color: #fff; 
  max-width: 1400px;
  width: calc(100% - 40px);
  margin: 0 auto; 
  padding: 40px; 
  border-radius: 15px; 
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  justify-content: center; 
  align-items: center; 
  min-height: 700px; 
}

.reset-page-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 40px; 
  background-color: #fff;
}

.reset-card {
  width: 420px;
  padding: 25px 30px;
  border: 1px solid #cdd4c4;
  border-radius: 15px;
  background-color: #fff;
  text-align: left;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin: 0 auto;
  max-height: 400px; 
}

.reset-icon {
  color: #d9534f;
  margin-bottom: 15px;
  font-size: 32px;
}

.reset-card h2 {
  margin-bottom: 12px;
  font-size: 20px;
  color: #333;
}

.reset-card p {
  font-size: 14px;
  margin: 6px 0;
  color: #555;
}

.reset-card .warning {
  color: #000000;
  font-weight: bold;
  margin-top: 10px;
}

.btn-danger {
  background-color: #d9534f;
  color: #fff;
  border: none;
  padding: 12px 18px;
  margin-top: 18px;
  cursor: pointer;
  border-radius: 15px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  font-size: 15px;
  transition: background-color 0.2s ease, box-shadow 0.2s ease;
}

.btn-danger:hover {
  background-color: #c9302c;
  box-shadow: 0 2px 6px rgba(217, 83, 79, 0.3);
}

.button-container {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}