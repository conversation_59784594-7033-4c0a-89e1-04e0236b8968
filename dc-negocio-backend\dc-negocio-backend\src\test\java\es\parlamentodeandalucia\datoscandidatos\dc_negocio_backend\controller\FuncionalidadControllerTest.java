package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.controller;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.util.Arrays;
import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import com.fasterxml.jackson.databind.ObjectMapper;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.FuncionalidadEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository.PermisoRepository;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service.FuncionalidadService;

@SpringBootTest
@AutoConfigureMockMvc
class FuncionalidadControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private FuncionalidadService funcionalidadService;
    
    @MockBean
    private PermisoRepository permisoRepository;

    @Autowired
    private ObjectMapper objectMapper;

    private FuncionalidadEntity funcionalidad;

    @BeforeEach
    void setUp() {
        funcionalidad = new FuncionalidadEntity();
        funcionalidad.setId(1L);
        funcionalidad.setDescripcion("Consultar datos");
        funcionalidad.setCodigo("Consultar codigo");
    }

    @Test
    @WithMockUser(roles = {"ADMIN"})
    void testGetAllFuncionalidades() throws Exception {
        Mockito.when(funcionalidadService.findAll()).thenReturn(Arrays.asList(funcionalidad));

        mockMvc.perform(get("/api/funcionalidades"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].id").value(1L))
                .andExpect(jsonPath("$[0].descripcion").value("Consultar datos"))
        		.andExpect(jsonPath("$[0].codigo").value("Consultar codigo"));
    }

    @Test
    @WithMockUser(roles = {"ADMIN"})
    void testGetFuncionalidadById_found() throws Exception {
        Mockito.when(funcionalidadService.findById(1L)).thenReturn(Optional.of(funcionalidad));

        mockMvc.perform(get("/api/funcionalidades/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(1L))
                .andExpect(jsonPath("$.descripcion").value("Consultar datos"))
        		.andExpect(jsonPath("$.codigo").value("Consultar codigo"));
    }

    @Test
    @WithMockUser(roles = {"ADMIN"})
    void testGetFuncionalidadById_notFound() throws Exception {
        Mockito.when(funcionalidadService.findById(99L)).thenReturn(Optional.empty());

        mockMvc.perform(get("/api/funcionalidades/99"))
                .andExpect(status().isNotFound());
    }

    @Test
    @WithMockUser(roles = {"ADMIN"})
    void testCreateFuncionalidad() throws Exception {
        Mockito.when(funcionalidadService.save(any())).thenReturn(funcionalidad);

        mockMvc.perform(post("/api/funcionalidades")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(funcionalidad)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.id").value(1L))
                .andExpect(jsonPath("$.descripcion").value("Consultar datos"))
        		.andExpect(jsonPath("$.codigo").value("Consultar codigo"));
    }

    @Test
    @WithMockUser(roles = {"ADMIN"})
    void testUpdateFuncionalidad() throws Exception {
        FuncionalidadEntity updated = new FuncionalidadEntity();
        updated.setId(1L);
        updated.setDescripcion("Actualizar descripcion");
        updated.setCodigo("Actualizar codigo");

        Mockito.when(funcionalidadService.updateFuncionalidad(eq(1L), any())).thenReturn(updated);

        mockMvc.perform(put("/api/funcionalidades/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updated)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.descripcion").value("Actualizar descripcion"))
                .andExpect(jsonPath("$.codigo").value("Actualizar codigo"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testDeleteFuncionalidad_noEncontrada() throws Exception {
        Long id = 99L;
        Mockito.when(funcionalidadService.findById(id)).thenReturn(Optional.empty());

        mockMvc.perform(delete("/api/funcionalidades/{id}", id))
               .andExpect(status().isNotFound());

        Mockito.verify(funcionalidadService, Mockito.never()).deleteById(Mockito.anyLong());
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testDeleteFuncionalidad_conPermisosAsociados() throws Exception {
        Long id = 1L;
        FuncionalidadEntity funcionalidad = new FuncionalidadEntity();
        funcionalidad.setId(id);
        funcionalidad.setCodigo("Actualizar codigo");
        funcionalidad.setDescripcion("Funcionalidad con permisos");

        Mockito.when(funcionalidadService.findById(id)).thenReturn(Optional.of(funcionalidad));
        Mockito.when(permisoRepository.existsByFuncionalidadId(id)).thenReturn(true);

        mockMvc.perform(delete("/api/funcionalidades/{id}", id))
               .andExpect(status().isConflict());

        Mockito.verify(funcionalidadService, Mockito.never()).deleteById(Mockito.anyLong());
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testDeleteFuncionalidad_exito() throws Exception {
        Long id = 1L;
        FuncionalidadEntity funcionalidad = new FuncionalidadEntity();
        funcionalidad.setId(id);
        funcionalidad.setCodigo("Actualizar codigo");
        funcionalidad.setDescripcion("Funcionalidad sin permisos");

        Mockito.when(funcionalidadService.findById(id)).thenReturn(Optional.of(funcionalidad));
        Mockito.when(permisoRepository.existsByFuncionalidadId(id)).thenReturn(false);

        mockMvc.perform(delete("/api/funcionalidades/{id}", id))
               .andExpect(status().isNoContent());

        Mockito.verify(funcionalidadService).deleteById(id);
    }
}
