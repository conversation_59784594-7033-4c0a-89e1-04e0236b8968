package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.PlantillaEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository.PlantillaRepository;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class PlantillaService {

    private final PlantillaRepository repository;

    public PlantillaService(PlantillaRepository repository) {
        this.repository = repository;
    }

    public List<PlantillaEntity> findAll() {
        return repository.findAll();
    }

    public Optional<PlantillaEntity> findById(Long id) {
        return repository.findById(id);
    }

    public PlantillaEntity save(PlantillaEntity entity) {
        return repository.save(entity);
    }

    public void deleteById(Long id) {
        repository.deleteById(id);
    }

    public void activar(List<Long> ids) {
        for (Long id : ids) {
            repository.findById(id).ifPresent(p -> {
                p.setActiva(true);
                repository.save(p);
            });
        }
    }

    public void desactivar(List<Long> ids) {
        for (Long id : ids) {
            repository.findById(id).ifPresent(p -> {
                p.setActiva(false);
                repository.save(p);
            });
        }
    }

    public List<PlantillaEntity> findAllByIds(List<Long> ids) {
        return repository.findAllById(ids);
    }

}

