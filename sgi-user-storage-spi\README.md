# SGI User Storage SPI

Sistema de Gestión de Identidades - User Storage Provider para Keycloak

## Descripción

Este proyecto implementa un User Storage SPI personalizado para Keycloak que permite autenticar usuarios contra la base de datos externa del Sistema de Gestión de Identidades (SGI).

## Características

- ✅ Autenticación contra base de datos PostgreSQL externa
- ✅ Mapeo completo con User Profile de Keycloak
- ✅ Soporte para campos personalizados (nombre, NIF, etc.)
- ✅ Gestión de intentos fallidos y bloqueo de cuentas
- ✅ Búsqueda avanzada de usuarios
- ✅ Configuración flexible de datasource
- ✅ Logging detallado para debugging

## Campos Soportados

El SPI mapea los siguientes campos del User Profile de Keycloak:

| Campo Keycloak | Campo SGI | Descripción |
|----------------|-----------|-------------|
| `username` | `sgi_username` | Nombre de usuario único |
| `email` | `sgi_email` | Correo electrónico |
| `firstName` | `sgi_first_name` | Nombre |
| `lastName` | `sgi_last_name` | Apellidos |
| `nombre` | `sgi_nombre` | Nombre completo |
| `nif` | `sgi_nif` | NIF del usuario |

## Estructura de Base de Datos

### Tabla Principal: `sgi_t_credencial`

```sql
-- Esquema completo del SGI según diagrama
-- Ejecutar script: sgi-negocio-backend/script-bd/1.Script-creacion-tablas-sgi.sql

-- Tabla principal que mapea con Keycloak
CREATE TABLE sgi_t_credencial (
    sgi_id_credencial INT8 NOT NULL,
    sgi_tx_username VARCHAR(255) NOT NULL UNIQUE,
    sgi_tx_password VARCHAR(255),
    sgi_fk_estado INT8,
    sgi_fh_alta DATE,
    sgi_fh_baja DATE,
    sgi_fk_identidad INT8,  -- Referencia a sgi_t_identidad
    CONSTRAINT sgi_t_credencial_pkey PRIMARY KEY (sgi_id_credencial),
    CONSTRAINT sgi_t_credencial_estado_fkey
        FOREIGN KEY (sgi_fk_estado) REFERENCES sgi_t_estado(sgi_id_estado),
    CONSTRAINT sgi_t_credencial_identidad_fkey
        FOREIGN KEY (sgi_fk_identidad) REFERENCES sgi_t_identidad(sgi_id_identidad)
);

-- Los datos del usuario (nombre, email, NIF, teléfono) se almacenan en sgi_t_identidad
-- Los roles se gestionan en el SGI (no en DAC como se pensaba inicialmente)
```

## Instalación y Configuración

### Prerrequisitos

- Java 21+
- Maven 3.8+
- Keycloak 25.0.2+
- PostgreSQL 12+
- Base de datos SGI configurada

### Paso 1: Compilar el SPI

```bash
# Clonar y compilar
cd sgi-user-storage-spi
mvn clean package
```

### Paso 2: Configurar DataSource en Keycloak

Crear archivo `conf/quarkus-jdbc-sgi.properties` en Keycloak:

```properties
quarkus.datasource.sgi-junta-electoral.db-kind=postgresql
quarkus.datasource.sgi-junta-electoral.username=postgres
quarkus.datasource.sgi-junta-electoral.password=postgres
quarkus.datasource.sgi-junta-electoral.jdbc.url=********************************************
```

### Paso 3: Desplegar JAR

```bash
# Copiar JAR a Keycloak
cp target/sgi-user-storage-spi.jar $KEYCLOAK_HOME/providers/

# O usar el script automatizado
./build-and-deploy.sh
```

### Paso 4: Reiniciar Keycloak

```bash
cd $KEYCLOAK_HOME
./bin/kc.sh start-dev
```

### Paso 5: Configurar en Keycloak Admin Console

1. **Acceder a Admin Console**: http://localhost:8080/admin
2. **Ir a Realm**: `juntaelectoral`
3. **Navegar a**: `User Federation`
4. **Agregar Provider**: Seleccionar `sgi-user-storage`
5. **Configurar**:
   - **Display Name**: `SGI Junta Electoral`
   - **Nombre del DataSource**: `sgi-junta-electoral`
   - **Política de Cache**: `DEFAULT`
   - **Enabled**: `ON`
6. **Guardar**

## Usuarios de Prueba

El sistema incluye usuarios de prueba (contraseña: `password123`):

| Username | Email | Nombre | NIF |
|----------|-------|--------|-----|
| `admin.sgi` | <EMAIL> | Administrador SGI Sistema | 12345678A |
| `usuario.test` | <EMAIL> | Usuario Prueba Test | 87654321B |
| `validador.sgi` | <EMAIL> | Validador SGI Datos | 11223344C |

**NOTA**: Los roles se gestionan en el sistema DAC, no en el SGI.

## Configuración Avanzada

### Parámetros del Provider

| Parámetro | Descripción | Valor por Defecto |
|-----------|-------------|-------------------|
| `datasourceName` | Nombre del datasource configurado | `sgi-junta-electoral` |
| `cachePolicy` | Política de cache | `DEFAULT` |

**NOTA**: La configuración de roles se eliminó - los roles se gestionan en el sistema DAC.

### Políticas de Cache

- `DEFAULT`: Cache estándar de Keycloak
- `EVICT_DAILY`: Limpiar cache diariamente
- `EVICT_WEEKLY`: Limpiar cache semanalmente
- `NO_CACHE`: Sin cache (siempre consultar BD)

## Troubleshooting

### Problemas Comunes

#### 1. Error de Conexión a Base de Datos

```
Error: No se puede conectar al datasource 'sgi-junta-electoral'
```

**Solución**:
- Verificar que PostgreSQL esté corriendo
- Verificar credenciales en `quarkus-jdbc-sgi.properties`
- Verificar que la base de datos `sgiDatabase` existe

#### 2. Usuario No Encontrado

```
Usuario no encontrado por username: test.user
```

**Solución**:
- Verificar que el usuario existe en `sgi_t_credencial`
- Verificar que `sgi_enabled = true`
- Verificar que `sgi_account_locked = false`

#### 3. Error de Autenticación

```
Contraseña inválida para usuario: test.user
```

**Solución**:
- Verificar que la contraseña esté hasheada con BCrypt
- Usar herramienta online para generar hash BCrypt
- Verificar que no hay espacios extra en la contraseña

### Logs Útiles

```bash
# Ver logs de Keycloak
tail -f $KEYCLOAK_HOME/data/log/keycloak.log

# Filtrar logs del SGI SPI
tail -f $KEYCLOAK_HOME/data/log/keycloak.log | grep SGI

# Ver logs de conexión a BD
tail -f $KEYCLOAK_HOME/data/log/keycloak.log | grep "sgi-junta-electoral"
```

## Desarrollo

### Estructura del Proyecto

```
sgi-user-storage-spi/
├── src/main/java/
│   └── es/parlamentodeandalucia/sgi/keycloak/spi/
│       ├── adapter/          # UserAdapter
│       ├── entity/           # Entidades JPA
│       ├── factory/          # ProviderFactory
│       └── provider/         # UserStorageProvider
├── src/main/resources/
│   └── META-INF/
│       ├── persistence.xml   # Configuración JPA
│       └── services/         # Registro SPI
├── keycloak-config/          # Configuración Keycloak
└── build-and-deploy.sh       # Script de despliegue
```

### Compilar para Desarrollo

```bash
# Compilar sin tests
mvn clean package -DskipTests

# Compilar con tests
mvn clean package

# Solo compilar (sin empaquetar)
mvn compile
```

## Licencia

Proyecto interno - Parlamento de Andalucía

## Soporte

Para soporte técnico, contactar al equipo de desarrollo del SGI.
