package es.parlamentodeandalucia.sgi.keycloak.spi.entity;

import jakarta.persistence.*;

/**
 * Entidad para identidades del SGI
 * Mapea con la tabla sgi_t_identidad
 */
@Entity
@Table(name = "sgi_t_identidad")
@NamedQueries({
    @NamedQuery(name = "SgiIdentidadEntity.findByIdentificador", 
                query = "SELECT i FROM SgiIdentidadEntity i WHERE i.identificador = :identificador"),
    @NamedQuery(name = "SgiIdentidadEntity.findByEmail", 
                query = "SELECT i FROM SgiIdentidadEntity i WHERE i.email = :email")
})
public class SgiIdentidadEntity {

    @Id
    @Column(name = "sgi_id_identidad")
    private Long id;

    @Column(name = "sgi_tx_nombre")
    private String nombre;

    @Column(name = "sgi_tx_apellido1")
    private String apellido1;

    @Column(name = "sgi_tx_apellido2")
    private String apellido2;

    @Column(name = "sgi_tx_identificador", unique = true)
    private String identificador;

    @Column(name = "sgi_tx_email")
    private String email;

    @Column(name = "sgi_tx_movil")
    private String movil;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "sgi_fk_tipo_identificador")
    private SgiTipoIdentificadorEntity tipoIdentificador;

    // Constructores
    public SgiIdentidadEntity() {}

    public SgiIdentidadEntity(String nombre, String apellido1, String apellido2, 
                             String identificador, String email, String movil) {
        this.nombre = nombre;
        this.apellido1 = apellido1;
        this.apellido2 = apellido2;
        this.identificador = identificador;
        this.email = email;
        this.movil = movil;
    }

    // Métodos de utilidad
    public String getNombreCompleto() {
        StringBuilder nombreCompleto = new StringBuilder();
        if (nombre != null && !nombre.trim().isEmpty()) {
            nombreCompleto.append(nombre.trim());
        }
        if (apellido1 != null && !apellido1.trim().isEmpty()) {
            if (nombreCompleto.length() > 0) nombreCompleto.append(" ");
            nombreCompleto.append(apellido1.trim());
        }
        if (apellido2 != null && !apellido2.trim().isEmpty()) {
            if (nombreCompleto.length() > 0) nombreCompleto.append(" ");
            nombreCompleto.append(apellido2.trim());
        }
        return nombreCompleto.toString();
    }

    public String getApellidosCompletos() {
        StringBuilder apellidos = new StringBuilder();
        if (apellido1 != null && !apellido1.trim().isEmpty()) {
            apellidos.append(apellido1.trim());
        }
        if (apellido2 != null && !apellido2.trim().isEmpty()) {
            if (apellidos.length() > 0) apellidos.append(" ");
            apellidos.append(apellido2.trim());
        }
        return apellidos.toString();
    }

    // Getters y Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getNombre() { return nombre; }
    public void setNombre(String nombre) { this.nombre = nombre; }

    public String getApellido1() { return apellido1; }
    public void setApellido1(String apellido1) { this.apellido1 = apellido1; }

    public String getApellido2() { return apellido2; }
    public void setApellido2(String apellido2) { this.apellido2 = apellido2; }

    public String getIdentificador() { return identificador; }
    public void setIdentificador(String identificador) { this.identificador = identificador; }

    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }

    public String getMovil() { return movil; }
    public void setMovil(String movil) { this.movil = movil; }

    public SgiTipoIdentificadorEntity getTipoIdentificador() { return tipoIdentificador; }
    public void setTipoIdentificador(SgiTipoIdentificadorEntity tipoIdentificador) { this.tipoIdentificador = tipoIdentificador; }
}
