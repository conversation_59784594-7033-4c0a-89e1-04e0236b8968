package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.controller;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.TipoCandidatoEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service.TipoCandidatoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/tipos-candidato")
@CrossOrigin(origins = { "http://localhost:4200", "https://localhost:4200" })
public class TipoCandidatoController {

    @Autowired
    private TipoCandidatoService tipoCandidatoService;

    /**
     * Obtiene todos los tipos de candidato
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<List<TipoCandidatoEntity>> getAll() {
        return ResponseEntity.ok(tipoCandidatoService.findAll());
    }

    /**
     * Obtiene un tipo de candidato por ID
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<TipoCandidatoEntity> getById(@PathVariable Long id) {
        return tipoCandidatoService.findById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Obtiene un tipo de candidato por valor
     */
    @GetMapping("/valor/{valor}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<TipoCandidatoEntity> getByValor(@PathVariable String valor) {
        return tipoCandidatoService.findByValor(valor)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Busca tipos de candidato por texto
     */
    @GetMapping("/buscar")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<List<TipoCandidatoEntity>> buscarPorTexto(@RequestParam String texto) {
        return ResponseEntity.ok(tipoCandidatoService.findByTextoContaining(texto));
    }

    /**
     * Crea un nuevo tipo de candidato
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<TipoCandidatoEntity> create(@RequestBody TipoCandidatoEntity tipoCandidato) {
        try {
            TipoCandidatoEntity saved = tipoCandidatoService.save(tipoCandidato);
            return ResponseEntity.ok(saved);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Actualiza un tipo de candidato
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<TipoCandidatoEntity> update(@PathVariable Long id, 
                                                     @RequestBody TipoCandidatoEntity tipoCandidato) {
        return tipoCandidatoService.findById(id)
                .map(existing -> {
                    existing.setValor(tipoCandidato.getValor());
                    existing.setDescripcion(tipoCandidato.getDescripcion());
                    return ResponseEntity.ok(tipoCandidatoService.save(existing));
                })
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Elimina un tipo de candidato
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        if (tipoCandidatoService.findById(id).isPresent()) {
            tipoCandidatoService.deleteById(id);
            return ResponseEntity.ok().build();
        }
        return ResponseEntity.notFound().build();
    }
}
