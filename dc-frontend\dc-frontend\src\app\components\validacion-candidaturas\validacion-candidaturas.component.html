<div class="layout-container">
    <app-header class="header-fixed"></app-header>

    <div class="page-wrapper scrollable-content">
        <div class="breadcrumbs">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="16px" height="16px">
                <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z" />
            </svg>
            <span class="separator">&gt;</span>
            <span>Administración</span>
            <span class="separator">&gt;</span>
            <strong>Validación de candidaturas</strong>
        </div>

        <div class="title-section">
            <a routerLink="/home" class="back-button">
                <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24" width="24px"
                    height="24px">
                    <path d="M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z" transform="rotate(180 12 12)" />
                </svg>
            </a>
            <h1>Validación de candidaturas</h1>
        </div>

        <div class="usuarios-page-container">
            <p-tabView (onChange)="onTabChange($event)" [(activeIndex)]="activeTabIndex">
                <p-tabPanel header="Pendientes">
                    <ng-template pTemplate="content">
                        <div class="filter-actions-bar">
                            <button class="btn-text" (click)="clearFilters()">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24" width="16px" height="16px">
                                    <path
                                        d="M6 13c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm14-10v2h-3.21l-3.42 8H18v2h-7.15c-.78 1.76-2.58 3-4.85 3-2.21 0-4-1.79-4-4s1.79-4 4-4c.78 0 1.5.22 2.15.62L12.58 4H20V2h2v2h-2z" />
                                </svg>
                                Limpiar filtros
                            </button>
                            <div class="dropdown-masivo" (clickOutside)="showMassiveActions = false">
                                <button class="btn-massive" [disabled]="selectedCandidaturas.length === 0"
                                    (click)="showMassiveActions = !showMassiveActions">
                                    Acciones masivas
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M7 10l5 5 5-5H7z" />
                                    </svg>
                                </button>
                                <ul class="dropdown-options" *ngIf="showMassiveActions">
                                    <li (click)="validarSeleccionadas()">Validar</li>
                                    <li (click)="rechazarSeleccionadas()">Rechazar</li>
                                </ul>
                            </div>
                        </div>
                        <p-table #dtPendientes [value]="candidaturasPendientes" [paginator]="true" [rows]="10"
                            [globalFilterFields]="['nombre', 'formacionPolitica', 'circunscripcion', 'orden', 'tipo']"
                            [(selection)]="selectedCandidaturas" dataKey="id" [rowSelectable]="isRowSelectable">
                            <ng-template pTemplate="header">
                                <tr>
                                    <th style="width: 3rem"><p-tableHeaderCheckbox></p-tableHeaderCheckbox></th>
                                    <th pSortableColumn="nombre">Nombre <p-sortIcon field="nombre"></p-sortIcon></th>
                                    <th pSortableColumn="formacionPolitica">Formación política <p-sortIcon field="formacionPolitica"></p-sortIcon></th>
                                    <th pSortableColumn="circunscripcion">Circunscripción <p-sortIcon field="circunscripcion"></p-sortIcon></th>
                                    <th pSortableColumn="orden">Orden <p-sortIcon field="orden"></p-sortIcon></th>
                                    <th pSortableColumn="tipo">Tipo <p-sortIcon field="tipo"></p-sortIcon></th>
                                    <th pSortableColumn="estado">Estado <p-sortIcon field="estado"></p-sortIcon></th>
                                    <th style="width: 5rem; text-align: center;">Acciones</th>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="body" let-candidatura let-rowIndex="rowIndex">
                                <tr [ngClass]="getRowStyle(candidatura, rowIndex)">
                                    <td><p-tableCheckbox [value]="candidatura"></p-tableCheckbox></td>
                                    <td>{{ candidatura.nombre }}</td>
                                    <td>{{ candidatura.formacionPolitica }}</td>
                                    <td>{{ candidatura.circunscripcion }}</td>
                                    <td>{{ candidatura.orden }}</td>
                                    <td><span class="status-badge" [ngClass]="getTipoClass(candidatura.tipo)">{{ candidatura.tipo }}</span></td>
                                    <td><span class="status-badge" [ngClass]="getStatusClass(candidatura.estado)">{{ candidatura.estado }}</span></td>
                                    <td class="acciones-cell" (clickOutside)="openedDropdownId = null">
                                        <div class="dropdown">
                                            <button class="btn-icon dropdown-toggle" (click)="toggleDropdown(candidatura.id)">
                                                Acciones
                                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor"
                                                    viewBox="0 0 24 24">
                                                    <path d="M12 7a2 2 0 1 0 0-4 2 2 0 0 0 0 4zm0 2a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm0 6a2 2 0 1 0 0 4 2 2 0 0 0 0-4z" />
                                                </svg>
                                            </button>
                                            <div class="dropdown-menu" *ngIf="openedDropdownId === candidatura.id">
                                                <a class="dropdown-item" (click)="validarCandidatura(candidatura.id)">Validar</a>
                                                <a class="dropdown-item" (click)="rechazarCandidatura(candidatura.id)">Rechazar</a>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="emptymessage">
                                <tr>
                                    <td colspan="8" class="p-text-center">No hay candidaturas pendientes.</td>
                                </tr>
                            </ng-template>
                        </p-table>
                    </ng-template>
                </p-tabPanel>
                <p-tabPanel header="Validados">
                    <ng-template pTemplate="content">
                        <div class="filter-actions-bar">
                            <button class="btn-text" (click)="clearFilters()">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24" width="16px" height="16px">
                                    <path
                                        d="M6 13c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm14-10v2h-3.21l-3.42 8H18v2h-7.15c-.78 1.76-2.58 3-4.85 3-2.21 0-4-1.79-4-4s1.79-4 4-4c.78 0 1.5.22 2.15.62L12.58 4H20V2h2v2h-2z" />
                                </svg>
                                Limpiar filtros
                            </button>
                            <div class="dropdown-masivo" (clickOutside)="showMassiveActions = false">
                                <button class="btn-massive" [disabled]="selectedCandidaturas.length === 0"
                                    (click)="showMassiveActions = !showMassiveActions">
                                    Acciones masivas
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M7 10l5 5 5-5H7z" />
                                    </svg>
                                </button>
                                <ul class="dropdown-options" *ngIf="showMassiveActions">
                                    <li (click)="revertirSeleccionadas()">Revertir a pendientes</li>
                                </ul>
                            </div>
                        </div>
                        <p-table #dtValidados [value]="candidaturasValidadas" [paginator]="true" [rows]="10"
                            [globalFilterFields]="['nombre', 'formacionPolitica', 'circunscripcion', 'orden', 'tipo']"
                            [(selection)]="selectedCandidaturas" dataKey="id" [rowSelectable]="isRowSelectable">
                            <ng-template pTemplate="header">
                                <tr>
                                    <th style="width: 3rem"><p-tableHeaderCheckbox></p-tableHeaderCheckbox></th>
                                    <th pSortableColumn="nombre">Nombre <p-sortIcon field="nombre"></p-sortIcon></th>
                                    <th pSortableColumn="formacionPolitica">Formación política <p-sortIcon field="formacionPolitica"></p-sortIcon></th>
                                    <th pSortableColumn="circunscripcion">Circunscripción <p-sortIcon field="circunscripcion"></p-sortIcon></th>
                                    <th pSortableColumn="orden">Orden <p-sortIcon field="orden"></p-sortIcon></th>
                                    <th pSortableColumn="tipo">Tipo <p-sortIcon field="tipo"></p-sortIcon></th>
                                    <th pSortableColumn="estado">Estado <p-sortIcon field="estado"></p-sortIcon></th>
                                    <th style="width: 5rem; text-align: center;">Acciones</th>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="body" let-candidatura let-rowIndex="rowIndex">
                                <tr [ngClass]="getRowStyle(candidatura, rowIndex)">
                                    <td><p-tableCheckbox [value]="candidatura"></p-tableCheckbox></td>
                                    <td>{{ candidatura.nombre }}</td>
                                    <td>{{ candidatura.formacionPolitica }}</td>
                                    <td>{{ candidatura.circunscripcion }}</td>
                                    <td>{{ candidatura.orden }}</td>
                                    <td><span class="status-badge" [ngClass]="getTipoClass(candidatura.tipo)">{{ candidatura.tipo }}</span></td>
                                    <td><span class="status-badge" [ngClass]="getStatusClass(candidatura.estado)">{{ candidatura.estado }}</span></td>
                                    <td class="acciones-cell" (clickOutside)="openedDropdownId = null">
                                        <div class="dropdown">
                                            <button class="btn-icon dropdown-toggle" (click)="toggleDropdown(candidatura.id)">
                                                Acciones
                                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor"
                                                    viewBox="0 0 24 24">
                                                    <path d="M12 7a2 2 0 1 0 0-4 2 2 0 0 0 0 4zm0 2a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm0 6a2 2 0 1 0 0 4 2 2 0 0 0 0-4z" />
                                                </svg>
                                            </button>
                                            <div class="dropdown-menu" *ngIf="openedDropdownId === candidatura.id">
                                                <a class="dropdown-item" (click)="revertirValidacion(candidatura.id)">Revertir a pendiente</a>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="emptymessage">
                                <tr>
                                    <td colspan="8" class="p-text-center">No hay candidaturas validadas.</td>
                                </tr>
                            </ng-template>
                        </p-table>
                    </ng-template>
                </p-tabPanel>
            </p-tabView>
        </div>
    </div>

    <app-footer></app-footer>
</div>

<p-confirmDialog header="Confirmación" icon="pi pi-exclamation-triangle"></p-confirmDialog>
<p-toast></p-toast>