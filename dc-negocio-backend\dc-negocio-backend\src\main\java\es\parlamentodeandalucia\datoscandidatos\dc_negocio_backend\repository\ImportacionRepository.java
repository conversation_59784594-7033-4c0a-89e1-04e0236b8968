package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.ImportacionEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface ImportacionRepository extends JpaRepository<ImportacionEntity, Long> {

    /**
     * Busca importaciones por usuario
     * @param usuarioImportacion Usuario que realizó la importación
     * @return Lista de importaciones del usuario
     */
    List<ImportacionEntity> findByUsuarioImportacion(String usuarioImportacion);

    /**
     * Busca importaciones por número de filas correctas
     * @param filasCorrectas Número de filas procesadas correctamente
     * @return Lista de importaciones con el número especificado de filas correctas
     */
    List<ImportacionEntity> findByFilasCorrectas(Long filasCorrectas);

    /**
     * Busca importaciones por formato de fichero
     * @param formatoFichero Formato del archivo (CSV, JSON, XML)
     * @return Lista de importaciones con el formato especificado
     */
    List<ImportacionEntity> findByFormatoFichero(String formatoFichero);

    /**
     * Busca importaciones en un rango de fechas
     * @param fechaInicio Fecha de inicio del rango
     * @param fechaFin Fecha de fin del rango
     * @return Lista de importaciones en el rango
     */
    @Query("SELECT i FROM ImportacionEntity i WHERE i.fechaImportacion BETWEEN :fechaInicio AND :fechaFin")
    List<ImportacionEntity> findByFechaImportacionBetween(@Param("fechaInicio") LocalDate fechaInicio, 
                                                          @Param("fechaFin") LocalDate fechaFin);

    /**
     * Busca importaciones con errores
     * @return Lista de importaciones que tienen errores asociados
     */
    @Query("SELECT DISTINCT i FROM ImportacionEntity i JOIN i.errores e")
    List<ImportacionEntity> findImportacionesConErrores();

    /**
     * Busca importaciones exitosas (sin errores)
     * @return Lista de importaciones sin errores
     */
    @Query("SELECT i FROM ImportacionEntity i WHERE i.filasIncorrectas = 0 AND " +
           "NOT EXISTS (SELECT e FROM ErrorImportEntity e WHERE e.importacion = i)")
    List<ImportacionEntity> findImportacionesExitosas();

    /**
     * Busca importaciones con paginación y filtros
     * @param usuarioImportacion Usuario (opcional)
     * @param formatoFichero Formato (opcional)
     * @param tieneErrores Si tiene errores (opcional)
     * @param pageable Configuración de paginación
     * @return Página de importaciones
     */
    @Query("SELECT i FROM ImportacionEntity i WHERE " +
           "(:usuarioImportacion IS NULL OR i.usuarioImportacion = :usuarioImportacion) AND " +
           "(:formatoFichero IS NULL OR i.formatoFichero = :formatoFichero) AND " +
           "(:tieneErrores IS NULL OR (:tieneErrores = true AND i.filasIncorrectas > 0) OR (:tieneErrores = false AND i.filasIncorrectas = 0))")
    Page<ImportacionEntity> findWithFilters(@Param("usuarioImportacion") String usuarioImportacion,
                                           @Param("formatoFichero") String formatoFichero,
                                           @Param("tieneErrores") Boolean tieneErrores,
                                           Pageable pageable);

    /**
     * Obtiene estadísticas de importaciones por usuario
     * @param usuarioImportacion Usuario
     * @return Array con [total, exitosas, con_errores]
     */
    @Query("SELECT COUNT(i), " +
           "SUM(CASE WHEN i.filasIncorrectas = 0 THEN 1 ELSE 0 END), " +
           "SUM(CASE WHEN i.filasIncorrectas > 0 THEN 1 ELSE 0 END) " +
           "FROM ImportacionEntity i WHERE i.usuarioImportacion = :usuarioImportacion")
    Object[] getEstadisticasPorUsuario(@Param("usuarioImportacion") String usuarioImportacion);
}
