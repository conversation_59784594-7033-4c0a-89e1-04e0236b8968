.page-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
}

.main-content {
  flex: 1;
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

.title-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;

  .title-with-back {
    display: flex;
    align-items: center;
    gap: 15px;

    .btn-back {
      background: none;
      border: none;
      cursor: pointer;
      padding: 8px;
      border-radius: 4px;
      color: #666;
      transition: all 0.2s ease;

      &:hover {
        background-color: #f0f0f0;
        color: #333;
      }

      svg {
        display: block;
      }
    }

    h1 {
      margin: 0;
      color: #333;
      font-size: 28px;
      font-weight: 600;
    }
  }

  .actions {
    display: flex;
    gap: 10px;
  }
}

.importaciones-page-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  :deep(.p-datatable) {
    .p-datatable-header {
      background: #f8f9fa;
      border-bottom: 1px solid #dee2e6;
      padding: 15px 20px;

      .table-header {
        font-weight: 600;
        color: #495057;
        font-size: 16px;
      }
    }

    .p-datatable-thead > tr > th {
      background: #f8f9fa;
      border-bottom: 2px solid #dee2e6;
      padding: 12px 15px;
      font-weight: 600;
      color: #495057;
      font-size: 14px;
      text-align: left;

      &:first-child {
        border-left: none;
      }

      &:last-child {
        border-right: none;
      }
    }

    .p-datatable-tbody > tr {
      &:nth-child(even) {
        background-color: #f8f9fa;
      }

      &:hover {
        background-color: #e9ecef;
      }

      > td {
        padding: 12px 15px;
        border-bottom: 1px solid #dee2e6;
        color: #495057;
        font-size: 14px;

        &:first-child {
          border-left: none;
        }

        &:last-child {
          border-right: none;
          text-align: center;
        }
      }
    }

    .p-paginator {
      background: #f8f9fa;
      border-top: 1px solid #dee2e6;
      padding: 10px 15px;
    }
  }
}

// Estilos para el modal de importación
:deep(.import-modal) {
  .p-dialog-header {
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 15px 20px;

    .p-dialog-title {
      font-weight: 600;
      color: #495057;
    }
  }

  .p-dialog-content {
    padding: 20px;
  }

  .p-dialog-footer {
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
    padding: 15px 20px;
  }
}

.modal-content {
  min-height: 200px;
}

.upload-section {
  text-align: center;

  .file-info {
    padding: 40px 20px;

    .upload-icon {
      margin-bottom: 15px;
      color: #6c757d;
    }

    .upload-text {
      font-size: 16px;
      color: #495057;
      margin-bottom: 5px;
    }

    .upload-subtext {
      font-size: 14px;
      color: #6c757d;
      margin-bottom: 0;
    }
  }

  .file-selected {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 20px;

    .file-icon {
      color: #28a745;
    }

    .file-details {
      text-align: left;

      .file-name {
        font-weight: 600;
        color: #495057;
        margin-bottom: 2px;
      }

      .file-size {
        font-size: 14px;
        color: #6c757d;
        margin-bottom: 0;
      }
    }
  }

  .upload-actions {
    display: flex;
    justify-content: center;
    gap: 10px;
    flex-wrap: wrap;
    margin-bottom: 20px;
  }
}

.progress-section {
  padding: 30px 20px;
  text-align: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
  margin: 20px 0;
  border: 1px solid #dee2e6;

  .progress-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin-bottom: 20px;

    .progress-icon {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .progress-text {
      text-align: left;

      h4 {
        margin: 0 0 5px 0;
        color: #28a745;
        font-size: 18px;
        font-weight: 600;
      }

      p {
        margin: 0;
        color: #6c757d;
        font-size: 14px;
      }
    }
  }

  :deep(.custom-progress) {
    .p-progressbar {
      height: 12px;
      border-radius: 6px;
      background-color: #e9ecef;
      box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
      overflow: hidden;

      .p-progressbar-value {
        background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
        border-radius: 6px;
        transition: width 0.3s ease;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(
            90deg,
            transparent 0%,
            rgba(255, 255, 255, 0.3) 50%,
            transparent 100%
          );
          animation: shimmer 2s infinite;
        }
      }

      .p-progressbar-label {
        color: #495057;
        font-weight: 600;
        font-size: 12px;
      }
    }
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.log-section {
  .log-content {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    max-height: 300px;
    overflow-y: auto;

    pre {
      margin: 0;
      font-family: 'Courier New', monospace;
      font-size: 13px;
      line-height: 1.4;
      color: #495057;
      white-space: pre-wrap;
      word-wrap: break-word;
    }
  }
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

// Responsive design
@media (max-width: 768px) {
  .main-content {
    padding: 15px;
  }

  .title-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 20px;

    .title-with-back h1 {
      font-size: 24px;
    }

    .actions {
      width: 100%;
      justify-content: flex-end;
    }
  }

  .importaciones-page-container {
    :deep(.p-datatable) {
      .p-datatable-thead > tr > th,
      .p-datatable-tbody > tr > td {
        padding: 8px 10px;
        font-size: 13px;
      }
    }
  }

  .upload-section .file-info {
    padding: 30px 15px;
  }

  .upload-actions,
  .modal-footer {
    flex-direction: column;
    align-items: center;

    button {
      width: 100%;
      max-width: 200px;
    }
  }

  .progress-section {
    padding: 20px 15px;

    .progress-content {
      flex-direction: column;
      gap: 10px;

      .progress-text {
        text-align: center;

        h4 {
          font-size: 16px;
        }

        p {
          font-size: 13px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: 10px;
  }

  .title-section .title-with-back {
    gap: 10px;

    h1 {
      font-size: 20px;
    }
  }

  .upload-section .file-selected {
    flex-direction: column;
    text-align: center;
    gap: 10px;

    .file-details {
      text-align: center;
    }
  }

  .importaciones-page-container {
    :deep(.p-datatable) {
      .p-datatable-thead > tr > th,
      .p-datatable-tbody > tr > td {
        padding: 6px 8px;
        font-size: 12px;
      }
    }
  }

  .progress-section {
    padding: 15px 10px;
    margin: 15px 0;

    .progress-content {
      .progress-text h4 {
        font-size: 14px;
      }

      .progress-text p {
        font-size: 12px;
      }
    }

    :deep(.custom-progress .p-progressbar) {
      height: 10px;
    }
  }
}
