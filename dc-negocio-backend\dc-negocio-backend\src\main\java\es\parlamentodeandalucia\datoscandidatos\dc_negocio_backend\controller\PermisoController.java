package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.controller;

import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.PermisoEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service.PermisoService;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * Controlador REST para gestionar los permisos del sistema.
 * Incluye operaciones CRUD y activación/desactivación de permisos.
 */
@RestController
@RequestMapping("/api/permisos")
@Tag(name = "Permiso", description = "Operaciones sobre permisos")
@CrossOrigin(origins = { "http://localhost:4200", "https://localhost:4200" })
public class PermisoController {

    private static final Logger logger = LoggerFactory.getLogger(PermisoController.class);

    @Autowired
    private PermisoService permisoService;

    public PermisoController(PermisoService permisoService) {
        this.permisoService = permisoService;
    }

    /**
     * Obtener todos los permisos.
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    public List<PermisoEntity> getAllPermisos() {
        logger.info("Listando todos los permisos");
        return permisoService.findAll();
    }

    /**
     * Obtener un permiso por ID.
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<PermisoEntity> getPermisoById(@PathVariable Long id) {
        logger.info("Buscando permiso con ID {}", id);
        Optional<PermisoEntity> permiso = permisoService.findById(id);
        return permiso.map(ResponseEntity::ok).orElse(ResponseEntity.notFound().build());
    }

    /**
     * Crear un nuevo permiso.
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<PermisoEntity> createPermiso(@RequestBody PermisoEntity permiso) {
        logger.info("Creando nuevo permiso: {}", permiso.getCodigo());
        PermisoEntity saved = permisoService.save(permiso);
        return new ResponseEntity<>(saved, HttpStatus.CREATED);
    }

    /**
     * Actualizar un permiso existente.
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<PermisoEntity> updatePermiso(@PathVariable Long id, @RequestBody PermisoEntity permisoDetails) {
        logger.info("Actualizando permiso con ID {}", id);
        try {
            PermisoEntity updated = permisoService.updatePermiso(id, permisoDetails);
            return ResponseEntity.ok(updated);
        } catch (RuntimeException e) {
            logger.warn("No se encontró el permiso con ID {} para actualizar", id);
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Eliminar un permiso por ID.
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deletePermiso(@PathVariable Long id) {
        logger.info("Eliminando permiso con ID {}", id);

        Optional<PermisoEntity> permisoOpt = permisoService.findById(id);
        if (!permisoOpt.isPresent()) {
            logger.warn("No se encontró el permiso con ID {} para eliminar", id);
            return ResponseEntity.notFound().build();
        }

        permisoService.deleteById(id);
        return ResponseEntity.noContent().build();
    }

    /**
     * Activar o desactivar un permiso.
     */
    @PutMapping("/{id}/activo")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<PermisoEntity> toggleActivo(@PathVariable Long id, @RequestParam boolean activo) {
        logger.info("{} permiso con ID {}", activo ? "Activando" : "Desactivando", id);
        try {
            PermisoEntity updated = permisoService.toggleActivo(id, activo);
            return ResponseEntity.ok(updated);
        } catch (RuntimeException e) {
            logger.warn("No se encontró el permiso con ID {} para cambio de estado", id);
            return ResponseEntity.notFound().build();
        }
    }
}
