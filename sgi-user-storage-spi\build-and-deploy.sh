#!/bin/bash

# Script para construir y desplegar el SGI User Storage SPI
# Autor: Sistema automatizado
# Fecha: $(date)

set -e

echo "========================================="
echo "SGI User Storage SPI - Build & Deploy"
echo "========================================="

# Variables de configuración
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
JAR_NAME="sgi-user-storage-spi.jar"
KEYCLOAK_HOME="${KEYCLOAK_HOME:-/opt/keycloak}"
PROVIDERS_DIR="$KEYCLOAK_HOME/providers"
CONF_DIR="$KEYCLOAK_HOME/conf"

echo "Directorio del proyecto: $PROJECT_DIR"
echo "Keycloak Home: $KEYCLOAK_HOME"
echo "Directorio de providers: $PROVIDERS_DIR"
echo "Directorio de configuración: $CONF_DIR"

# Función para verificar si Keycloak está corriendo
check_keycloak_running() {
    if pgrep -f "keycloak" > /dev/null; then
        echo "⚠️  ADVERTENCIA: Keycloak está corriendo. Se recomienda detenerlo antes del despliegue."
        read -p "¿Desea continuar de todos modos? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo "Despliegue cancelado."
            exit 1
        fi
    fi
}

# Función para limpiar y compilar
build_project() {
    echo ""
    echo "📦 Compilando proyecto..."
    cd "$PROJECT_DIR"
    
    # Limpiar compilaciones anteriores
    mvn clean
    
    # Compilar y empaquetar
    mvn package -DskipTests
    
    if [ ! -f "target/$JAR_NAME" ]; then
        echo "❌ Error: No se pudo generar el JAR $JAR_NAME"
        exit 1
    fi
    
    echo "✅ Compilación exitosa: target/$JAR_NAME"
}

# Función para crear directorios necesarios
create_directories() {
    echo ""
    echo "📁 Creando directorios necesarios..."
    
    if [ ! -d "$PROVIDERS_DIR" ]; then
        echo "Creando directorio de providers: $PROVIDERS_DIR"
        mkdir -p "$PROVIDERS_DIR"
    fi
    
    if [ ! -d "$CONF_DIR" ]; then
        echo "Creando directorio de configuración: $CONF_DIR"
        mkdir -p "$CONF_DIR"
    fi
}

# Función para desplegar JAR
deploy_jar() {
    echo ""
    echo "🚀 Desplegando JAR..."
    
    # Hacer backup del JAR anterior si existe
    if [ -f "$PROVIDERS_DIR/$JAR_NAME" ]; then
        echo "Haciendo backup del JAR anterior..."
        mv "$PROVIDERS_DIR/$JAR_NAME" "$PROVIDERS_DIR/${JAR_NAME}.backup.$(date +%Y%m%d_%H%M%S)"
    fi
    
    # Copiar nuevo JAR
    cp "target/$JAR_NAME" "$PROVIDERS_DIR/"
    echo "✅ JAR desplegado en: $PROVIDERS_DIR/$JAR_NAME"
}

# Función para desplegar configuración
deploy_config() {
    echo ""
    echo "⚙️  Desplegando configuración..."
    
    CONFIG_FILE="keycloak-config/quarkus-jdbc-sgi.properties"
    if [ -f "$CONFIG_FILE" ]; then
        # Hacer backup de la configuración anterior si existe
        if [ -f "$CONF_DIR/quarkus-jdbc-sgi.properties" ]; then
            echo "Haciendo backup de la configuración anterior..."
            mv "$CONF_DIR/quarkus-jdbc-sgi.properties" "$CONF_DIR/quarkus-jdbc-sgi.properties.backup.$(date +%Y%m%d_%H%M%S)"
        fi
        
        # Copiar nueva configuración
        cp "$CONFIG_FILE" "$CONF_DIR/"
        echo "✅ Configuración desplegada en: $CONF_DIR/quarkus-jdbc-sgi.properties"
    else
        echo "⚠️  Archivo de configuración no encontrado: $CONFIG_FILE"
    fi
}

# Función para mostrar instrucciones post-despliegue
show_instructions() {
    echo ""
    echo "========================================="
    echo "✅ DESPLIEGUE COMPLETADO"
    echo "========================================="
    echo ""
    echo "Próximos pasos:"
    echo ""
    echo "1. 🔄 Reiniciar Keycloak:"
    echo "   cd $KEYCLOAK_HOME"
    echo "   ./bin/kc.sh start-dev"
    echo ""
    echo "2. 🌐 Acceder a la consola de administración:"
    echo "   http://localhost:8080/admin"
    echo ""
    echo "3. ⚙️  Configurar el User Storage Provider:"
    echo "   - Ir a: Realm 'juntaelectoral' → User Federation"
    echo "   - Agregar provider: 'sgi-user-storage'"
    echo "   - Configurar:"
    echo "     * Display Name: SGI Junta Electoral"
    echo "     * Nombre del DataSource: sgi-junta-electoral"
    echo "     * Política de Cache: DEFAULT"
    echo ""
    echo "4. 🧪 Probar autenticación con usuarios de prueba:"
    echo "   - admin.sgi / password123"
    echo "   - usuario.test / password123"
    echo "   - validador.sgi / password123"
    echo ""
    echo "5. 🔍 Verificar logs en caso de problemas:"
    echo "   tail -f $KEYCLOAK_HOME/data/log/keycloak.log"
    echo ""
    echo "========================================="
}

# Función principal
main() {
    echo "Iniciando proceso de build y deploy..."
    
    # Verificar si Keycloak está corriendo
    check_keycloak_running
    
    # Compilar proyecto
    build_project
    
    # Crear directorios necesarios
    create_directories
    
    # Desplegar JAR
    deploy_jar
    
    # Desplegar configuración
    deploy_config
    
    # Mostrar instrucciones
    show_instructions
}

# Ejecutar función principal
main "$@"
