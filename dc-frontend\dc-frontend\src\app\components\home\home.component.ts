import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { KeycloakService } from 'keycloak-angular';
import { HeaderComponent } from '../header/header.component';
import { FooterComponent } from '../footer/footer.component';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [CommonModule, HeaderComponent, FooterComponent],
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss']
})
export class HomeComponent implements OnInit {

  constructor(
    private keycloakService: KeycloakService,
    private http: HttpClient
  ) {}

  async ngOnInit() {
    // Sincronizar usuario al cargar la página home (después del login)
    try {
      const response = await this.http.get(`${environment.apiUrl}/auth/me`).toPromise();
      console.log('Usuario sincronizado:', response);
    } catch (error) {
      console.error('Error al sincronizar usuario:', error);
    }
  }

  logout() {
    this.keycloakService.logout(window.location.origin); // redirige a inicio después de logout
  }

}
