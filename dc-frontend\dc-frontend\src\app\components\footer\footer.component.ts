// src/app/components/footer/footer.component.ts
import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-footer',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './footer.component.html',
  styleUrls: ['./footer.component.scss']
})
export class FooterComponent {
  year = new Date().getFullYear();
  links = [
    { label: 'Política de privacidad', url: '/privacidad' },
    { label: 'Política de cookies',     url: '/cookies' },
    { label: 'Contacto',                url: '/contacto' },
  ];
}
