package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity;

import jakarta.persistence.*;

@Entity
@Table(name = "dac_t_tipo_documento")
public class TipoDocumentoEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "dac_id_tipo_documento")
    private Long id;

    @Column(name = "dac_tx_nombre")
    private String nombre;

    // Constructor por defecto
    public TipoDocumentoEntity() {
    }

    // Constructor con campos
    public TipoDocumentoEntity(Long id, String nombre) {
        this.id = id;
        this.nombre = nombre;
    }

    // Getters y Setters

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNombre() {
        return nombre;
    }

    public void setNombre(String nombre) {
        this.nombre = nombre;
    }
}
