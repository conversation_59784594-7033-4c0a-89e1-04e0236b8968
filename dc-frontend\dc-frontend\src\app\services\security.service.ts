import { Injectable } from '@angular/core';

/**
 * Servicio de seguridad adicional para proteger tokens
 */
@Injectable({
  providedIn: 'root'
})
export class SecurityService {

  constructor() {
    this.setupSecurityMeasures();
  }

  /**
   * Configura medidas de seguridad adicionales
   */
  private setupSecurityMeasures(): void {
    this.preventConsoleAccess();
    this.setupCSPViolationReporting();
    this.detectDevTools();
  }

  /**
   * Dificulta el acceso a tokens desde la consola
   */
  private preventConsoleAccess(): void {
    // Sobrescribir console.log para detectar intentos de acceso a tokens
    const originalLog = console.log;
    console.log = (...args: any[]) => {
      const message = args.join(' ');
      if (message.includes('token') || message.includes('Bearer')) {
        console.warn('⚠️ Intento de acceso a token detectado');
        // En producción, podrías reportar esto a tu sistema de monitoreo
      }
      originalLog.apply(console, args);
    };

    // Advertencia en consola
    console.warn(`
    🔒 ADVERTENCIA DE SEGURIDAD 🔒
    Esta aplicación contiene información sensible.
    El acceso no autorizado está prohibido y monitoreado.
    `);
  }

  /**
   * Configura reporte de violaciones CSP
   */
  private setupCSPViolationReporting(): void {
    document.addEventListener('securitypolicyviolation', (event) => {
      console.error('CSP Violation:', {
        blockedURI: event.blockedURI,
        violatedDirective: event.violatedDirective,
        originalPolicy: event.originalPolicy
      });
      
      // En producción, enviar a sistema de monitoreo
      this.reportSecurityViolation({
        type: 'csp_violation',
        details: {
          blockedURI: event.blockedURI,
          directive: event.violatedDirective
        }
      });
    });
  }

  /**
   * Detecta si las herramientas de desarrollador están abiertas
   */
  private detectDevTools(): void {
    let devtools = false;
    
    setInterval(() => {
      const before = Date.now();
      debugger; // Esta línea causa pausa si DevTools está abierto
      const after = Date.now();
      
      if (after - before > 100) {
        if (!devtools) {
          devtools = true;
          console.warn('🔍 Herramientas de desarrollador detectadas');
          // En producción, podrías limpiar tokens o mostrar advertencia
        }
      } else {
        devtools = false;
      }
    }, 1000);
  }

  /**
   * Reporta violaciones de seguridad (placeholder)
   */
  private reportSecurityViolation(violation: any): void {
    // En producción, enviar a tu sistema de monitoreo
    console.warn('Security violation reported:', violation);
  }

  /**
   * Valida la integridad de un token
   */
  validateTokenIntegrity(token: string): boolean {
    try {
      // Verificar estructura JWT básica
      const parts = token.split('.');
      if (parts.length !== 3) return false;

      // Verificar que se puede decodificar
      const payload = JSON.parse(atob(parts[1]));
      
      // Verificaciones básicas
      if (!payload.exp || !payload.iat || !payload.sub) return false;
      
      // Verificar que no está expirado
      const now = Math.floor(Date.now() / 1000);
      if (payload.exp < now) return false;

      return true;
    } catch {
      return false;
    }
  }

  /**
   * Genera hash de integridad para token
   */
  async generateTokenHash(token: string): Promise<string> {
    const encoder = new TextEncoder();
    const data = encoder.encode(token);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }
}
