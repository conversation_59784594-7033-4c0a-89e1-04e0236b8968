package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.EstadoCandidaturaEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository.EstadoCandidaturaRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class EstadoCandidaturaService {

    @Autowired
    private EstadoCandidaturaRepository estadoCandidaturaRepository;

    /**
     * Obtiene todos los estados de candidatura
     */
    public List<EstadoCandidaturaEntity> findAll() {
        return estadoCandidaturaRepository.findAll();
    }

    /**
     * Busca un estado de candidatura por ID
     */
    public Optional<EstadoCandidaturaEntity> findById(Long id) {
        return estadoCandidaturaRepository.findById(id);
    }

    /**
     * Busca un estado de candidatura por valor
     */
    public Optional<EstadoCandidaturaEntity> findByValor(String valor) {
        return estadoCandidaturaRepository.findByValor(valor);
    }

    /**
     * Obtiene el estado IMPORTADA
     */
    public Optional<EstadoCandidaturaEntity> getImportada() {
        return findByValor("IMPORTADA");
    }

    /**
     * Obtiene el estado BORRADOR
     */
    public Optional<EstadoCandidaturaEntity> getBorrador() {
        return findByValor("BORRADOR");
    }

    /**
     * Obtiene el estado MANUAL (alias para BORRADOR según Script 2)
     */
    public Optional<EstadoCandidaturaEntity> getManual() {
        return getBorrador();
    }

    /**
     * Obtiene el estado VALIDADA
     */
    public Optional<EstadoCandidaturaEntity> getValidada() {
        return findByValor("VALIDADA");
    }

    /**
     * Obtiene el estado RECHAZADA
     */
    public Optional<EstadoCandidaturaEntity> getRechazada() {
        return findByValor("RECHAZADA");
    }

    /**
     * Busca estados que contengan el texto especificado
     */
    public List<EstadoCandidaturaEntity> findByTextoContaining(String texto) {
        return estadoCandidaturaRepository.findByTextoContaining(texto);
    }

    /**
     * Verifica si existe un estado con el valor especificado
     */
    public boolean existsByValor(String valor) {
        return estadoCandidaturaRepository.existsByValor(valor);
    }

    /**
     * Guarda un estado de candidatura
     */
    public EstadoCandidaturaEntity save(EstadoCandidaturaEntity estadoCandidatura) {
        return estadoCandidaturaRepository.save(estadoCandidatura);
    }

    /**
     * Elimina un estado de candidatura por ID
     */
    public void deleteById(Long id) {
        estadoCandidaturaRepository.deleteById(id);
    }

    /**
     * Valida que el estado de candidatura sea válido
     */
    public boolean isValidEstado(String valor) {
        return "IMPORTADA".equals(valor) || "BORRADOR".equals(valor) ||
               "VALIDADA".equals(valor) || "RECHAZADA".equals(valor);
    }

    /**
     * Verifica si el estado permite modificaciones
     */
    public boolean isEditable(String valor) {
        return "IMPORTADA".equals(valor) || "MANUAL".equals(valor);
    }
}
