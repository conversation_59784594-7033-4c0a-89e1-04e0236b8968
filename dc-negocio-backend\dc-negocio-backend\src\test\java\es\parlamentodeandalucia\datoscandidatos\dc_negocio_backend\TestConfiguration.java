package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service.*;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.web.SecurityFilterChain;

@TestConfiguration
@EnableWebSecurity
class TestSecurityConfiguration {

    @Bean
    public SecurityFilterChain testSecurityFilterChain(HttpSecurity http) throws Exception {
        http
            .csrf(csrf -> csrf.disable())
            .authorizeHttpRequests(authz -> authz
                .requestMatchers("/api/candidaturas/**").hasRole("ADMIN")
                .requestMatchers("/api/importaciones/**").hasRole("ADMIN")
                .anyRequest().permitAll()
            );
        return http.build();
    }

    @MockBean
    private CandidaturaService candidaturaService;

    @MockBean
    private ImportacionService importacionService;

    @MockBean
    private EstadoCandidaturaService estadoCandidaturaService;

    @MockBean
    private TipoCandidaturaService tipoCandidaturaService;
}
