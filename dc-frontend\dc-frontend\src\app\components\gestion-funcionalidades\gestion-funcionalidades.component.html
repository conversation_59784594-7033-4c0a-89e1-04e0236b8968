<div class="layout-container">
  <app-header class="header-fixed"></app-header>
  <p-confirmDialog></p-confirmDialog>
  <p-toast></p-toast>
  <div class="page-wrapper scrollable-content">
    <div class="breadcrumbs">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="16px" height="16px">
        <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z" />
      </svg>
      <span class="separator">&gt;</span>
      <span>Administración</span>
      <span class="separator">&gt;</span>
      <strong>Funcionalidades</strong>
    </div>

    <div class="title-section">
      <a routerLink="/home" class="back-button">
        <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24" width="24px" height="24px">
          <path d="M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z" transform="rotate(180 12 12)" />
        </svg>
      </a>
      <h1 class="page-main-title">Funcionalidades</h1>
      <button pButton type="button" class="btn btn-primary nueva-funcionalidad-btn" (click)="abrirModalNueva()">
        Nueva Funcionalidad
      </button>
    </div>

    <div class="funcionalidades-page-container">
      <div class="filter-actions-bar">

        <button class="btn-text" (click)="clearFilters()">
          <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24" width="16px" height="16px">
            <path
              d="M6 13c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm14-10v2h-3.21l-3.42 8H18v2h-7.15c-.78 1.76-2.58 3-4.85 3-2.21 0-4-1.79-4-4s1.79-4 4-4c.78 0 1.5.22 2.15.62L12.58 4H20V2h2v2h-2z" />
          </svg>
          Limpiar filtros
        </button>

        <!-- Boton acciones masivas y sus opciones -->
        <div class="dropdown-masivo" (clickOutside)="showMassiveActions = false">
          <button class="btn-massive" (click)="showMassiveActions = !showMassiveActions">
              Acciones masivas
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                  viewBox="0 0 24 24">
                  <path d="M7 10l5 5 5-5H7z" />
              </svg>
          </button>

          <ul class="dropdown-options" *ngIf="showMassiveActions">
              <li (click)="activateAll()">Activar</li>
              <li (click)="deactivateAll()">Desactivar</li>
          </ul>

        </div>
      </div>

      <p-table #dt [value]="funcionalidades" [paginator]="true" [rows]="10"
        [globalFilterFields]="['nombre', 'status']" [selection]="selectedFuncionalidades"
        (selectionChange)="selectedFuncionalidades = $event" [rowHover]="true" dataKey="id"
        paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown">

        <ng-template pTemplate="header">
          <tr>
            <th style="width: 3rem">
              <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
            </th>
            <th pSortableColumn="id">
              Código
              <p-sortIcon field="id" />
              <p-columnFilter field="id" display="menu" type="numeric"></p-columnFilter>
            </th>
            <th pSortableColumn="nombre">
              Nombre
              <p-sortIcon field="nombre" />
              <p-columnFilter field="nombre" matchMode="contains" display="menu" type="text"></p-columnFilter>
            </th>
            <th pSortableColumn="activo">
              Estado
              <p-sortIcon field="activo" />
              <p-columnFilter field="activo" matchMode="equals" display="menu">
                <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                    <p-dropdown [options]="estados" [ngModel]="value" (onChange)="filter($event.value)"
                        optionLabel="label" optionValue="value" placeholder="Selecciona estado"
                        class="w-full">
                    </p-dropdown>
                </ng-template>
              </p-columnFilter>
            </th>
            <th style="width: 5rem;"></th>
          </tr>
        </ng-template>

        <ng-template pTemplate="body" let-f let-rowIndex="rowIndex">
          <tr [ngClass]="getRowStyle(f, rowIndex)">
            <td><p-tableCheckbox [value]="f"></p-tableCheckbox></td>
            <td>{{ f.id }}</td>
            <td>{{ f.nombre }}</td>
            <td>
              <span class="status-badge" [ngClass]="getStatusClass(f.activo)">
                {{ f.activo ? 'Activo' : 'Inactivo' }}
              </span>
            </td>
            <td class="acciones-cell">
              <div class="dropdown">
                <button class="btn-icon dropdown-toggle" (click)="toggleDropdown(f.id)">
                  Acciones
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor"
                    viewBox="0 0 24 24">
                    <path
                      d="M12 7a2 2 0 1 0 0-4 2 2 0 0 0 0 4zm0 2a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm0 6a2 2 0 1 0 0 4 2 2 0 0 0 0-4z" />
                  </svg>
                </button>
                <div class="dropdown-menu" *ngIf="openedDropdownId === f.id">
                  <a class="dropdown-item" (click)="abrirModalEdicion(f)">Editar</a>
                  <a class="dropdown-item" (click)="eliminar(f)">Eliminar</a>
                  <a class="dropdown-item" (click)="cambiarEstado(f)">
                    {{ f.activo ? 'Desactivar' : 'Activar' }}
                  </a>
                </div>
              </div>
            </td>
          </tr>
        </ng-template>

      </p-table>
    </div>
  </div>

  <app-footer class="footer-fixed"></app-footer>

    <p-dialog *ngIf="funcionalidadSeleccionada"
            header="{{ funcionalidadSeleccionada.id? 'Editar' : 'Nueva' }} Funcionalidad"
            [(visible)]="showModal"
            [modal]="true"
            [closable]="false"
            [style]="{ width: '400px' }">

      <div class="p-fluid" style="padding: 1rem 0;">
        
        <!-- Campo Nombre -->
        <div class="campo-nombre">
          <label for="nombre">Nombre</label>
          <input id="nombre"
                type="text"
                pInputText
                [(ngModel)]="funcionalidadSeleccionada.nombre"
                name="nombre"
                required />
        </div>

        <!-- Checkbox Activo -->
        <div class="campo-activo">
          <label for="activo">Activa</label>
          <p-checkbox inputId="activo"
                      [(ngModel)]="funcionalidadSeleccionada.activo"
                      binary="true"
                      name="activo">
          </p-checkbox>        
        </div>


      </div>

      <!-- Footer con botones -->
      <ng-template pTemplate="footer">
          <button pButton label="Cancelar"
                  icon="pi pi-times"
                  class="p-button-text"
                  (click)="cancelar()"></button>

          <button pButton label="Guardar"
                  icon="pi pi-check"
                  (click)="guardar()"
                  [disabled]="!funcionalidadSeleccionada.nombre"></button>
      </ng-template>
    </p-dialog>


</div>
