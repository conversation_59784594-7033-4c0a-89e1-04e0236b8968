openapi: 3.0.0
info:
  title: API Representantes - Datos Candi<PERSON>
  version: 1.0.0

servers:
  - url: http://localhost:8080/api
  - url: https://***************:8463/api/v1/myservice

paths:

  /circunscripciones:
    get:
      summary: Obtener lista de circunscripciones
      responses:
        '200':
          description: Lista de circunscripciones
          content:
            application/json:
              example:
                - id: 1
                  nombre: "Andalucía"
                  provincia: "Sevilla"
    post:
      summary: Crear nueva circunscripción
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Circunscripcion'
      responses:
        '201':
          description: Circunscripción creada correctamente

  /circunscripciones/{id}:
    get:
      summary: Obtener circunscripción por ID
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Circunscripción encontrada correctamente
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Circunscripcion'
    put:
      summary: Actualizar circunscripción por ID
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Circunscripcion'
      responses:
        '200':
          description: Circunscripción actualizada correctamente
    delete:
      summary: Eliminar circunscripción por ID 
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      responses:
        '204':
          description: Circunscripción eliminada correctamente

  /convocatorias:
    get:
      summary: Obtener lista de convocatorias
      operationId: getAllConvocatorias
      responses:
        '200':
          description: Lista de convocatorias
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Convocatoria'
              examples: # Es útil tener ejemplos claros
                convocatoriasEjemplo:
                  value:
                    - id: 1
                      descripcion: "Elecciones 2024"
                      fechaConvocatoria: "2024-03-01"
                      fechaElecciones: "2024-04-20"
                      fechaInicioPresentacion: "2024-03-16"
                      fechaFinPresentacion: "2024-03-21"
                      fechaPublicacionBoja: "2024-03-23"
                      # ... y todas las demás fechas
                      fechaCancelacion: "2024-03-26"
                      logotipoBase64: "iVBORw0KGgoAAAANSUhEUgAAAAUA..." # Ejemplo de Base64
    post:
      summary: Crear nueva convocatoria
      operationId: createConvocatoria
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConvocatoriaRequest' # Usa un DTO de Request si algunos campos son solo para entrada
      responses:
        '201':
          description: Convocatoria creada correctamente
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Convocatoria'
        '400':
          description: Solicitud inválida (ej. fechas cronológicamente incorrectas, campos requeridos faltantes)
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string

  /convocatorias/{id}:
    get:
      summary: Obtener convocatoria por ID
      operationId: getConvocatoriaById
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Convocatoria encontrada correctamente
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Convocatoria'
        '404':
          description: Convocatoria no encontrada

    put:
      summary: Actualizar convocatoria por ID
      operationId: updateConvocatoria
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConvocatoriaRequest' # O Convocatoria si es el mismo para entrada
      responses:
        '200':
          description: Convocatoria actualizada correctamente
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Convocatoria'
        '400':
          description: Solicitud inválida
        '404':
          description: Convocatoria no encontrada

    delete:
      summary: Eliminar convocatoria por ID
      operationId: deleteConvocatoria
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '204':
          description: Convocatoria eliminada correctamente
        '404':
          description: Convocatoria no encontrada

  /convocatorias/{id}/logotipo:
    post: # O PUT, si siempre se reemplaza
      summary: Subir o actualizar el logotipo de una convocatoria
      operationId: uploadLogotipoConvocatoria
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          multipart/form-data: # Importante para subir archivos
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary # Indica un archivo binario
                  description: Archivo del logotipo (PNG o SVG, máx. 1 MB)
      responses:
        '200':
          description: Logotipo subido/actualizado correctamente
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Convocatoria' # Podría devolver la convocatoria con el logotipo actualizado
        '400':
          description: Solicitud inválida (ej. formato o tamaño incorrecto)
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
        '404':
          description: Convocatoria no encontrada

  /candidatos/{id}:
    get:
      summary: Obtener candidato por ID
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Candidato encontrado correctamente
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Candidato'
    put:
      summary: Actualizar candidato por ID
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Candidato'
      responses:
        '200':
          description: Candidato actualizado correctamente
    delete:
      summary: Eliminar candidato por ID
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      responses:
        '204':
          description: Candidato eliminado correctamente

  /representantes:
    get:
      summary: Obtener lista de representantes
      responses:
        '200':
          description: Lista de representantes
          content:
            application/json:
              example:
                - id: 1
                  nombre: "Estibaliz"
                  email: "<EMAIL>"
                  circunscripcion: "Sevilla"
    post:
      summary: Crear nuevo representante
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Representante'
      responses:
        '201':
          description: Representante creado correctamente

  /representantes/{id}:
    get:
      summary: Obtener representante por ID
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Representante encontrado correctamente
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Representante'
    put:
      summary: Actualizar representante por ID
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Representante'
      responses:
        '200':
          description: Representante actualizado correctamente
    delete:
      summary: Eliminar representante por id
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      responses:
        '204':
          description: Representante eliminado correctamente

  /representantes/{id}/candidatos:
    get:
      summary: Obtener candidatos asociados a un representante
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Lista de candidatos generada correctamente

  /roles:
    get:
      summary: Listado de roles
      responses:
        '200':
          description: Lista de roles generada correctamente

  /usuarios:
    get:
      summary: Listado de usuarios
      responses:
        '200':
          description: Lista de usuarios
    post:
      summary: Crear un usuario
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Usuario'
      responses:
        '201':
          description: Usuario creado correctamente

  /usuarios/{id}:
    get:
      summary: Consultar usuario por ID
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Usuario encontrado correctamente
    put:
      summary: Modificar un usuario
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Usuario'
      responses:
        '200':
          description: Usuario actualizado correctamente
    delete:
      summary: Eliminar usuario
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      responses:
        '204':
          description: Usuario eliminado correctamente

  /auth/login:
    post:
      summary: Iniciar sesión
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                username:
                  type: string
                password:
                  type: string
      responses:
        '200':
          description: Inicio de sesion correcto

  /auth/logout:
    post:
      summary: Cerrar sesión
      responses:
        '204':
          description: Sesión cerrada correctamente

  /auth/me:
    get:
      summary: Obtener datos del usuario autenticado
      responses:
        '200':
          description: Datos del usuario autenticado
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Usuario'

components:
  schemas:
    Representante:
      type: object
      properties:
        id:
          type: integer
        nombre:
          type: string
        email:
          type: string
        circunscripcion:
          type: string

    Usuario:
      type: object
      properties:
        id:
          type: integer
        username:
          type: string
        email:
          type: string
        rol:
          type: string

    Circunscripcion:
      type: object
      properties:
        id:
          type: integer
        nombre:
          type: string
        provincia:
          type: string

    Convocatoria: # Schema para la respuesta (output DTO)
      type: object
      properties:
        id:
          type: integer
          format: int64
          readOnly: true # El ID lo genera el backend
        descripcion:
          type: string
          description: Descripción o nombre de la convocatoria
        fechaConvocatoria:
          type: string
          format: date # Formato YYYY-MM-DD
          description: Fecha de convocatoria elecciones (Fecha publicación votaciones en BOJA).
        fechaElecciones:
          type: string
          format: date
          description: Fecha elecciones (Fecha publicada en BOJA).
          nullable: true # Puede ser nulo si se determina más tarde o no es aplicable
        fechaInicioPresentacion:
          type: string
          format: date
          description: Fecha Inicio presentación Candidaturas.
          nullable: true
        fechaFinPresentacion:
          type: string
          format: date
          description: Fecha Fin presentación Candidaturas.
          nullable: true
        fechaPublicacionBoja: # Corresponde a dac_fh_inicio_publicacion_boja
          type: string
          format: date
          description: Fecha Publicación Candidaturas en BOJA y BOP.
          nullable: true
        fechaIrregularidades:
          type: string
          format: date
          description: Fecha Comunicación Irregularidades.
          nullable: true
        fechaInicioSubsanacion:
          type: string
          format: date
          description: Fecha Inicio Subsanación.
          nullable: true
        fechaFinSubsanacion:
          type: string
          format: date
          description: Fecha Fin Subsanación.
          nullable: true
        fechaProclamacion:
          type: string
          format: date
          description: Fecha Proclamación Candidaturas Definitivas.
          nullable: true
        fechaPublicacionProclamada:
          type: string
          format: date
          description: Fecha Publicación Candidaturas Proclamadas.
          nullable: true
        fechaInicioDeclaracion:
          type: string
          format: date
          description: Fecha Inicio Declaraciones.
          nullable: true
        fechaFinDeclaracion:
          type: string
          format: date
          description: Fecha Fin Declaraciones.
          nullable: true
        fechaInicioPublicacionInternet:
          type: string
          format: date
          description: Fecha Inicio Publicación Internet.
          nullable: true
        fechaFinPublicacionInternet:
          type: string
          format: date
          description: Fecha Fin Publicación Internet.
          nullable: true
        fechaCancelacion:
          type: string
          format: date
          description: Fecha Cancelación Datos.
          nullable: true
        logotipoBase64:
          type: string
          format: byte # Indica que es una cadena Base64
          description: Logotipo del proceso en formato Base64.
          nullable: true
      required:
        - descripcion
        - fechaConvocatoria # La fecha de convocatoria es la principal para los cálculos

    ConvocatoriaRequest: # Schema para la solicitud (input DTO) - Puede ser el mismo que Convocatoria si no hay diferencias
      type: object
      properties:
        id: # Para PUT, el ID puede ir en el body o no, pero ya está en el path
          type: integer
          format: int64
        descripcion:
          type: string
        fechaConvocatoria:
          type: string
          format: date
        fechaElecciones:
          type: string
          format: date
          nullable: true
        fechaInicioPresentacion:
          type: string
          format: date
          nullable: true
        fechaFinPresentacion:
          type: string
          format: date
          nullable: true
        fechaPublicacionBoja:
          type: string
          format: date
          nullable: true
        fechaIrregularidades:
          type: string
          format: date
          nullable: true
        fechaInicioSubsanacion:
          type: string
          format: date
          nullable: true
        fechaFinSubsanacion:
          type: string
          format: date
          nullable: true
        fechaProclamacion:
          type: string
          format: date
          nullable: true
        fechaPublicacionProclamada:
          type: string
          format: date
          nullable: true
        fechaInicioDeclaracion:
          type: string
          format: date
          nullable: true
        fechaFinDeclaracion:
          type: string
          format: date
          nullable: true
        fechaInicioPublicacionInternet:
          type: string
          format: date
          nullable: true
        fechaFinPublicacionInternet:
          type: string
          format: date
          nullable: true
        fechaCancelacion:
          type: string
          format: date
          nullable: true
        # El logotipo NO se envía en el JSON para POST/PUT del recurso principal,
        # se envía con el endpoint específico de /logotipo usando multipart/form-data
        # Si quisieras la auditoría en el request, tendrías que definirla aquí,
        # pero normalmente es una propiedad de solo lectura generada por el backend.
      required:
        - descripcion
        - fechaConvocatoria

    Candidato:
      type: object
      properties:
        id:
          type: integer
        nombre:
          type: string
        apellido:
          type: string
        email:
          type: string
        representante_id:
          type: integer
        circunscripcion_id:
          type: integer