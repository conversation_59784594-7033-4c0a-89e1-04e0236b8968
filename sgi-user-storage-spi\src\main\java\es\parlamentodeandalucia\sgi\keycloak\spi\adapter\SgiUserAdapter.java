package es.parlamentodeandalucia.sgi.keycloak.spi.adapter;

import es.parlamentodeandalucia.sgi.keycloak.spi.entity.SgiCredencialEntity;
import org.keycloak.component.ComponentModel;
import org.keycloak.models.KeycloakSession;
import org.keycloak.models.RealmModel;
import org.keycloak.models.RoleModel;
import org.keycloak.models.UserModel;
import org.keycloak.storage.StorageId;
import org.keycloak.storage.adapter.AbstractUserAdapterFederatedStorage;

import java.util.*;
import java.util.stream.Stream;

/**
 * Adaptador de usuario para el SGI User Storage SPI
 * Mapea entre SgiCredencialEntity y UserModel de Keycloak
 */
public class SgiUserAdapter extends AbstractUserAdapterFederatedStorage {

    private final SgiCredencialEntity credencial;
    private final String keycloakId;

    public SgiUserAdapter(KeycloakSession session, RealmModel realm, ComponentModel model, SgiCredencialEntity credencial) {
        super(session, realm, model);
        this.credencial = credencial;
        this.keycloakId = generateKeycloakId(credencial);
    }

    private String generateKeycloakId(SgiCredencialEntity credencial) {
        return StorageId.keycloakId(this.storageProviderModel, credencial.getId().toString());
    }

    @Override
    public String getId() {
        return keycloakId;
    }

    @Override
    public String getUsername() {
        return credencial.getUsername();
    }

    @Override
    public void setUsername(String username) {
        credencial.setUsername(username);
    }

    @Override
    public String getEmail() {
        return credencial.getIdentidad() != null ? credencial.getIdentidad().getEmail() : null;
    }

    @Override
    public void setEmail(String email) {
        // Los datos se gestionan en la identidad, no directamente en credencial
        // Esta operación requeriría actualizar la identidad asociada
    }

    @Override
    public boolean isEmailVerified() {
        // Por defecto, consideramos los emails como verificados
        return true;
    }

    @Override
    public void setEmailVerified(boolean verified) {
        // No implementamos verificación de email en el SGI
    }



    @Override
    public boolean isEnabled() {
        return credencial.isAccountActive();
    }

    @Override
    public void setEnabled(boolean enabled) {
        // El estado se gestiona a través de la entidad estado, no directamente
    }

    @Override
    public Long getCreatedTimestamp() {
        if (credencial.getFechaAlta() != null) {
            return credencial.getFechaAlta().atStartOfDay(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();
        }
        return null;
    }

    @Override
    public void setCreatedTimestamp(Long timestamp) {
        if (timestamp != null) {
            credencial.setFechaAlta(
                java.time.LocalDate.ofInstant(
                    java.time.Instant.ofEpochMilli(timestamp),
                    java.time.ZoneId.systemDefault()
                )
            );
        }
    }

    @Override
    public String getFirstAttribute(String name) {
        switch (name) {
            case "nombre":
                return credencial.getIdentidad() != null ? credencial.getIdentidad().getNombre() : null;
            case "apellido1":
                return credencial.getIdentidad() != null ? credencial.getIdentidad().getApellido1() : null;
            case "apellido2":
                return credencial.getIdentidad() != null ? credencial.getIdentidad().getApellido2() : null;
            case "nif":
                return credencial.getIdentidad() != null ? credencial.getIdentidad().getIdentificador() : null;
            case "telefono":
                return credencial.getIdentidad() != null ? credencial.getIdentidad().getMovil() : null;
            case "fecha_alta":
                return credencial.getFechaAlta() != null ? credencial.getFechaAlta().toString() : null;
            case "fecha_baja":
                return credencial.getFechaBaja() != null ? credencial.getFechaBaja().toString() : null;
            case "estado":
                return credencial.getEstado() != null ? credencial.getEstado().getValor() : null;
            case "account_locked":
                return credencial.isAccountLocked() ? "true" : "false";
            default:
                return super.getFirstAttribute(name);
        }
    }

    @Override
    public Map<String, List<String>> getAttributes() {
        Map<String, List<String>> attributes = super.getAttributes();

        // Agregar atributos específicos del SGI
        if (credencial.getIdentidad() != null) {
            if (credencial.getIdentidad().getNombre() != null) {
                attributes.put("nombre", Arrays.asList(credencial.getIdentidad().getNombre()));
            }
            if (credencial.getIdentidad().getApellido1() != null) {
                attributes.put("apellido1", Arrays.asList(credencial.getIdentidad().getApellido1()));
            }
            if (credencial.getIdentidad().getApellido2() != null) {
                attributes.put("apellido2", Arrays.asList(credencial.getIdentidad().getApellido2()));
            }
            if (credencial.getIdentidad().getIdentificador() != null) {
                attributes.put("nif", Arrays.asList(credencial.getIdentidad().getIdentificador()));
            }
            if (credencial.getIdentidad().getMovil() != null) {
                attributes.put("telefono", Arrays.asList(credencial.getIdentidad().getMovil()));
            }
        }

        if (credencial.getFechaAlta() != null) {
            attributes.put("fecha_alta", Arrays.asList(credencial.getFechaAlta().toString()));
        }
        if (credencial.getFechaBaja() != null) {
            attributes.put("fecha_baja", Arrays.asList(credencial.getFechaBaja().toString()));
        }
        if (credencial.getEstado() != null) {
            attributes.put("estado", Arrays.asList(credencial.getEstado().getValor()));
        }

        attributes.put("account_locked", Arrays.asList(credencial.isAccountLocked() ? "true" : "false"));

        return attributes;
    }

    @Override
    public Stream<String> getAttributeStream(String name) {
        List<String> values = getAttributes().get(name);
        return values != null ? values.stream() : Stream.empty();
    }

    @Override
    public void setAttribute(String name, List<String> values) {
        if (values == null || values.isEmpty()) {
            return;
        }

        // Los atributos se gestionan a través de la identidad, no directamente en credencial
        // Esta operación requeriría actualizar la identidad asociada
        super.setAttribute(name, values);
    }

    @Override
    public void removeAttribute(String name) {
        // Los atributos se gestionan a través de la identidad, no directamente en credencial
        super.removeAttribute(name);
    }

    @Override
    public void setSingleAttribute(String name, String value) {
        setAttribute(name, value != null ? Arrays.asList(value) : null);
    }

    // Métodos relacionados con roles (para uso futuro)
    @Override
    public Stream<RoleModel> getRealmRoleMappingsStream() {
        // Por ahora, no mapeamos roles del SGI a roles de Keycloak
        // Esto se puede implementar en el futuro si es necesario
        return Stream.empty();
    }

    @Override
    public Stream<RoleModel> getClientRoleMappingsStream(org.keycloak.models.ClientModel client) {
        // Por ahora, no mapeamos roles del SGI a roles de cliente
        return Stream.empty();
    }

    @Override
    public boolean hasRole(RoleModel role) {
        // Por ahora, no verificamos roles del SGI
        return false;
    }

    @Override
    public void grantRole(RoleModel role) {
        // Los roles se gestionan en el SGI, no en Keycloak
        // Esta operación no está soportada
    }

    @Override
    public void deleteRoleMapping(RoleModel role) {
        // Los roles se gestionan en el SGI, no en Keycloak
        // Esta operación no está soportada
    }

    // Método de utilidad para obtener la entidad SGI
    public SgiCredencialEntity getSgiCredencial() {
        return credencial;
    }
}
