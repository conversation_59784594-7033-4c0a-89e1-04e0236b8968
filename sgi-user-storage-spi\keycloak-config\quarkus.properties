# Configuración del DataSource para SGI User Storage SPI
# Este archivo debe copiarse al directorio conf/ de Keycloak

# Configuración del datasource SGI (siguiendo el patrón del README)
quarkus.datasource.sgi-junta-electoral.db-kind=postgresql
quarkus.datasource.sgi-junta-electoral.username=sgi_user
quarkus.datasource.sgi-junta-electoral.password=sgi_password
quarkus.datasource.sgi-junta-electoral.jdbc.url=**************************************************

# Configurar transacciones XA para múltiples datasources
quarkus.datasource.sgi-junta-electoral.jdbc.transactions=xa