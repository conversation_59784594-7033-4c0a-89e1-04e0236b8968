package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity;

import java.util.List;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;


@Entity
@Table(name = "dac_t_circunscripcion")
public class CircunscripcionEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "dac_id_circunscripcion")
    private Long id;
    
    @Column(name = "dac_tx_codigo")
    private String codigoProvincia;

    @Column(name = "dac_tx_nombre", unique = true)
    private String nombre;

    @Column(name = "dac_bl_activa")
    private Boolean candidaturaActiva;

    @OneToMany(mappedBy = "circunscripcion")
    private List<CandidaturaEntity> candidaturas;

    // Constructor por defecto
    public CircunscripcionEntity() {
        // Constructor por defecto necesario para JPA
    }

    // Constructor principal
    public CircunscripcionEntity(Long id, String codigoProvincia, String nombre, Boolean candidaturaActiva) {
        this.id = id;
        this.codigoProvincia = codigoProvincia;
        this.nombre = nombre;
        this.candidaturaActiva = candidaturaActiva;
    }

    // Getters y Setters

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNombre() {
        return nombre;
    }

    public void setNombre(String nombre) {
        this.nombre = nombre;
    }

    public Boolean getCandidaturaActiva() {
        return this.candidaturas != null && !this.candidaturas.isEmpty();
    } 

    public void setCandidaturaActiva(Boolean candidaturaActiva) {
        this.candidaturaActiva = candidaturaActiva;
    }

    public String getCodigoProvincia() {
        return codigoProvincia;
    }

    public void setCodigoProvincia(String codigoProvincia) {
        this.codigoProvincia = codigoProvincia;
    }

    public List<CandidaturaEntity> getCandidaturas() {
        return candidaturas;
    }

    public void setCandidaturas(List<CandidaturaEntity> candidaturas) {
        this.candidaturas = candidaturas;
    }
}
