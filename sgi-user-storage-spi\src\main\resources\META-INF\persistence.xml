<?xml version="1.0" encoding="UTF-8"?>
<persistence xmlns="https://jakarta.ee/xml/ns/persistence"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xsi:schemaLocation="https://jakarta.ee/xml/ns/persistence 
             https://jakarta.ee/xml/ns/persistence/persistence_3_0.xsd"
             version="3.0">

    <!-- Unidad de persistencia para el SGI User Storage SPI -->
    <persistence-unit name="sgi-user-store" transaction-type="JTA">
        
        <!-- Entidades del SGI -->
        <class>es.parlamentodeandalucia.sgi.keycloak.spi.entity.SgiCredencialEntity</class>
        <class>es.parlamentodeandalucia.sgi.keycloak.spi.entity.SgiIdentidadEntity</class>
        <class>es.parlamentodeandalucia.sgi.keycloak.spi.entity.SgiTipoIdentificadorEntity</class>
        <class>es.parlamentodeandalucia.sgi.keycloak.spi.entity.SgiEstadoEntity</class>
        <!-- NOTA: Los roles se gestionan en el sistema SGI según el diagrama -->
        
        <properties>
            <!-- Configuración de Hibernate -->
            <property name="hibernate.dialect" value="org.hibernate.dialect.PostgreSQLDialect"/>
            <property name="hibernate.connection.datasource" value="sgi-junta-electoral"/>
            <property name="jakarta.persistence.transactionType" value="JTA"/>

            <!-- Configuración de esquema -->
            <property name="hibernate.hbm2ddl.auto" value="none"/>
            <property name="hibernate.show_sql" value="false"/>
            <property name="hibernate.format_sql" value="false"/>

            <!-- Configuración de cache -->
            <property name="hibernate.cache.use_second_level_cache" value="false"/>
            <property name="hibernate.cache.use_query_cache" value="false"/>

            <!-- Configuración de conexiones -->
            <property name="hibernate.connection.autocommit" value="false"/>
            <property name="hibernate.connection.isolation" value="2"/>

            <!-- Configuración de logging -->
            <property name="hibernate.generate_statistics" value="false"/>
        </properties>
    </persistence-unit>

</persistence>
