import { Component, OnInit, ChangeDetectorRef, ViewChild } from '@angular/core'; // Añade ViewChild
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { HttpClientModule } from '@angular/common/http';

// Importaciones de PrimeNG
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { CheckboxModule } from 'primeng/checkbox';
import { DropdownModule } from 'primeng/dropdown';
import { MenuModule } from 'primeng/menu';
import { InputTextModule } from 'primeng/inputtext';
import { PaginatorModule } from 'primeng/paginator'; // Aunque TableModule ya lo incluye, a veces es útil si lo usas fuera.
import { DialogModule } from 'primeng/dialog'; // Si usas el sidebar como un diálogo de PrimeNG
import { MultiSelectModule } from 'primeng/multiselect'; // Para filtros de columna avanzados si los necesitaras
import { InputIconModule } from 'primeng/inputicon';
import { IconFieldModule } from 'primeng/iconfield';
import { SortEvent } from 'primeng/api'; // Para customSort si lo implementas
import { TagModule } from 'primeng/tag'; // Si usas etiquetas para el estado activo/inactivo
import { SelectModule } from 'primeng/select';
import { SliderModule } from 'primeng/slider'; // Si necesitas un slider para algún campo

// servicio y modelo
import { CircunscripcionesService, Circunscripcion } from '../../services/circunscripcionService';
import { HeaderComponent } from "../header/header.component";
import { FooterComponent } from "../footer/footer.component";


@Component({
  selector: 'app-circunscripciones',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    HeaderComponent,
    FooterComponent,
    FormsModule,
    HttpClientModule,
    // Necesario para ngModel
    // Módulos de PrimeNG
    TableModule,
    ButtonModule,
    CheckboxModule,
    DropdownModule,
    MenuModule,
    InputTextModule,
    PaginatorModule,
    DialogModule,
    MultiSelectModule,
    InputIconModule,
    IconFieldModule,
    TagModule,
    SliderModule,
    SelectModule
  ],
  templateUrl: './circunscripciones.component.html',
  styleUrls: ['./circunscripciones.component.scss']
})

export class CircunscripcionesComponent implements OnInit {
  constructor(private circService: CircunscripcionesService) { }

  // Referencia a la p-table para el filtro global y otras operaciones
  @ViewChild('dt', { static: false }) dt!: any;

  circunscripciones: Circunscripcion[] = [];
  // Para la selección de filas con PrimeNG, se usa un array de objetos Circunscripcion
  selectedCircunscripciones: Circunscripcion[] = [];
  // Valor para el input de filtro global
  globalFilterValue: string = '';
  // Variable para controlar el dropdown abierto (se mantiene si lo usas fuera de p-table)
  openDropdownId: number | null = null;

  isEditing: boolean = false;

  // Variable para controlar el sidebar de "Añadir circunscripción"
  showAddCircunscripcionSidebar: boolean = false;
  newCircunscripcion: Circunscripcion = {
    id: 0, // El ID será generado por el backend
    codigoProvincia: '',
    nombre: '',
    candidaturaActiva: false // Valor por defecto para el formulario
  };
  selectedRows: number[] = [];

  ngOnInit(): void {
    console.log('CircunscripcionesComponent inicializado');
    this.loadCircunscripciones(); // Carga inicial de datos
  }

  loadCircunscripciones(): void {

    this.circService.getAll().subscribe({
      next: (data: Circunscripcion[]) => {
        console.log('Datos recibidos al cargar circunscripciones:', data);
        this.circunscripciones = data;
      },
      error: (err) => {
        console.error('Error al obtener circunscripciones en loadCircunscripciones:', err);
      }
    });
  }

  // Simula la acción de un elemento del menú de acciones de fila
  rowAction(circunscripcionId: number, action: string): void {
    console.log(`Acción '${action}' para la circunscripción con ID: ${circunscripcionId}`);
    if (action === 'edit') {
      this.isEditing = true; // Establecer modo edición
      this.circService.getById(circunscripcionId).subscribe({
        next: (circunscripcionData: Circunscripcion) => {
          this.newCircunscripcion = { ...circunscripcionData }; // Cargar datos en el formulario
          this.showAddCircunscripcionSidebar = true; // Abrir sidebar
        },
        error: (err) => {
          console.error(`Error al obtener circunscripción con ID ${circunscripcionId}:`, err);
        }
      });
    }
  }

  // --- Métodos de filtro global ---
  onGlobalFilter(event: Event, dt: any): void {
    const inputElement = event.target as HTMLInputElement;
    if (dt) {
      dt.filterGlobal(inputElement.value, 'contains');
    }
  }


  // Función para seleccionar/deseleccionar todas las filas
  toggleSelectAll(event: Event): void {
    const isChecked = (event.target as HTMLInputElement).checked;
    if (isChecked) {
      this.selectedRows = this.circunscripciones.map(c => c.id);
    } else {
      this.selectedRows = [];
    }
  }

  // Función para seleccionar/deseleccionar una fila individual
  toggleSelectRow(circunscripcionId: number): void {
    const index = this.selectedRows.indexOf(circunscripcionId);
    if (index > -1) {
      this.selectedRows.splice(index, 1); // Deseleccionar
    } else {
      this.selectedRows.push(circunscripcionId); // Seleccionar
    }
  }

  // Comprueba si una fila está seleccionada
  isRowSelected(circunscripcionId: number): boolean {
    return this.selectedRows.includes(circunscripcionId);
  }

  // Simula la acción de limpiar filtros
  clearFilters(): void {
    console.log('Limpiar filtros clicado');
    this.globalFilterValue = ''; // Limpia el valor del input de filtro
    if (this.dt) {
      this.dt.filterGlobal('', 'contains'); // Borra el filtro global en la tabla PrimeNG
      this.dt.reset();
    }
    this.loadCircunscripciones();
  }

  // Función para obtener la clase CSS del estado (Activo/Inactivo)
  getStatusClass(activo: boolean): string {
    return activo ? 'status-active' : 'status-inactive';
  }

  getActions(id: number) {
    return [
      {
        label: 'Editar',
        icon: 'pi pi-pencil',
        command: () => this.rowAction(id, 'edit')
      }
    ];
  }

}
