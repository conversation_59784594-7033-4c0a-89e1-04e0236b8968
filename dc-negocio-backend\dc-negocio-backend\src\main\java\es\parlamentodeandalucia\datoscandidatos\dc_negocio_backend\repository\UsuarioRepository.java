package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.UsuarioEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UsuarioRepository extends JpaRepository<UsuarioEntity, Long> {

    // Buscar usuario por ID de Keycloak
    Optional<UsuarioEntity> findByKeycloakId(String keycloakId);

    // Buscar usuarios por estado
    List<UsuarioEntity> findByEstado(String estado);

    // Métodos de búsqueda por nombre, email, username, rol y estado
    @Query("""
        SELECT u FROM UsuarioEntity u
        JOIN u.roles r
        WHERE (:nombre IS NULL OR u.nombre LIKE %:nombre%)
        AND (:email IS NULL OR u.email LIKE %:email%)
        AND (:username IS NULL OR u.username LIKE %:username%)
        AND (:rol IS NULL OR r.valor LIKE %:rol%)
        AND (:estado IS NULL OR u.estado LIKE %:estado%)
    """)
    List<UsuarioEntity> buscarUsuarios(
        @Param("nombre") String nombre,
        @Param("email") String email,
        @Param("username") String username,
        @Param("rol") String rol,
        @Param("estado") String estado
    );
    
    // Buscar usuarios cuyo nombre contenga una subcadena, ignorando mayúsculas/minúsculas
    List<UsuarioEntity> findByNombreContainingIgnoreCase(String nombre);

    // Buscar usuarios cuyo email contenga una subcadena, ignorando mayúsculas/minúsculas
    List<UsuarioEntity> findByEmailContainingIgnoreCase(String email);

    // Buscar usuarios por rol específico, ignorando mayúsculas/minúsculas
    List<UsuarioEntity> findByRolesValorIgnoreCase(String valor);

    // Buscar usuarios por rol y estado, ignorando mayúsculas/minúsculas
    List<UsuarioEntity> findByRolesValorIgnoreCaseAndEstadoIgnoreCase(String rol, String estado);

}
