spring.application.name=dc-negocio-backend

# Configuración url frontend
frontend.url=https://***************:8007

# Configuración del servidor
server.port=8084 
server.servlet.context-path=/

# --- INICIO CONFIGURACIÓN SSL/HTTPS ---
server.ssl.enabled=true
server.ssl.key-store=file:/app/config/keystore-prod.p12
server.ssl.key-store-type=PKCS12
server.ssl.key-store-password=123456
server.ssl.key-alias=springboot-prod
# --- FIN CONFIGURACIÓN SSL/HTTPS ---

# Configuración de SpringDoc OpenAPI
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method
springdoc.swagger-ui.tagsSorter=alpha
springdoc.swagger-ui.tryItOutEnabled=true
springdoc.swagger-ui.filter=true
springdoc.swagger-ui.url=/swagger.yaml


# Configuración de CORS
# ¡IMPORTANTE! Si es para PRODUCCIÓN, spring.web.cors.allowed-origins NO debería incluir localhost.
# La IP del backend ahora es HTTPS en el puerto 8084
spring.web.cors.allowed-origins=https://***************:8007
spring.web.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
spring.web.cors.allowed-headers=*
spring.web.cors.allow-credentials=true

# Configuración de logging
logging.level.es.parlamentodeandalucia.datoscandidatos=INFO
logging.level.org.springframework.web=WARN

# Configuración de la base de datos
# Asegúrate de que tu PostgreSQL está escuchando en 8012 y accesible desde el backend
spring.datasource.url=******************************************************
spring.datasource.username=postgres
spring.datasource.password=postgres
spring.datasource.driver-class-name=org.postgresql.Driver

# Opcional: Configuración para Hibernate/JPA
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.properties.hibernate.use_sql_comments=false

# Configuración de logging detallado
logging.level.es.parlamentodeandalucia.datoscandidatos=INFO
logging.level.org.hibernate.SQL=WARN
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=WARN
logging.level.org.springframework.security=WARN
logging.level.org.springframework.web=WARN


# Configuración de Keycloak OAuth2 Resource Server (automática)
# ¡Las URLs de Keycloak también DEBEN ser HTTPS ahora!
spring.security.oauth2.resourceserver.jwt.issuer-uri=https://***************:8453/realms/parlamento
spring.security.oauth2.resourceserver.jwt.jwk-set-uri=https://***************:8453/realms/parlamento/protocol/openid-connect/certs

# Configuración adicional de JWT
spring.security.oauth2.resourceserver.jwt.jws-algorithm=RS256

# Configuración de Keycloak Client (opcional para client credentials)
keycloak.realm=parlamento
keycloak.auth-server-url=https://***************:8453 # ¡URL de Keycloak en HTTPS!
keycloak.resource=datosCandidatos
keycloak.public-client=true

# Deshabilitar métricas OTLP para desarrollo
management.otlp.metrics.export.enabled=false

# Logging para debugging de JWT
logging.level.org.springframework.security=WARN
logging.level.org.springframework.security.oauth2=WARN
logging.level.org.springframework.web.cors=WARN