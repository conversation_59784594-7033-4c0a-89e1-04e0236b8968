package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.controller;

import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.CandidaturaEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.FormacionPoliticaEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.CircunscripcionEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.EstadoCandidaturaEntity;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service.CandidaturaService;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service.FormacionPoliticaService;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service.CircunscripcionService;
import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.dto.CandidaturaCreateRequest;
import es.parlamentodeandalucia.datoscandidatos.dto.Candidatura;
import es.parlamentodeandalucia.datoscandidatos.dto.FormacionPolitica;
import es.parlamentodeandalucia.datoscandidatos.dto.Circunscripcion;
import java.util.stream.Collectors;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;

@RestController
@RequestMapping("/api/candidaturas")
@CrossOrigin(origins = { "http://localhost:4200", "https://localhost:4200" })
public class CandidaturaController {

    @Autowired
    private CandidaturaService candidaturaService;

    @Autowired
    private FormacionPoliticaService formacionPoliticaService;

    @Autowired
    private CircunscripcionService circunscripcionService;

    public CandidaturaController(CandidaturaService candidaturaService,
                                FormacionPoliticaService formacionPoliticaService,
                                CircunscripcionService circunscripcionService) {
        this.candidaturaService = candidaturaService;
        this.formacionPoliticaService = formacionPoliticaService;
        this.circunscripcionService = circunscripcionService;
    }

    /**
     * Obtiene todas las candidaturas
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<List<Candidatura>> getAllCandidaturas() {
        List<CandidaturaEntity> entities = candidaturaService.findAll();
        List<Candidatura> candidaturas = entities.stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());
        return ResponseEntity.ok(candidaturas);
    }

    /**
     * Obtiene una candidatura por ID
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<CandidaturaEntity> getCandidaturaById(@PathVariable Long id) {
        return candidaturaService.findById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Obtiene una candidatura con sus candidatos
     */
    @GetMapping("/{id}/candidatos")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<CandidaturaEntity> getCandidaturaConCandidatos(@PathVariable Long id) {
        return candidaturaService.findByIdWithCandidatos(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Busca candidaturas con filtros y paginación
     */
    @GetMapping("/buscar")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<Page<CandidaturaEntity>> buscarConFiltros(
            @RequestParam(required = false) Long formacionPoliticaId,
            @RequestParam(required = false) Long circunscripcionId,
            @RequestParam(required = false) Long estadoCandidaturaId,
            @RequestParam(required = false) Long tipoCandidaturaId,
            Pageable pageable) {
        Page<CandidaturaEntity> candidaturas = candidaturaService.findWithFilters(
            formacionPoliticaId, circunscripcionId, estadoCandidaturaId, tipoCandidaturaId, pageable);
        return ResponseEntity.ok(candidaturas);
    }

    /**
     * Busca candidatura por formación política y circunscripción
     */
    @GetMapping("/formacion/{formacionId}/circunscripcion/{circunscripcionId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<CandidaturaEntity> getByFormacionYCircunscripcion(
            @PathVariable Long formacionId,
            @PathVariable Long circunscripcionId) {
        return candidaturaService.findByFormacionPoliticaAndCircunscripcion(formacionId, circunscripcionId)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Cuenta candidaturas por estado
     */
    @GetMapping("/contar/estado/{estadoId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<Long> contarPorEstado(@PathVariable Long estadoId) {
        Long count = candidaturaService.countByEstado(estadoId);
        return ResponseEntity.ok(count);
    }

    /**
     * Crea una nueva candidatura
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<CandidaturaEntity> createCandidatura(
            @RequestBody CandidaturaCreateRequest request,
            @AuthenticationPrincipal Jwt jwt) {
        try {
            String usuario = jwt.getClaimAsString("username");

            // Buscar las entidades por ID
            FormacionPoliticaEntity formacion = formacionPoliticaService.findById(request.getFormacionPolitica().getId())
                .orElseThrow(() -> new IllegalArgumentException("Formación política no encontrada"));

            CircunscripcionEntity circunscripcion = circunscripcionService.findById(request.getCircunscripcion().getId())
                .orElseThrow(() -> new IllegalArgumentException("Circunscripción no encontrada"));

            // Crear la candidatura
            CandidaturaEntity candidatura = new CandidaturaEntity(formacion, circunscripcion, request.getOrden(), usuario);

            CandidaturaEntity savedCandidatura = candidaturaService.save(candidatura);
            return new ResponseEntity<>(savedCandidatura, HttpStatus.CREATED);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        }
    }



    /**
     * Actualiza una candidatura
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<CandidaturaEntity> updateCandidatura(
            @PathVariable Long id,
            @RequestBody CandidaturaEntity candidaturaDetails) {
        return candidaturaService.findById(id)
                .map(existing -> {
                    // Actualizar campos permitidos
                    existing.setOrden(candidaturaDetails.getOrden());
                    existing.setFormacionPolitica(candidaturaDetails.getFormacionPolitica());
                    existing.setCircunscripcion(candidaturaDetails.getCircunscripcion());
                    existing.setEstadoCandidatura(candidaturaDetails.getEstadoCandidatura());
                    existing.setTipoCandidatura(candidaturaDetails.getTipoCandidatura());
                    // Convocatoria eliminada del modelo según Script 2

                    try {
                        CandidaturaEntity updated = candidaturaService.save(existing);
                        return ResponseEntity.ok(updated);
                    } catch (IllegalArgumentException e) {
                        return ResponseEntity.badRequest().<CandidaturaEntity>build();
                    }
                })
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Elimina una candidatura
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteCandidatura(@PathVariable Long id) {
        if (!candidaturaService.findById(id).isPresent()) {
            return ResponseEntity.notFound().build();
        }
        try {
            candidaturaService.deleteById(id);
            return ResponseEntity.noContent().build();
        } catch (IllegalStateException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Valida una candidatura
     */
    @PostMapping("/{id}/validar")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<CandidaturaEntity> validarCandidatura(
            @PathVariable Long id,
            @RequestParam String comentario,
            @AuthenticationPrincipal Jwt jwt) {
        try {
            String usuario = jwt.getClaimAsString("username");
            CandidaturaEntity candidatura = candidaturaService.validarCandidatura(id, usuario, comentario);
            return ResponseEntity.ok(candidatura);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Rechaza una candidatura
     */
    @PostMapping("/{id}/rechazar")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<CandidaturaEntity> rechazarCandidatura(
            @PathVariable Long id,
            @RequestParam String observacion,
            @AuthenticationPrincipal Jwt jwt) {
        try {
            String usuario = jwt.getClaimAsString("username");
            CandidaturaEntity candidatura = candidaturaService.rechazarCandidatura(id, usuario, observacion);
            return ResponseEntity.ok(candidatura);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Convierte una entidad CandidaturaEntity a DTO Candidatura
     */
    private Candidatura convertToDto(CandidaturaEntity entity) {
        Candidatura dto = new Candidatura();
        dto.setId(entity.getId());
        dto.setOrden(entity.getOrden());
        dto.setUsuarioCreacion(entity.getUsuarioCreacion());

        // Convertir fechas de LocalDate a OffsetDateTime
        if (entity.getFechaCreacion() != null) {
            dto.setFechaCreacion(entity.getFechaCreacion().atStartOfDay().atOffset(ZoneOffset.UTC));
        }
        if (entity.getFechaValidacion() != null) {
            dto.setFechaValidacion(entity.getFechaValidacion().atStartOfDay().atOffset(ZoneOffset.UTC));
        }

        dto.setUsuarioValidacion(entity.getUsuarioValidacion());
        dto.setComentarioValidacion(entity.getComentarioValidacion());
        dto.setObservacionRechazo(entity.getObservacionRechazo());

        // Convertir FormacionPolitica
        if (entity.getFormacionPolitica() != null) {
            FormacionPolitica formacionDto = new FormacionPolitica();
            formacionDto.setId(entity.getFormacionPolitica().getId());
            formacionDto.setNombre(entity.getFormacionPolitica().getNombre());
            formacionDto.setSiglas(entity.getFormacionPolitica().getSiglas());
            formacionDto.setCodigoInterno(entity.getFormacionPolitica().getCodigoInterno());
            dto.setFormacionPolitica(formacionDto);
        }

        // Convertir Circunscripcion
        if (entity.getCircunscripcion() != null) {
            Circunscripcion circunscripcionDto = new Circunscripcion();
            circunscripcionDto.setId(entity.getCircunscripcion().getId().intValue()); // Convertir Long a Integer
            circunscripcionDto.setNombre(entity.getCircunscripcion().getNombre());
            circunscripcionDto.setCodigo(entity.getCircunscripcion().getCodigoProvincia() != null ?
                entity.getCircunscripcion().getCodigoProvincia().toString() : null);
            // Los campos provincia y comunidadAutonoma no están en la entidad, los dejamos null por ahora
            dto.setCircunscripcion(circunscripcionDto);
        }

        return dto;
    }
}

