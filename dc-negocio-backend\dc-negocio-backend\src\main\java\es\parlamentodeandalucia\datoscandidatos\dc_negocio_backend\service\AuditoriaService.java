package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Service
public class AuditoriaService {

    private static final Logger logger = LoggerFactory.getLogger(AuditoriaService.class);
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * Registra una operación de creación
     */
    public void registrarCreacion(String entidad, Long id, String usuario, String detalles) {
        String mensaje = String.format("[CREACION] %s - Entidad: %s, ID: %s, Usuario: %s, Detalles: %s", 
                                     LocalDateTime.now().format(formatter), entidad, id, usuario, detalles);
        logger.info(mensaje);
    }

    /**
     * Registra una operación de actualización
     */
    public void registrarActualizacion(String entidad, Long id, String usuario, String detalles) {
        String mensaje = String.format("[ACTUALIZACION] %s - Entidad: %s, ID: %s, Usuario: %s, Detalles: %s", 
                                     LocalDateTime.now().format(formatter), entidad, id, usuario, detalles);
        logger.info(mensaje);
    }

    /**
     * Registra una operación de eliminación
     */
    public void registrarEliminacion(String entidad, Long id, String usuario, String motivo) {
        String mensaje = String.format("[ELIMINACION] %s - Entidad: %s, ID: %s, Usuario: %s, Motivo: %s", 
                                     LocalDateTime.now().format(formatter), entidad, id, usuario, motivo);
        logger.warn(mensaje);
    }

    /**
     * Registra una operación de validación
     */
    public void registrarValidacion(String entidad, Long id, String usuario, String estado, String comentario) {
        String mensaje = String.format("[VALIDACION] %s - Entidad: %s, ID: %s, Usuario: %s, Estado: %s, Comentario: %s", 
                                     LocalDateTime.now().format(formatter), entidad, id, usuario, estado, comentario);
        logger.info(mensaje);
    }

    /**
     * Registra una operación de importación
     */
    public void registrarImportacion(String usuario, String archivo, int candidaturasCreadas, int candidatosCreados, int errores) {
        String mensaje = String.format("[IMPORTACION] %s - Usuario: %s, Archivo: %s, Candidaturas: %d, Candidatos: %d, Errores: %d", 
                                     LocalDateTime.now().format(formatter), usuario, archivo, candidaturasCreadas, candidatosCreados, errores);
        if (errores > 0) {
            logger.warn(mensaje);
        } else {
            logger.info(mensaje);
        }
    }

    /**
     * Registra un error de validación
     */
    public void registrarErrorValidacion(String entidad, String usuario, String errores) {
        String mensaje = String.format("[ERROR_VALIDACION] %s - Entidad: %s, Usuario: %s, Errores: %s", 
                                     LocalDateTime.now().format(formatter), entidad, usuario, errores);
        logger.error(mensaje);
    }

    /**
     * Registra un acceso a datos sensibles
     */
    public void registrarAccesoSensible(String operacion, String usuario, String recurso, String ip) {
        String mensaje = String.format("[ACCESO_SENSIBLE] %s - Operacion: %s, Usuario: %s, Recurso: %s, IP: %s", 
                                     LocalDateTime.now().format(formatter), operacion, usuario, recurso, ip);
        logger.info(mensaje);
    }

    /**
     * Registra un intento de acceso no autorizado
     */
    public void registrarAccesoNoAutorizado(String usuario, String recurso, String ip, String motivo) {
        String mensaje = String.format("[ACCESO_DENEGADO] %s - Usuario: %s, Recurso: %s, IP: %s, Motivo: %s", 
                                     LocalDateTime.now().format(formatter), usuario, recurso, ip, motivo);
        logger.warn(mensaje);
    }

    /**
     * Registra cambios de estado
     */
    public void registrarCambioEstado(String entidad, Long id, String usuario, String estadoAnterior, String estadoNuevo, String motivo) {
        String mensaje = String.format("[CAMBIO_ESTADO] %s - Entidad: %s, ID: %s, Usuario: %s, Estado: %s -> %s, Motivo: %s", 
                                     LocalDateTime.now().format(formatter), entidad, id, usuario, estadoAnterior, estadoNuevo, motivo);
        logger.info(mensaje);
    }

    /**
     * Registra operaciones masivas
     */
    public void registrarOperacionMasiva(String operacion, String usuario, int registrosAfectados, String criterios) {
        String mensaje = String.format("[OPERACION_MASIVA] %s - Operacion: %s, Usuario: %s, Registros: %d, Criterios: %s", 
                                     LocalDateTime.now().format(formatter), operacion, usuario, registrosAfectados, criterios);
        logger.info(mensaje);
    }

    /**
     * Registra errores del sistema
     */
    public void registrarErrorSistema(String operacion, String usuario, String error, String stackTrace) {
        String mensaje = String.format("[ERROR_SISTEMA] %s - Operacion: %s, Usuario: %s, Error: %s", 
                                     LocalDateTime.now().format(formatter), operacion, usuario, error);
        logger.error(mensaje);
        
        if (stackTrace != null && !stackTrace.isEmpty()) {
            logger.error("StackTrace: {}", stackTrace);
        }
    }

    /**
     * Registra inicio y fin de sesión
     */
    public void registrarSesion(String evento, String usuario, String ip, String userAgent) {
        String mensaje = String.format("[SESION_%s] %s - Usuario: %s, IP: %s, UserAgent: %s", 
                                     evento, LocalDateTime.now().format(formatter), usuario, ip, userAgent);
        logger.info(mensaje);
    }

    /**
     * Registra consultas con filtros sensibles
     */
    public void registrarConsulta(String entidad, String usuario, String filtros, int resultados) {
        String mensaje = String.format("[CONSULTA] %s - Entidad: %s, Usuario: %s, Filtros: %s, Resultados: %d", 
                                     LocalDateTime.now().format(formatter), entidad, usuario, filtros, resultados);
        logger.debug(mensaje);
    }

    /**
     * Registra exportaciones de datos
     */
    public void registrarExportacion(String formato, String usuario, String entidad, int registros, String filtros) {
        String mensaje = String.format("[EXPORTACION] %s - Formato: %s, Usuario: %s, Entidad: %s, Registros: %d, Filtros: %s", 
                                     LocalDateTime.now().format(formatter), formato, usuario, entidad, registros, filtros);
        logger.info(mensaje);
    }

    /**
     * Registra configuraciones del sistema
     */
    public void registrarConfiguracion(String parametro, String valorAnterior, String valorNuevo, String usuario) {
        String mensaje = String.format("[CONFIGURACION] %s - Parametro: %s, Valor: %s -> %s, Usuario: %s", 
                                     LocalDateTime.now().format(formatter), parametro, valorAnterior, valorNuevo, usuario);
        logger.info(mensaje);
    }
}
