package es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.repository;

import es.parlamentodeandalucia.datoscandidatos.dc_negocio_backend.entity.CircunscripcionEntity;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface CircunscripcionRepository extends JpaRepository<CircunscripcionEntity, Long> {

    Optional<CircunscripcionEntity> findByNombre(String nombre);

    // NUEVO MÉTODO: Carga las candidaturas junto con la circunscripción
    // Esto es crucial para que .isEmpty() funcione con relaciones LAZY
    @Query("SELECT c FROM CircunscripcionEntity c LEFT JOIN FETCH c.candidaturas ORDER BY c.codigoProvincia ASC")
    List<CircunscripcionEntity> findAllConCandidaturas();

    @Query("SELECT c FROM CircunscripcionEntity c LEFT JOIN FETCH c.candidaturas WHERE c.id = :id")
    Optional<CircunscripcionEntity> findByIdConCandidaturas(Long id);
}
