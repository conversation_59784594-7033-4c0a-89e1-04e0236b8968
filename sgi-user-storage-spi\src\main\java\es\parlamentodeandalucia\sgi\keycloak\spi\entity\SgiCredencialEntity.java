package es.parlamentodeandalucia.sgi.keycloak.spi.entity;

import jakarta.persistence.*;
import java.time.LocalDate;

/**
 * Entidad para credenciales del SGI - Versión SPI
 * Mapea con la tabla sgi_t_credencial
 * MAPEA CON KEYCLOAK USER STORAGE SPI
 */
@Entity
@Table(name = "sgi_t_credencial")
@NamedQueries({
    @NamedQuery(name = "SgiCredencialEntity.findByUsername",
                query = "SELECT c FROM SgiCredencialEntity c WHERE c.username = :username"),
    @NamedQuery(name = "SgiCredencialEntity.findByIdentificador",
                query = "SELECT c FROM SgiCredencialEntity c JOIN c.identidad i WHERE i.identificador = :identificador"),
    @NamedQuery(name = "SgiCredencialEntity.findActiveUsers",
                query = "SELECT c FROM SgiCredencialEntity c JOIN c.estado e WHERE e.valor = 'ACTIVO'"),
    @NamedQuery(name = "SgiCredencialEntity.countActiveUsers",
                query = "SELECT COUNT(c) FROM SgiCredencialEntity c JOIN c.estado e WHERE e.valor = 'ACTIVO'")
})
public class SgiCredencialEntity {

    @Id
    @Column(name = "sgi_id_credencial")
    private Long id;

    @Column(name = "sgi_tx_username", nullable = false, unique = true)
    private String username;

    @Column(name = "sgi_tx_password")
    private String password;

    @Column(name = "sgi_fh_alta")
    private LocalDate fechaAlta;

    @Column(name = "sgi_fh_baja")
    private LocalDate fechaBaja;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "sgi_fk_estado")
    private SgiEstadoEntity estado;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "sgi_fk_identidad")
    private SgiIdentidadEntity identidad;

    // Constructores
    public SgiCredencialEntity() {}

    public SgiCredencialEntity(String username, String password, SgiIdentidadEntity identidad, SgiEstadoEntity estado) {
        this.username = username;
        this.password = password;
        this.identidad = identidad;
        this.estado = estado;
        this.fechaAlta = LocalDate.now();
    }

    // Métodos de utilidad
    public boolean isAccountActive() {
        return estado != null && "ACTIVO".equals(estado.getValor());
    }

    public boolean isAccountLocked() {
        return estado != null && "BLOQUEADO".equals(estado.getValor());
    }

    // Métodos para obtener datos de Keycloak desde la identidad
    public String getEmail() {
        return identidad != null ? identidad.getEmail() : null;
    }

    // Métodos para gestión de login
    public void updateLastLogin() {
        // No hay campo fechaUltimoAcceso en la BD actual
        // Se podría implementar en el futuro si se agrega el campo
    }

    public void incrementFailedLoginAttempts() {
        // No hay campo intentosFallidos en la BD actual
        // Se podría implementar en el futuro si se agrega el campo
    }

    public Boolean getAccountLocked() {
        // Verificar si el estado es BLOQUEADO
        return estado != null && "BLOQUEADO".equals(estado.getValor());
    }

    public Integer getFailedLoginAttempts() {
        // No hay campo intentosFallidos en la BD actual
        return 0;
    }

    // Getters y Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }

    public String getPassword() { return password; }
    public void setPassword(String password) { this.password = password; }

    public LocalDate getFechaAlta() { return fechaAlta; }
    public void setFechaAlta(LocalDate fechaAlta) { this.fechaAlta = fechaAlta; }

    public LocalDate getFechaBaja() { return fechaBaja; }
    public void setFechaBaja(LocalDate fechaBaja) { this.fechaBaja = fechaBaja; }

    public SgiEstadoEntity getEstado() { return estado; }
    public void setEstado(SgiEstadoEntity estado) { this.estado = estado; }

    public SgiIdentidadEntity getIdentidad() { return identidad; }
    public void setIdentidad(SgiIdentidadEntity identidad) { this.identidad = identidad; }

    @PrePersist
    protected void onCreate() {
        if (fechaAlta == null) {
            fechaAlta = LocalDate.now();
        }
    }
}
