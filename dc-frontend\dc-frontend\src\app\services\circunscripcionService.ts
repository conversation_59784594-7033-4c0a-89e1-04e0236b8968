import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

import { environment } from '../../environments/environment';

export interface Circunscripcion {
  id: number;
  codigoProvincia: string; 
  nombre: string;
  candidaturaActiva?: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class CircunscripcionesService {
  private readonly apiUrl = environment.apiUrl + '/circunscripciones';

  constructor(private http: HttpClient) {}

  getAll(): Observable<Circunscripcion[]> {
    return this.http.get<Circunscripcion[]>(this.apiUrl);
  }

  getById(id: number): Observable<Circunscripcion> {
    return this.http.get<Circunscripcion>(`${this.apiUrl}/${id}`);
  }

  create(circunscripcion: { codigoProvincia: string; nombre: string }): Observable<Circunscripcion> {
    return this.http.post<Circunscripcion>(this.apiUrl, circunscripcion);
  }

  update(id: number, circunscripcion: Circunscripcion): Observable<Circunscripcion> {
    return this.http.put<Circunscripcion>(`${this.apiUrl}/${id}`, circunscripcion);
  }

  delete(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }
}
