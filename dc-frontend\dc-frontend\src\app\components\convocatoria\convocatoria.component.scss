::ng-deep {
    .p-column-filter-overlay {
        z-index: 1100 !important;
    }

    .p-progressbar {
        height: .5rem;
        background-color: #D8DADC;

        .p-progressbar-value {
            background-color: #607D8B;
        }
    }
}

.title-section {
    margin: 0 auto 20px auto;
    max-width: 1400px;
    width: calc(100% - 40px);
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: nowrap;
    width: 100%;
}

.title-section .back-button {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: transparent;
    color: #333;
    text-decoration: none;
    padding: 0;
    flex-shrink: 0;
}

.title-section .back-button svg {
    width: 24px;
    height: 24px;
    color: #555;
}

.title-section .page-main-title {
    font-size: 28px;
    font-weight: 600;
    color: #333;
    margin-left: 5px;
    white-space: nowrap;
    flex-shrink: 0;
}

.title-section .header-actions {
    display: flex;
    gap: 10px;
    margin-left: auto;
    flex-wrap: nowrap;
    flex-shrink: 0;
    background-color: #C2E038;
    border-radius: 10px;
}

.breadcrumbs {
    margin: 0 auto;
    max-width: 1400px;
    width: 100%;
    font-size: 14px;
    color: #666;
    margin-top: 0;
    margin-bottom: 0;
    display: flex;
    align-items: center;
}

.breadcrumbs .separator {
    margin: 0 5px;
    color: #999;
}

.breadcrumbs strong {
    color: #333;
    font-weight: 600;
}

.page-header {
    width: 100%;
    margin: 20px auto 0 auto;
    padding: 0 20px;
    box-sizing: border-box;
}

/* Filtros y acciones */
.filter-actions-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 10px 10px;
    flex-wrap: wrap;
    gap: 15px;
}

/* Dropdown */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-toggle {
    display: flex;
    align-items: center;
    gap: 5px;
}

.dropdown-menu {
    display: none;
    position: absolute;
    background-color: #fff;
    min-width: 160px;
    box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.2);
    z-index: 1;
    border-radius: 6px;
    overflow: auto;
    top: 100%;
    left: 0;
    margin-top: 5px;
}

.dropdown-item {
    color: #333;
    padding: 12px 16px;
    text-decoration: none;
    display: block;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.dropdown-item:hover {
    background-color: #f1f1f1;
}

.dropdown:hover .dropdown-menu {
    display: block;
}

.layout-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh; // Asegura que el layout ocupe al menos toda la altura de la vista
}

.header-fixed {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    height: 60px;
}

.footer-fixed {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    background-color: #fff;
    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
    height: 50px;
}

.page-wrapper {
    flex-grow: 1;
    /* Permite que ocupe todo el espacio vertical disponible */
    overflow-y: auto;
    /* Hace que el contenido interno sea scrollable si excede el tamaño */
    background-color: #f8f8f8;
    /* Color de fondo del wrapper */

    /* Importante: Añadir padding para compensar el header y footer fijos */
    padding-top: 60px;
    /* Altura del header */
    padding-bottom: 50px;
    /* Altura del footer */

    /* Añade padding lateral para el contenido principal, pero no uses margin auto aquí */
    padding-left: 20px;
    padding-right: 20px;

    display: flex;
    /* Usamos flexbox para organizar el contenido verticalmente */
    flex-direction: column;
    gap: 20px;
    /* Espacio entre las secciones principales (breadcrumbs, title, form container) */
}

.main-content-area {
    flex-grow: 1; // Permite que esta área ocupe todo el espacio disponible entre el header y el footer
    padding: 20px; // Padding general para el contenido principal
    background-color: #f8f8f8; // Un color de fondo suave para la página
}

.page-header {
    display: flex;
    justify-content: space-between; // Alinea el título a la izquierda y los botones a la derecha
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee; // Línea divisoria debajo del título
}

.title-section {
    margin: 0 auto;
    /* Centra horizontalmente el título y los botones */
    max-width: 1400px;
    /* Tu ancho máximo deseado */
    width: 100%;
    /* Ocupa todo el ancho disponible hasta el max-width */
    display: flex;
    align-items: center;
    justify-content: space-between;
    /* Alinea los elementos a los extremos */
    flex-wrap: nowrap;
    // margin-bottom: 20px; // Ya lo tienes en page-wrapper gap
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
    // La imagen_853240.png muestra el botón "Modificar información"
    // dentro de los header-actions-right, no directamente en title-section header-actions.
    // Esto se corrige más abajo.
}

.page-title {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    // La flecha hacia atrás que se ve en la imagen se puede añadir como un icono pi-arrow-left
    // dentro de un span junto al texto del título, o con un pseudo-elemento.
    // Para simplificar, de momento no la incluyo aquí, pero es un detalle estético.
}

.header-actions-right {
    display: flex;
    justify-content: flex-end;
    gap: 10px; // Espacio entre los botones "Consultar histórico" y "Modificar información"
}

.global-actions {
    display: flex;
    gap: 10px;
    margin-bottom: 20px; // Espacio entre los botones "Añadir" / "Limpiar Filtros" y el formulario
}

/* Contenedor del formulario */
.convocatoria-form-container {
    background-color: #fff;
    padding: 25px 30px; 
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin: 0 auto; 
    max-width: 1400px; 
    width: 100%; 
    display: flex;
    flex-direction: column;
    gap: 20px
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr; // Dos columnas de igual ancho
    gap: 25px 50px; // Espacio vertical y horizontal entre los campos del formulario
}

.form-column {
    display: flex;
    flex-direction: column;
    gap: 20px; // Espacio entre los campos dentro de una misma columna
}

.form-field {
    display: flex;
    flex-direction: column;
    gap: 5px; // Espacio entre el label y el input/componente
    margin-bottom: 15px;
}

.form-field label {
    font-weight: 600;
    color: black;
    font-size: 14px;
}

.form-field.horizontal {
    flex-direction: row; 
    align-items: center;
    gap: 10px;
}

.form-field.horizontal label {
    width: 220px; 
    flex-shrink: 0; 
    text-align: right; 
    padding-right: 5px; 
}

/* Opcional: Para que el p-datePicker ocupe el resto del espacio disponible */
.form-field.horizontal p-datePicker {
    flex-grow: 1; 
    max-width: 250px; 
}

/* Estilos específicos para los componentes de PrimeNG */
.p-calendar {
    width: 100%; // Asegura que el p-calendar ocupe todo el ancho disponible
}

/* Estilo para el campo simulado de "Logotipo del proceso" */
.fake-file-input {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 0.75rem 0.75rem; // Padding similar al de los inputs de PrimeNG
    border: 1px solid #ced4da; // Borde similar al de los inputs
    border-radius: 6px;
    background-color: #C2E038; // Fondo gris claro para indicar que es de solo lectura/simulado
    color: #C2E038; // Color de texto
    cursor: default; // Cursor por defecto para que no parezca un campo editable

    .icon-plus-green {
        font-size: 16px;
        color: #282F19;// Color verde para el icono de añadir
        
    }

    .file-name {
        // Estilos para el nombre del archivo si es necesario
    }
}

/* Tus estilos de botones existentes */
.p-button-green {
    background-color: #C2E038 !important;
    border-color: #C2E038 !important;
    color: #fff !important;
}

.p-button-green:hover {
    background-color: #C2E038 !important;
    border-color: #C2E038 !important;
}

.p-button-secondary {
    background-color: #6c757d !important;
    border-color: #6c757d !important;
    color: #fff !important;
}

.p-button-secondary:hover {
    background-color: #5a6268 !important;
    border-color: #5a6268 !important;
}

.p-button-success {
    background-color: #C2E038 !important; // Usa el color verde de tu tema
    border-color: #C2E038 !important;
    color: #fff !important;

}

.p-button-success:hover {
    background-color: #C2E038 !important;
    border-color: #C2E038 !important;
}

.btn-text {
    background: none !important;
    border: none !important;
    color: #6c757d !important; // Un color gris para texto de botón
    box-shadow: none !important;

    .pi {
        margin-right: 5px;
    }
}

.btn-text:hover {
    color: #495057 !important;
    background-color: #f8f9fa !important;
}

// Diseño modal.

:host ::ng-deep .convocatoria-dialog .p-dialog {
  border-radius: 12px;
}

:host ::ng-deep .convocatoria-dialog .p-dialog-header {
  font-size: 18px;
  font-weight: 600;
  color: #2d2d2d;
}

:host ::ng-deep .convocatoria-dialog .p-dialog-content {
  font-size: 14px;
}

:host ::ng-deep .convocatoria-dialog .p-dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

:host ::ng-deep .convocatoria-dialog .p-button.p-button-success {
  padding: 6px 20px;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  background-color: #C2E038;
  color: #000;
}

:host ::ng-deep .convocatoria-dialog .p-button.p-button-success:hover {
  background-color: #A8CB5B;
}

:host ::ng-deep .convocatoria-dialog .p-button.p-button-secondary {
  padding: 6px 20px;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  background-color: #F3F4F6;
  color: #000;
}

:host ::ng-deep .convocatoria-dialog .p-button.p-button-secondary:hover {
  background-color: #FECACA;
}

